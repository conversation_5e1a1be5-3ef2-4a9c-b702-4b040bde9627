{"formatVersion": 1, "database": {"version": 2, "identityHash": "d8a2889fafce85cfd2fda41d20977400", "entities": [{"tableName": "game_state", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `board` TEXT NOT NULL, `score` INTEGER NOT NULL, `moves` INTEGER NOT NULL, `isGameOver` INTEGER NOT NULL, `hasWon` INTEGER NOT NULL, `lastMoveType` TEXT, `timestamp` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "board", "columnName": "board", "affinity": "TEXT", "notNull": true}, {"fieldPath": "score", "columnName": "score", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "moves", "columnName": "moves", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isGameOver", "columnName": "isGameOver", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "hasWon", "columnName": "hasWon", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lastMoveType", "columnName": "lastMoveType", "affinity": "TEXT"}, {"fieldPath": "timestamp", "columnName": "timestamp", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}}, {"tableName": "game_stats", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `highScore` INTEGER NOT NULL, `gamesPlayed` INTEGER NOT NULL, `gamesWon` INTEGER NOT NULL, `totalMoves` INTEGER NOT NULL, `highestTile` INTEGER NOT NULL, `bestTile` INTEGER NOT NULL, `averageScore` REAL NOT NULL, `bestTime` INTEGER NOT NULL, `totalPlayTime` INTEGER NOT NULL, `totalMerges` INTEGER NOT NULL, `longestWinStreak` INTEGER NOT NULL, `currentWinStreak` INTEGER NOT NULL, `lastUpdated` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "highScore", "columnName": "highScore", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "gamesPlayed", "columnName": "gamesPlayed", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "gamesWon", "columnName": "gamesWon", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "totalMoves", "columnName": "totalMoves", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "highestTile", "columnName": "highestTile", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "bestTile", "columnName": "bestTile", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "averageScore", "columnName": "averageScore", "affinity": "REAL", "notNull": true}, {"fieldPath": "bestTime", "columnName": "bestTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "totalPlayTime", "columnName": "totalPlayTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "totalMerges", "columnName": "totalMerges", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "longestWinStreak", "columnName": "longestWinStreak", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "currentWinStreak", "columnName": "currentWinStreak", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lastUpdated", "columnName": "lastUpdated", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}}], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'd8a2889fafce85cfd2fda41d20977400')"]}}