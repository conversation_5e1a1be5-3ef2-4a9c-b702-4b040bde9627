<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="Theme.TriSwipe" parent="Theme.SplashScreen">
        <item name="windowSplashScreenBackground">@color/light_background</item>
        <item name="windowSplashScreenAnimatedIcon">@android:drawable/sym_def_app_icon</item>
        <item name="postSplashScreenTheme">@style/Theme.TriSwipe.Main</item>
    </style>
    
    <style name="Theme.TriSwipe.Main" parent="android:Theme.Material.Light.NoActionBar">
        <item name="android:windowBackground">@color/light_background</item>
        <item name="android:statusBarColor">@color/light_background</item>
        <item name="android:windowLightStatusBar">true</item>
    </style>
</resources>