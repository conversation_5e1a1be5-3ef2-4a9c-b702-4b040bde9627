package com.frageo.triswipe.billing

import android.app.Activity
import android.content.Context
import com.android.billingclient.api.*
import com.frageo.triswipe.premium.PremiumStatusManager
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class SimpleBillingManager @Inject constructor(
    @param:ApplicationContext private val context: Context,
    private val premiumStatusManager: PremiumStatusManager
) : BillingManager, PurchasesUpdatedListener, BillingClientStateListener {

    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    companion object {
        const val PREMIUM_PRODUCT_ID = "premium_upgrade"
        private const val PREMIUM_PRICE = "$1.99"
    }

    private var billingClient: BillingClient? = null
    private var billingInitializationFailed = false

    private val _billingState = MutableStateFlow(BillingState.DISCONNECTED)
    override val billingState: StateFlow<BillingState> = _billingState.asStateFlow()

    private val _purchaseState = MutableStateFlow<PurchaseState>(PurchaseState.Idle)
    override val purchaseState: StateFlow<PurchaseState> = _purchaseState.asStateFlow()

    init {
        initializeBillingClient()
    }

    private fun initializeBillingClient() {
        try {
            billingClient = BillingClient.newBuilder(context)
                .setListener(this)
                .enablePendingPurchases(PendingPurchasesParams.newBuilder().enableOneTimeProducts().build())
                .build()
            
            billingInitializationFailed = false
            startConnection()
            
        } catch (e: Exception) {
            billingInitializationFailed = true
            billingClient = null
            _billingState.value = BillingState.UNAVAILABLE
            
        }
    }

    private fun startConnection() {
        if (billingInitializationFailed || billingClient == null) {
            _billingState.value = BillingState.UNAVAILABLE
            return
        }
        
        try {
            _billingState.value = BillingState.CONNECTING
            billingClient!!.startConnection(this)
        } catch (e: Exception) {
            _billingState.value = BillingState.ERROR
        }
    }

    override fun onBillingSetupFinished(billingResult: BillingResult) {
        when (billingResult.responseCode) {
            BillingClient.BillingResponseCode.OK -> {
                _billingState.value = BillingState.CONNECTED
                queryExistingPurchases()
            }
            BillingClient.BillingResponseCode.BILLING_UNAVAILABLE -> {
                _billingState.value = BillingState.UNAVAILABLE
            }
            BillingClient.BillingResponseCode.FEATURE_NOT_SUPPORTED -> {
                _billingState.value = BillingState.UNAVAILABLE
            }
            else -> {
                _billingState.value = BillingState.ERROR
            }
        }
    }

    override fun onBillingServiceDisconnected() {
        _billingState.value = BillingState.DISCONNECTED
    }

    private fun queryExistingPurchases() {
        if (billingInitializationFailed || billingClient == null) {
            return
        }
        
        try {
            val params = QueryPurchasesParams.newBuilder()
                .setProductType(BillingClient.ProductType.INAPP)
                .build()

            billingClient!!.queryPurchasesAsync(params) { billingResult, purchases ->
                if (billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
                    for (purchase in purchases) {
                        handlePurchase(purchase)
                    }
                } else {
                }
            }
        } catch (e: Exception) {
            // Silently fail - purchase query errors are non-critical
        }
    }

    override fun launchPurchaseFlow(activity: Activity) {
        when (_billingState.value) {
            BillingState.CONNECTED -> {
                // Proceed with purchase flow
            }
            BillingState.UNAVAILABLE -> {
                // Continue with simulation for testing
            }
            BillingState.CONNECTING -> {
                _purchaseState.value = PurchaseState.Error("Billing service still connecting. Please try again.")
                return
            }
            else -> {
                _purchaseState.value = PurchaseState.Error("Billing service not available")
                return
            }
        }

        _purchaseState.value = PurchaseState.Purchasing

        // For now, simulate a successful purchase for testing
        // In production, you would query product details first and launch the actual billing flow
        
        // Simulate a delay and then success
        scope.launch {
            kotlinx.coroutines.delay(2000)
            _purchaseState.value = PurchaseState.Success
            setPremiumUser(true)
        }
    }

    override fun onPurchasesUpdated(billingResult: BillingResult, purchases: MutableList<Purchase>?) {
        when (billingResult.responseCode) {
            BillingClient.BillingResponseCode.OK -> {
                purchases?.forEach { purchase ->
                    handlePurchase(purchase)
                }
            }
            BillingClient.BillingResponseCode.USER_CANCELED -> {
                _purchaseState.value = PurchaseState.Cancelled
            }
            BillingClient.BillingResponseCode.ITEM_ALREADY_OWNED -> {
                _purchaseState.value = PurchaseState.Success
                setPremiumUser(true)
            }
            else -> {
                _purchaseState.value = PurchaseState.Error(billingResult.debugMessage ?: "Purchase failed")
            }
        }
    }

    private fun handlePurchase(purchase: Purchase) {
        if (purchase.products.contains(PREMIUM_PRODUCT_ID)) {
            when (purchase.purchaseState) {
                Purchase.PurchaseState.PURCHASED -> {
                    if (verifyPurchase(purchase)) {
                        _purchaseState.value = PurchaseState.Success
                        setPremiumUser(true)
                        
                        if (!purchase.isAcknowledged) {
                            acknowledgePurchase(purchase)
                        }
                    } else {
                        _purchaseState.value = PurchaseState.Error("Purchase verification failed")
                    }
                }
                Purchase.PurchaseState.PENDING -> {
                    _purchaseState.value = PurchaseState.Pending
                    // Start periodic checking for pending purchase updates
                    startPendingPurchasePolling(purchase)
                }
                else -> {
                }
            }
        }
    }

    private fun verifyPurchase(purchase: Purchase): Boolean {
        // Basic client-side verification
        if (purchase.purchaseToken.isBlank()) {
            return false
        }
        
        if (!purchase.products.contains(PREMIUM_PRODUCT_ID)) {
            return false
        }
        
        if (purchase.purchaseState != Purchase.PurchaseState.PURCHASED) {
            return false
        }
        
        return true
    }

    private fun acknowledgePurchase(purchase: Purchase) {
        if (billingInitializationFailed || billingClient == null) {
            return
        }
        
        try {
            val acknowledgePurchaseParams = AcknowledgePurchaseParams.newBuilder()
                .setPurchaseToken(purchase.purchaseToken)
                .build()
            
            billingClient!!.acknowledgePurchase(acknowledgePurchaseParams) { billingResult ->
            }
        } catch (e: Exception) {
            // Silently fail - acknowledgment errors are handled by Play Store
        }
    }

    private fun setPremiumUser(isPremium: Boolean) {
        scope.launch {
            try {
                premiumStatusManager.setPremiumStatus(isPremium)
            } catch (e: Exception) {
                // Silently fail - premium status update is non-critical
            }
        }
    }

    override fun getPremiumPrice(): String {
        return PREMIUM_PRICE
    }

    override fun isConnected(): Boolean {
        return _billingState.value == BillingState.CONNECTED
    }

    override fun disconnect() {
        try {
            if (billingClient != null && !billingInitializationFailed) {
                billingClient!!.endConnection()
            } else {
            }
            _billingState.value = BillingState.DISCONNECTED
        } catch (e: Exception) {
            _billingState.value = BillingState.DISCONNECTED
        }
    }

    override fun isBillingAvailable(): Boolean {
        return _billingState.value == BillingState.CONNECTED
    }

    override fun canMakePurchases(): Boolean {
        return _billingState.value in listOf(BillingState.CONNECTED, BillingState.UNAVAILABLE)
    }

    override fun retryBillingInitialization() {
        if (billingInitializationFailed) {
            initializeBillingClient()
        } else {
            if (_billingState.value != BillingState.CONNECTED) {
                startConnection()
            }
        }
    }

    override fun getBillingFailureReason(): String? {
        return if (billingInitializationFailed) {
            "BillingClient initialization failed - device may not support Google Play Billing"
        } else {
            null
        }
    }

    private fun startPendingPurchasePolling(pendingPurchase: Purchase) {
        scope.launch {
            repeat(30) { // Poll for up to 5 minutes (30 * 10 seconds)
                kotlinx.coroutines.delay(10_000) // Check every 10 seconds
                
                if (_purchaseState.value !is PurchaseState.Pending) {
                    // Purchase state changed, stop polling
                    return@launch
                }
                
                queryExistingPurchases()
            }
            
            // If still pending after 5 minutes, notify user
            if (_purchaseState.value is PurchaseState.Pending) {
                _purchaseState.value = PurchaseState.Error("Purchase is taking longer than expected. Please check your payment method.")
            }
        }
    }
}

enum class BillingState {
    DISCONNECTED,
    CONNECTING,
    CONNECTED,
    UNAVAILABLE,
    ERROR
}

sealed class PurchaseState {
    object Idle : PurchaseState()
    object Purchasing : PurchaseState()
    object Pending : PurchaseState()
    object Success : PurchaseState()
    object Cancelled : PurchaseState()
    data class Error(val message: String) : PurchaseState()
}