package com.frageo.triswipe.billing

import android.app.Activity
import kotlinx.coroutines.flow.StateFlow

/**
 * Interface for managing in-app billing operations.
 * Provides abstraction for different billing implementations (Google Play, App Store, etc.).
 */
interface BillingManager {
    
    /**
     * Observable billing connection state
     */
    val billingState: StateFlow<BillingState>
    
    /**
     * Observable purchase state for tracking purchase flow
     */
    val purchaseState: StateFlow<PurchaseState>
    
    /**
     * Launch the purchase flow for premium upgrade
     * @param activity The activity context for launching the billing flow
     */
    fun launchPurchaseFlow(activity: Activity)
    
    /**
     * Get the localized price for the premium upgrade
     * @return Formatted price string (e.g., "$1.99")
     */
    fun getPremiumPrice(): String
    
    /**
     * Check if billing service is connected and ready
     * @return true if connected, false otherwise
     */
    fun isConnected(): Boolean
    
    /**
     * Check if billing service is available and can make purchases
     * @return true if purchases are possible, false otherwise
     */
    fun canMakePurchases(): Boolean
    
    /**
     * Check if billing service is available (may not be connected)
     * @return true if billing is available, false otherwise
     */
    fun isBillingAvailable(): Boolean
    
    /**
     * Retry billing initialization if it previously failed
     */
    fun retryBillingInitialization()
    
    /**
     * Get the reason for billing failure if any
     * @return Error message or null if no failure
     */
    fun getBillingFailureReason(): String?
    
    /**
     * Disconnect from billing service
     */
    fun disconnect()
}