package com.frageo.triswipe.billing

import android.content.Context
import com.frageo.triswipe.premium.PremiumStatusManager
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object BillingModule {

    @Provides
    @Singleton
    fun provideBillingManager(
        @ApplicationContext context: Context,
        premiumStatusManager: PremiumStatusManager
    ): BillingManager {
        return SimpleBillingManager(context, premiumStatusManager)
    }
}