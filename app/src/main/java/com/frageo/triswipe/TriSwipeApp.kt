package com.frageo.triswipe

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.compose.rememberNavController
import com.frageo.triswipe.navigation.TriSwipeNavGraph
import com.frageo.triswipe.ui.theme.TriSwipeTheme
import com.frageo.triswipe.viewmodel.GameViewModel

@Composable
fun TriSwipeApp() {
    val gameViewModel: GameViewModel = viewModel()
    val themeMode by gameViewModel.getThemeModeFlow().collectAsStateWithLifecycle()
    
    TriSwipeTheme(themeMode = themeMode) {
        Surface(
            modifier = Modifier.fillMaxSize(),
            color = MaterialTheme.colorScheme.background
        ) {
            val navController = rememberNavController()
            TriSwipeNavGraph(
                navController = navController,
                modifier = Modifier.fillMaxSize()
            )
        }
    }
}