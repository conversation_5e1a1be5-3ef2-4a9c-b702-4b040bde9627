package com.frageo.triswipe.di

import com.frageo.triswipe.data.models.managers.UndoManager
import com.frageo.triswipe.data.models.managers.UndoManagerImpl
import com.frageo.triswipe.data.models.managers.MoveAnalyzer
import com.frageo.triswipe.data.models.managers.MoveAnalyzerImpl
import com.frageo.triswipe.data.models.managers.GameValidator
import com.frageo.triswipe.data.models.managers.GameValidatorImpl
import com.frageo.triswipe.data.models.managers.MoveExecutor
import com.frageo.triswipe.data.models.managers.MoveExecutorImpl
import com.frageo.triswipe.data.models.managers.SwapModeManager
import com.frageo.triswipe.data.models.MergeDetector
import com.frageo.triswipe.data.models.MergeExecutor
import com.frageo.triswipe.data.repository.UserPreferencesRepository
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Hilt module for providing game manager dependencies.
 * 
 * Created as part of GameEngine refactoring to provide specialized managers
 * for undo, move analysis, validation, and execution.
 */
@Module
@InstallIn(SingletonComponent::class)
object ManagerModule {
    
    @Provides
    @Singleton
    fun provideMergeDetector(): MergeDetector {
        return MergeDetector()
    }
    
    @Provides
    @Singleton
    fun provideMergeExecutor(): MergeExecutor {
        return MergeExecutor()
    }
    
    @Provides
    @Singleton
    fun provideUndoManager(): UndoManager {
        return UndoManagerImpl()
    }
    
    @Provides
    @Singleton
    fun provideGameValidator(
        mergeDetector: MergeDetector
    ): GameValidator {
        return GameValidatorImpl(mergeDetector)
    }
    
    @Provides
    @Singleton
    fun provideMoveAnalyzer(
        mergeDetector: MergeDetector,
        mergeExecutor: MergeExecutor
    ): MoveAnalyzer {
        return MoveAnalyzerImpl(mergeDetector, mergeExecutor)
    }
    
    @Provides
    @Singleton
    fun provideMoveExecutor(
        mergeDetector: MergeDetector,
        mergeExecutor: MergeExecutor,
        gameValidator: GameValidator
    ): MoveExecutor {
        return MoveExecutorImpl(mergeDetector, mergeExecutor, gameValidator)
    }
    
    @Provides
    @Singleton
    fun provideSwapModeManager(
        userPreferencesRepository: UserPreferencesRepository,
        mergeDetector: MergeDetector
    ): SwapModeManager {
        return SwapModeManager(userPreferencesRepository, mergeDetector)
    }
}