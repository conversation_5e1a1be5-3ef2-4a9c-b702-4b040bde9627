package com.frageo.triswipe.di

import android.content.Context
import com.frageo.triswipe.data.database.GameDao
import com.frageo.triswipe.data.database.GameDatabase
import com.frageo.triswipe.data.preferences.GamePreferences
import com.frageo.triswipe.data.repository.GameStatisticsManager
import com.frageo.triswipe.data.repository.GameStateRepository
import com.frageo.triswipe.data.repository.GameStateRepositoryImpl
import com.frageo.triswipe.data.repository.GameStatisticsRepository
import com.frageo.triswipe.data.repository.GameStatisticsRepositoryImpl
import com.frageo.triswipe.data.repository.UserPreferencesRepository
import com.frageo.triswipe.data.repository.UserPreferencesRepositoryImpl
import com.frageo.triswipe.data.repository.MonetizationRepository
import com.frageo.triswipe.data.repository.MonetizationRepositoryImpl
import com.frageo.triswipe.data.models.GameEngine
import com.frageo.triswipe.data.models.GameEngineInterface
import com.frageo.triswipe.data.models.MergeDetector
import com.frageo.triswipe.data.models.MergeExecutor
import com.frageo.triswipe.animation.AnimationController
import com.frageo.triswipe.game.GameStateManager
import com.frageo.triswipe.data.commands.CommandManager
import com.frageo.triswipe.premium.PremiumStatusManager
import com.frageo.triswipe.data.events.StatisticsEventProcessor
import com.frageo.triswipe.di.ApplicationScope
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {
    
    @Provides
    @Singleton
    fun provideGameDatabase(@ApplicationContext context: Context): GameDatabase {
        return GameDatabase.getDatabase(context)
    }
    
    @Provides
    fun provideGameDao(database: GameDatabase): GameDao {
        return database.gameDao()
    }
    
    @Provides
    @Singleton
    fun provideGamePreferences(@ApplicationContext context: Context): GamePreferences {
        return GamePreferences(context)
    }
    
    
    @Provides
    @Singleton
    fun provideGameStatisticsManager(
        eventProcessor: StatisticsEventProcessor,
        monetizationRepository: MonetizationRepository
    ): GameStatisticsManager {
        return GameStatisticsManager(eventProcessor, monetizationRepository)
    }
    
    // New specialized repositories
    @Provides
    @Singleton
    fun provideGameStateRepository(
        gameDao: GameDao
    ): GameStateRepository {
        return GameStateRepositoryImpl(gameDao)
    }
    
    @Provides
    @Singleton
    fun provideGameStatisticsRepository(
        gameDao: GameDao,
        gamePreferences: GamePreferences
    ): GameStatisticsRepository {
        return GameStatisticsRepositoryImpl(gameDao, gamePreferences)
    }
    
    @Provides
    @Singleton
    fun provideUserPreferencesRepository(
        gamePreferences: GamePreferences
    ): UserPreferencesRepository {
        return UserPreferencesRepositoryImpl(gamePreferences)
    }
    
    @Provides
    @Singleton
    fun provideMonetizationRepository(
        gamePreferences: GamePreferences
    ): MonetizationRepository {
        return MonetizationRepositoryImpl(gamePreferences)
    }
    
    @Provides
    @Singleton
    fun provideDatabaseCoroutineScope(): CoroutineScope {
        return CoroutineScope(SupervisorJob() + Dispatchers.IO)
    }
    
    @Provides
    @Singleton
    fun provideGameEngine(
        undoManager: com.frageo.triswipe.data.models.managers.UndoManager,
        moveAnalyzer: com.frageo.triswipe.data.models.managers.MoveAnalyzer,
        gameValidator: com.frageo.triswipe.data.models.managers.GameValidator,
        moveExecutor: com.frageo.triswipe.data.models.managers.MoveExecutor,
        mergeDetector: MergeDetector,
        mergeExecutor: MergeExecutor,
        statisticsManager: GameStatisticsManager,
        swapModeManager: com.frageo.triswipe.data.models.managers.SwapModeManager
    ): GameEngineInterface {
        return GameEngine(undoManager, moveAnalyzer, gameValidator, moveExecutor, mergeDetector, mergeExecutor, statisticsManager, swapModeManager)
    }
    
    @Provides
    @Singleton
    fun provideAnimationController(): AnimationController {
        return AnimationController()
    }
    
    @Provides
    @Singleton
    fun provideGameStateManager(
        gameEngine: GameEngineInterface,
        commandManager: CommandManager,
        premiumStatusManager: PremiumStatusManager,
        animationController: AnimationController
    ): GameStateManager {
        return GameStateManager(gameEngine, commandManager, premiumStatusManager, animationController)
    }

    @Provides
    @ApplicationScope
    fun provideApplicationScope(): CoroutineScope {
        return CoroutineScope(SupervisorJob() + Dispatchers.Default)
    }

    @Provides
    @Singleton
    fun provideStatisticsEventProcessor(
        statisticsRepository: GameStatisticsRepository,
        @ApplicationScope processingScope: CoroutineScope
    ): StatisticsEventProcessor {
        return StatisticsEventProcessor(statisticsRepository, processingScope)
    }
}