package com.frageo.triswipe

import android.app.Application
import com.frageo.triswipe.premium.PremiumStatusManager
import dagger.hilt.android.HiltAndroidApp
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltAndroidApp
class TriSwipeApplication : Application() {
    
    @Inject
    lateinit var premiumStatusManager: PremiumStatusManager
    
    
    
    
    
    private val applicationScope = CoroutineScope(SupervisorJob())
    
    override fun onCreate() {
        super.onCreate()

        // Initialize premium status manager
        applicationScope.launch {
            premiumStatusManager.initialize()
        }
    }
    
    override fun onTerminate() {
        super.onTerminate()
        // Simplified application termination
    }
}