package com.frageo.triswipe.data.repository

import com.frageo.triswipe.data.models.TileTheme
import com.frageo.triswipe.data.models.ThemeMode
import com.frageo.triswipe.data.models.SwapMode

/**
 * Repository interface for user preference and settings management.
 * Handles animation settings, gesture configurations, themes, and auto-save preferences.
 */
interface UserPreferencesRepository {
    
    // Animation Settings
    /**
     * Get the current animation speed multiplier.
     * @return Float value representing animation speed (typically 0.5f to 2.0f)
     */
    suspend fun getAnimationSpeed(): Float
    
    /**
     * Set the animation speed multiplier.
     * @param speed Float value for animation speed
     */
    suspend fun setAnimationSpeed(speed: Float)
    
    // Gesture Settings
    /**
     * Get the gesture threshold for swipe detection.
     * @return Float value representing minimum swipe distance
     */
    suspend fun getGestureThreshold(): Float
    
    /**
     * Set the gesture threshold for swipe detection.
     * @param threshold Float value for minimum swipe distance
     */
    suspend fun setGestureThreshold(threshold: Float)
    
    // Auto-save Settings
    /**
     * Enable or disable automatic game state saving.
     * @param enable True to enable auto-save, false to disable
     */
    suspend fun enableAutoSave(enable: Boolean)
    
    /**
     * Check if auto-save is currently enabled.
     * @return True if auto-save is enabled, false otherwise
     */
    suspend fun isAutoSaveEnabled(): Boolean
    
    // Theme Settings
    /**
     * Get the currently selected tile theme.
     * @return TileTheme currently selected by user
     */
    suspend fun getSelectedTheme(): TileTheme
    
    /**
     * Set the selected tile theme.
     * @param theme TileTheme to set as selected
     */
    suspend fun setSelectedTheme(theme: TileTheme)
    
    /**
     * Get the current theme mode (light/dark/system).
     * @return ThemeMode currently selected
     */
    suspend fun getThemeMode(): ThemeMode
    
    /**
     * Set the theme mode.
     * @param themeMode ThemeMode to set (light/dark/system)
     */
    suspend fun setThemeMode(themeMode: ThemeMode)
    
    // Swap Mode Settings
    /**
     * Get the current swap mode setting.
     * @return SwapMode currently selected
     */
    suspend fun getSwapMode(): SwapMode
    
    /**
     * Set the swap mode.
     * @param swapMode SwapMode to set
     */
    suspend fun setSwapMode(swapMode: SwapMode)
}