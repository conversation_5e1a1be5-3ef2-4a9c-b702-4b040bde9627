package com.frageo.triswipe.data.repository.entities

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.frageo.triswipe.data.database.Converters
import com.frageo.triswipe.data.models.Tile

@Entity(tableName = "game_state")
@TypeConverters(Converters::class)
data class GameStateEntity(
    @PrimaryKey
    val id: String = "current_game",
    val board: Array<Array<Tile?>>,
    val score: Int,
    val moves: Int,
    val isGameOver: <PERSON>olean,
    val hasWon: <PERSON>olean,
    val lastMoveType: String?,
    val timestamp: Long = System.currentTimeMillis()
) {
    override fun equals(other: Any?): <PERSON><PERSON>an {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as GameStateEntity

        if (id != other.id) return false
        if (!board.contentDeepEquals(other.board)) return false
        if (score != other.score) return false
        if (moves != other.moves) return false
        if (isGameOver != other.isGameOver) return false
        if (hasWon != other.hasWon) return false
        if (lastMoveType != other.lastMoveType) return false

        return true
    }

    override fun hashCode(): Int {
        var result = id.hashCode()
        result = 31 * result + board.contentDeepHashCode()
        result = 31 * result + score
        result = 31 * result + moves
        result = 31 * result + isGameOver.hashCode()
        result = 31 * result + hasWon.hashCode()
        result = 31 * result + (lastMoveType?.hashCode() ?: 0)
        return result
    }
}