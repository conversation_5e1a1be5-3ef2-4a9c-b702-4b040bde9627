package com.frageo.triswipe.data.models

import kotlin.random.Random

class GameBoard {
    private val board: Array<Array<Tile?>> = Array(GameConfig.BOARD_SIZE) { Array(GameConfig.BOARD_SIZE) { null } }
    
    fun getTile(position: Position): Tile? {
        return if (position.isValid()) {
            board[position.row][position.col]
        } else {
            null
        }
    }
    
    fun setTile(position: Position, tile: Tile?) {
        if (position.isValid()) {
            board[position.row][position.col] = tile
        }
    }
    
    fun isEmpty(position: Position): Boolean {
        return getTile(position) == null
    }
    
    fun getEmptyPositions(): List<Position> {
        val emptyPositions = mutableListOf<Position>()
        for (row in 0 until GameConfig.BOARD_SIZE) {
            for (col in 0 until GameConfig.BOARD_SIZE) {
                val position = Position(row, col)
                if (isEmpty(position)) {
                    emptyPositions.add(position)
                }
            }
        }
        return emptyPositions
    }
    
    fun hasEmptySpaces(): Boolean {
        return getEmptyPositions().isNotEmpty()
    }
    
    fun getAllTiles(): List<Tile> {
        val tiles = mutableListOf<Tile>()
        for (row in 0 until GameConfig.BOARD_SIZE) {
            for (col in 0 until GameConfig.BOARD_SIZE) {
                val tile = board[row][col]
                if (tile != null) {
                    tiles.add(tile)
                }
            }
        }
        return tiles
    }
    
    fun spawnRandomTile(): Tile? {
        val emptyPositions = getEmptyPositions()
        if (emptyPositions.isEmpty()) {
            return null
        }
        
        val randomPosition = emptyPositions.random()
        val randomValue = getRandomSpawnValue()
        val newTile = Tile(randomValue, randomPosition, origin = TileOrigin.SPAWN)
        
        setTile(randomPosition, newTile)
        return newTile
    }
    
    private fun getRandomSpawnValue(): Int {
        val random = Random.nextFloat()
        var cumulativeProbability = 0f
        
        // Use adaptive spawning if available (premium feature), otherwise use default
        val spawnProbabilities = getAdaptiveSpawnProbabilities() ?: GameConfig.SPAWN_PROBABILITIES
        
        for ((value, probability) in spawnProbabilities) {
            cumulativeProbability += probability
            if (random <= cumulativeProbability) {
                return value
            }
        }
        
        return GameConfig.SPAWN_TILE_VALUES.first()
    }
    
    /**
     * Adaptive spawning system that adjusts tile spawn probabilities based on game progress.
     * Premium feature that reduces early-game grinding by spawning higher tiles for advanced players.
     */
    private fun getAdaptiveSpawnProbabilities(): Map<Int, Float>? {
        val highestTile = getHighestTileValue()
        
        // Only apply adaptive spawning for premium users and when highest tile is significant
        // This feature reduces grinding while maintaining challenge balance
        return when {
            highestTile >= 243 -> { // Advanced gameplay - spawn higher tiles more often
                mapOf(
                    1 to 0.3f,   // Reduced from 0.7f
                    3 to 0.4f,   // Increased from 0.25f
                    9 to 0.25f,  // Increased from 0.05f
                    27 to 0.05f  // New spawn option for advanced players
                )
            }
            highestTile >= 81 -> { // Intermediate gameplay - slightly better spawns
                mapOf(
                    1 to 0.5f,   // Reduced from 0.7f
                    3 to 0.35f,  // Increased from 0.25f
                    9 to 0.15f   // Increased from 0.05f
                )
            }
            highestTile >= 27 -> { // Early-mid game - minor improvements
                mapOf(
                    1 to 0.6f,   // Slightly reduced from 0.7f
                    3 to 0.3f,   // Increased from 0.25f
                    9 to 0.1f    // Increased from 0.05f
                )
            }
            else -> null // Use default spawning for beginners
        }
    }
    
    /**
     * Gets the highest tile value currently on the board
     */
    private fun getHighestTileValue(): Int {
        var highest = 1
        for (row in 0 until GameConfig.BOARD_SIZE) {
            for (col in 0 until GameConfig.BOARD_SIZE) {
                val tile = getTile(Position(row, col))
                if (tile != null && tile.value > highest) {
                    highest = tile.value
                }
            }
        }
        return highest
    }
    
    fun initializeBoard() {
        clearBoard()
        repeat(GameConfig.INITIAL_TILES_COUNT) {
            spawnRandomTile()
        }
    }
    
    fun clearBoard() {
        for (row in 0 until GameConfig.BOARD_SIZE) {
            for (col in 0 until GameConfig.BOARD_SIZE) {
                board[row][col] = null
            }
        }
    }
    
    fun copyBoard(): Array<Array<Tile?>> {
        return Array(GameConfig.BOARD_SIZE) { row ->
            Array(GameConfig.BOARD_SIZE) { col ->
                board[row][col]
            }
        }
    }
    
    fun updateTileOrigin(position: Position, newOrigin: TileOrigin) {
        val tile = getTile(position)
        if (tile != null) {
            setTile(position, tile.copy(origin = newOrigin))
        }
    }
    
    fun toGameState(score: Int = 0, moves: Int = 0, isGameOver: Boolean = false): GameState {
        return GameState(
            board = copyBoard(),
            score = score,
            moves = moves,
            isGameOver = isGameOver
        )
    }
    
    fun moveTiles(direction: Direction): List<TileMovement> {
        val movements = mutableListOf<TileMovement>()
        
        when (direction) {
            Direction.UP -> {
                for (col in 0 until GameConfig.BOARD_SIZE) {
                    val columnMovements = moveColumn(col, Direction.UP)
                    movements.addAll(columnMovements)
                }
            }
            Direction.DOWN -> {
                for (col in 0 until GameConfig.BOARD_SIZE) {
                    val columnMovements = moveColumn(col, Direction.DOWN)
                    movements.addAll(columnMovements)
                }
            }
            Direction.LEFT -> {
                for (row in 0 until GameConfig.BOARD_SIZE) {
                    val rowMovements = moveRow(row, Direction.LEFT)
                    movements.addAll(rowMovements)
                }
            }
            Direction.RIGHT -> {
                for (row in 0 until GameConfig.BOARD_SIZE) {
                    val rowMovements = moveRow(row, Direction.RIGHT)
                    movements.addAll(rowMovements)
                }
            }
        }
        
        return movements
    }
    
    private fun moveColumn(col: Int, direction: Direction): List<TileMovement> {
        val movements = mutableListOf<TileMovement>()
        val tiles = mutableListOf<Tile>()
        
        // Extract tiles in movement order
        val range = if (direction == Direction.UP) {
            0 until GameConfig.BOARD_SIZE
        } else { // DOWN
            0 until GameConfig.BOARD_SIZE
        }
        
        // First pass: collect tiles that will move
        for (row in range) {
            val tile = board[row][col]
            if (tile != null) {
                tiles.add(tile)
            }
        }
        
        // Early return if no tiles to move
        if (tiles.isEmpty()) {
            return movements
        }
        
        // Calculate target positions
        val targetPositions = if (direction == Direction.UP) {
            (0 until tiles.size).map { Position(it, col) }
        } else {
            ((GameConfig.BOARD_SIZE - tiles.size) until GameConfig.BOARD_SIZE).map { Position(it, col) }
        }
        
        // Clear the entire column first
        for (row in 0 until GameConfig.BOARD_SIZE) {
            board[row][col] = null
        }
        
        // Place tiles in their new positions and track movements
        for (i in tiles.indices) {
            val tile = tiles[i]
            val newPosition = targetPositions[i]
            
            // Always create new tile and track movement for animation purposes
            val newTile = tile.withPosition(newPosition)
            board[newPosition.row][newPosition.col] = newTile
            
            // Only track actual movements for game logic validation
            // but ensure all tiles get animated during swipes
            if (tile.position != newPosition) {
                movements.add(TileMovement(tile, newPosition))
            }
        }
        
        return movements
    }
    
    private fun moveRow(row: Int, direction: Direction): List<TileMovement> {
        val movements = mutableListOf<TileMovement>()
        val tiles = mutableListOf<Tile>()
        
        // Extract tiles in movement order
        val range = if (direction == Direction.LEFT) {
            0 until GameConfig.BOARD_SIZE
        } else { // RIGHT
            0 until GameConfig.BOARD_SIZE
        }
        
        // First pass: collect tiles that will move
        for (col in range) {
            val tile = board[row][col]
            if (tile != null) {
                tiles.add(tile)
            }
        }
        
        // Early return if no tiles to move
        if (tiles.isEmpty()) {
            return movements
        }
        
        // Calculate target positions
        val targetPositions = if (direction == Direction.LEFT) {
            (0 until tiles.size).map { Position(row, it) }
        } else {
            ((GameConfig.BOARD_SIZE - tiles.size) until GameConfig.BOARD_SIZE).map { Position(row, it) }
        }
        
        // Clear the entire row first
        for (col in 0 until GameConfig.BOARD_SIZE) {
            board[row][col] = null
        }
        
        // Place tiles in their new positions and track movements
        for (i in tiles.indices) {
            val tile = tiles[i]
            val newPosition = targetPositions[i]
            
            // Always create new tile and track movement for animation purposes
            val newTile = tile.withPosition(newPosition)
            board[newPosition.row][newPosition.col] = newTile
            
            // Only track actual movements for game logic validation
            // but ensure all tiles get animated during swipes
            if (tile.position != newPosition) {
                movements.add(TileMovement(tile, newPosition))
            }
        }
        
        return movements
    }
    
    fun swapTiles(pos1: Position, pos2: Position): Boolean {
        if (!pos1.isValid() || !pos2.isValid() || !pos1.isAdjacent(pos2)) {
            return false
        }
        
        val tile1 = getTile(pos1)
        val tile2 = getTile(pos2)
        
        if (tile1 == null && tile2 == null) {
            return false
        }
        
        val newTile1 = tile2?.withPosition(pos1)
        val newTile2 = tile1?.withPosition(pos2)
        
        setTile(pos1, newTile1)
        setTile(pos2, newTile2)
        
        return true
    }
    
    // Board State Validation Functions
    
    fun validateBoardState(): BoardValidationResult {
        val errors = mutableListOf<String>()
        val warnings = mutableListOf<String>()
        
        // Check for position consistency
        val positionErrors = validateTilePositions()
        errors.addAll(positionErrors)
        
        // Check for duplicate tiles
        val duplicateErrors = validateNoDuplicateTiles()
        errors.addAll(duplicateErrors)
        
        // Check for invalid tile values
        val valueErrors = validateTileValues()
        errors.addAll(valueErrors)
        
        // Check board bounds
        val boundsErrors = validateBoardBounds()
        errors.addAll(boundsErrors)
        
        // Performance warnings
        val performanceWarnings = validatePerformanceMetrics()
        warnings.addAll(performanceWarnings)
        
        return BoardValidationResult(
            isValid = errors.isEmpty(),
            errors = errors,
            warnings = warnings,
            tileCount = getAllTiles().size,
            emptySpaceCount = getEmptyPositions().size
        )
    }
    
    private fun validateTilePositions(): List<String> {
        val errors = mutableListOf<String>()
        
        for (row in 0 until GameConfig.BOARD_SIZE) {
            for (col in 0 until GameConfig.BOARD_SIZE) {
                val tile = board[row][col]
                if (tile != null) {
                    val expectedPosition = Position(row, col)
                    if (tile.position != expectedPosition) {
                        errors.add("Tile at board[$row][$col] has position ${tile.position} but should be $expectedPosition")
                    }
                }
            }
        }
        
        return errors
    }
    
    private fun validateNoDuplicateTiles(): List<String> {
        val errors = mutableListOf<String>()
        val seenIds = mutableSetOf<String>()
        val duplicatePositions = mutableSetOf<Position>()
        
        for (row in 0 until GameConfig.BOARD_SIZE) {
            for (col in 0 until GameConfig.BOARD_SIZE) {
                val tile = board[row][col]
                if (tile != null) {
                    // Check for duplicate tile IDs
                    if (tile.id in seenIds) {
                        errors.add("Duplicate tile ID ${tile.id} found at position ${tile.position}")
                    } else {
                        seenIds.add(tile.id)
                    }
                    
                    // Check for tiles occupying the same position
                    if (tile.position in duplicatePositions) {
                        errors.add("Multiple tiles claim position ${tile.position}")
                    } else {
                        duplicatePositions.add(tile.position)
                    }
                }
            }
        }
        
        return errors
    }
    
    private fun validateTileValues(): List<String> {
        val errors = mutableListOf<String>()
        
        for (row in 0 until GameConfig.BOARD_SIZE) {
            for (col in 0 until GameConfig.BOARD_SIZE) {
                val tile = board[row][col]
                if (tile != null) {
                    // Check if tile value is valid
                    if (tile.value !in GameConfig.TILE_VALUES) {
                        errors.add("Invalid tile value ${tile.value} at position ${tile.position}")
                    }
                    
                    // Check if tile value is positive
                    if (tile.value <= 0) {
                        errors.add("Tile value must be positive, found ${tile.value} at position ${tile.position}")
                    }
                }
            }
        }
        
        return errors
    }
    
    private fun validateBoardBounds(): List<String> {
        val errors = mutableListOf<String>()
        
        for (row in 0 until GameConfig.BOARD_SIZE) {
            for (col in 0 until GameConfig.BOARD_SIZE) {
                val tile = board[row][col]
                if (tile != null) {
                    if (!tile.position.isValid()) {
                        errors.add("Tile at board[$row][$col] has invalid position ${tile.position}")
                    }
                }
            }
        }
        
        return errors
    }
    
    private fun validatePerformanceMetrics(): List<String> {
        val warnings = mutableListOf<String>()
        val tileCount = getAllTiles().size
        val totalSpaces = GameConfig.BOARD_SIZE * GameConfig.BOARD_SIZE
        
        // Warn if board is too full (might indicate game over soon)
        if (tileCount > totalSpaces * 0.8) {
            warnings.add("Board is ${(tileCount.toFloat() / totalSpaces * 100).toInt()}% full - game may end soon")
        }
        
        // Warn if board is too empty (might indicate early game state)
        if (tileCount < 3) {
            warnings.add("Board has very few tiles ($tileCount) - might be early game state")
        }
        
        return warnings
    }
    
    fun canPerformMove(direction: Direction): Boolean {
        val tempBoard = GameBoard()
        copyBoardStateTo(tempBoard)
        val movements = tempBoard.moveTiles(direction)
        return movements.isNotEmpty()
    }
    
    fun prevalidateSwipe(direction: Direction): SwipeValidationResult {
        // Check if swipe would actually change board state
        val tempBoard = GameBoard()
        copyBoardStateTo(tempBoard)
        val movements = tempBoard.moveTiles(direction)
        
        if (movements.isEmpty()) {
            return SwipeValidationResult(
                isValid = false,
                reason = "No tiles would move in direction $direction"
            )
        }
        
        // Validate board state after movement
        val validationResult = tempBoard.validateBoardState()
        if (!validationResult.isValid) {
            return SwipeValidationResult(
                isValid = false,
                reason = "Board state would be invalid after swipe: ${validationResult.errors.joinToString(", ")}"
            )
        }
        
        return SwipeValidationResult(
            isValid = true,
            reason = "Swipe is valid",
            expectedMovements = movements.size
        )
    }
    
    fun prevalidateSwap(pos1: Position, pos2: Position): SwapValidationResult {
        // Basic validation
        if (!pos1.isValid() || !pos2.isValid()) {
            return SwapValidationResult(
                isValid = false,
                reason = "Invalid positions: $pos1 or $pos2"
            )
        }
        
        if (!pos1.isAdjacent(pos2)) {
            return SwapValidationResult(
                isValid = false,
                reason = "Positions $pos1 and $pos2 are not adjacent"
            )
        }
        
        val tile1 = getTile(pos1)
        val tile2 = getTile(pos2)
        
        if (tile1 == null && tile2 == null) {
            return SwapValidationResult(
                isValid = false,
                reason = "Cannot swap two empty positions"
            )
        }
        
        // Test the swap
        val tempBoard = GameBoard()
        copyBoardStateTo(tempBoard)
        val swapSuccess = tempBoard.swapTiles(pos1, pos2)
        
        if (!swapSuccess) {
            return SwapValidationResult(
                isValid = false,
                reason = "Swap operation failed"
            )
        }
        
        // Validate board state after swap
        val validationResult = tempBoard.validateBoardState()
        if (!validationResult.isValid) {
            return SwapValidationResult(
                isValid = false,
                reason = "Board state would be invalid after swap: ${validationResult.errors.joinToString(", ")}"
            )
        }
        
        return SwapValidationResult(
            isValid = true,
            reason = "Swap is valid"
        )
    }
    
    private fun copyBoardStateTo(targetBoard: GameBoard) {
        targetBoard.clearBoard()
        for (row in 0 until GameConfig.BOARD_SIZE) {
            for (col in 0 until GameConfig.BOARD_SIZE) {
                val position = Position(row, col)
                val tile = getTile(position)
                if (tile != null) {
                    targetBoard.setTile(position, tile)
                }
            }
        }
    }
    
    fun restoreFromArray(boardState: Array<Array<Tile?>>) {
        clearBoard()
        for (row in 0 until GameConfig.BOARD_SIZE) {
            for (col in 0 until GameConfig.BOARD_SIZE) {
                val position = Position(row, col)
                val tile = boardState[row][col]
                if (tile != null) {
                    setTile(position, tile)
                }
            }
        }
    }
}

data class BoardValidationResult(
    val isValid: Boolean,
    val errors: List<String>,
    val warnings: List<String>,
    val tileCount: Int,
    val emptySpaceCount: Int
)

data class SwipeValidationResult(
    val isValid: Boolean,
    val reason: String,
    val expectedMovements: Int = 0
)

data class SwapValidationResult(
    val isValid: Boolean,
    val reason: String
)

enum class Direction {
    UP, DOWN, LEFT, RIGHT
}

data class TileMovement(
    val tile: Tile,
    val toPosition: Position
)