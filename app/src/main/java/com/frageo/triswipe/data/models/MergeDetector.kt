package com.frageo.triswipe.data.models

class MergeDetector {
    
    fun findMergeGroups(board: GameBoard): List<MergeGroup> {
        val mergeGroups = mutableListOf<MergeGroup>()
        val visited = mutableSetOf<Position>()
        
        for (row in 0 until GameConfig.BOARD_SIZE) {
            for (col in 0 until GameConfig.BOARD_SIZE) {
                val position = Position(row, col)
                if (position !in visited) {
                    val tile = board.getTile(position)
                    if (tile != null) {
                        val allConnected = findConnectedGroup(board, position, tile.value, visited)
                        
                        // Break large groups into groups of exactly 3 tiles
                        if (allConnected.size >= GameConfig.MIN_TILES_FOR_MERGE) {
                            val groups = breakIntoTripleGroups(allConnected)
                            for (group in groups) {
                                mergeGroups.add(MergeGroup(group, tile.value))
                            }
                        }
                    }
                }
            }
        }
        
        return mergeGroups
    }
    
    private fun findConnectedGroup(
        board: GameBoard,
        startPosition: Position,
        targetValue: Int,
        visited: MutableSet<Position>
    ): List<Position> {
        val group = mutableListOf<Position>()
        val queue = mutableListOf(startPosition)
        
        while (queue.isNotEmpty()) {
            val currentPosition = queue.removeAt(0)
            
            if (currentPosition in visited) {
                continue
            }
            
            val tile = board.getTile(currentPosition)
            if (tile == null || tile.value != targetValue) {
                continue
            }
            
            visited.add(currentPosition)
            group.add(currentPosition)
            
            val neighbors = getAdjacentPositions(currentPosition)
            for (neighbor in neighbors) {
                if (neighbor !in visited) {
                    queue.add(neighbor)
                }
            }
        }
        
        return group
    }
    
    private fun getAdjacentPositions(position: Position): List<Position> {
        val adjacentPositions = mutableListOf<Position>()
        
        val directions = listOf(
            Position(-1, 0), // Up
            Position(1, 0),  // Down
            Position(0, -1), // Left
            Position(0, 1)   // Right
        )
        
        for (direction in directions) {
            val newRow = position.row + direction.row
            val newCol = position.col + direction.col
            val newPosition = Position(newRow, newCol)
            
            if (newPosition.isValid()) {
                adjacentPositions.add(newPosition)
            }
        }
        
        return adjacentPositions
    }
    
    fun hasValidMerges(board: GameBoard): Boolean {
        return findMergeGroups(board).isNotEmpty()
    }
    
    fun canMakeMoves(board: GameBoard): Boolean {
        return hasValidMerges(board) || canSwapTiles(board) || canMoveTiles(board)
    }
    
    private fun canSwapTiles(board: GameBoard): Boolean {
        for (row in 0 until GameConfig.BOARD_SIZE) {
            for (col in 0 until GameConfig.BOARD_SIZE) {
                val position = Position(row, col)
                val adjacentPositions = getAdjacentPositions(position)
                
                for (adjPosition in adjacentPositions) {
                    val tile1 = board.getTile(position)
                    val tile2 = board.getTile(adjPosition)
                    
                    if (tile1 != null || tile2 != null) {
                        val tempBoard = GameBoard()
                        copyBoardState(board, tempBoard)
                        
                        if (tempBoard.swapTiles(position, adjPosition)) {
                            if (hasValidMerges(tempBoard)) {
                                return true
                            }
                        }
                    }
                }
            }
        }
        return false
    }
    
    private fun canMoveTiles(board: GameBoard): Boolean {
        val directions = listOf(Direction.UP, Direction.DOWN, Direction.LEFT, Direction.RIGHT)
        
        for (direction in directions) {
            val tempBoard = GameBoard()
            copyBoardState(board, tempBoard)
            
            val movements = tempBoard.moveTiles(direction)
            if (movements.isNotEmpty()) {
                if (hasValidMerges(tempBoard) || tempBoard.hasEmptySpaces()) {
                    return true
                }
            }
        }
        return false
    }
    
    private fun copyBoardState(from: GameBoard, to: GameBoard) {
        to.clearBoard()
        for (row in 0 until GameConfig.BOARD_SIZE) {
            for (col in 0 until GameConfig.BOARD_SIZE) {
                val position = Position(row, col)
                val tile = from.getTile(position)
                if (tile != null) {
                    to.setTile(position, tile)
                }
            }
        }
    }
    
    fun findBestMergeGroup(board: GameBoard): MergeGroup? {
        val mergeGroups = findMergeGroups(board)
        return mergeGroups.maxByOrNull { it.positions.size * it.value }
    }
    
    /**
     * Enhanced triple grouping algorithm that optimizes merge center positions
     * and minimizes wasted tiles through intelligent group selection.
     */
    private fun breakIntoTripleGroups(positions: List<Position>): List<List<Position>> {
        if (positions.size < 3) return emptyList()
        
        return when {
            positions.size == 3 -> listOf(positions)
            positions.size <= 6 -> optimizeSmallGroups(positions)
            else -> optimizeLargeGroups(positions)
        }
    }
    
    /**
     * Optimizes small groups (4-6 tiles) using combinatorial analysis
     * to find the best merge center positions.
     */
    private fun optimizeSmallGroups(positions: List<Position>): List<List<Position>> {
        return when (positions.size) {
            4 -> selectBestTripleFrom4(positions)
            5 -> selectBestTripleFrom5(positions)
            6 -> selectBestTriplesFrom6(positions)
            else -> emptyList()
        }
    }
    
    /**
     * Handles large groups (7+ tiles) using geometric clustering algorithm
     * to maintain O(n) performance while optimizing merge positions.
     */
    private fun optimizeLargeGroups(positions: List<Position>): List<List<Position>> {
        val clusters = mutableListOf<List<Position>>()
        val remaining = positions.toMutableList()
        
        while (remaining.size >= 3) {
            // Find the most strategically valuable position as seed
            val seed = findOptimalSeed(remaining)
            
            // Find 2 best neighbors to form optimal triangle
            val neighbors = findOptimalNeighbors(seed, remaining.filter { it != seed })
            
            if (neighbors.size >= 2) {
                val cluster = listOf(seed, neighbors[0], neighbors[1])
                clusters.add(cluster)
                remaining.removeAll(cluster)
            } else {
                // If we can't find good neighbors, fall back to sequential
                break
            }
        }
        
        // Handle any remaining tiles with sequential grouping
        var i = 0
        while (i + 2 < remaining.size) {
            clusters.add(remaining.subList(i, i + 3))
            i += 3
        }
        
        return clusters
    }
    
    // ===== Foundation Utilities =====
    
    /**
     * Position analysis utilities for strategic grouping
     */
    private fun isCenterPosition(pos: Position): Boolean {
        return pos.row in 1..2 && pos.col in 1..2
    }
    
    private fun isEdgePosition(pos: Position): Boolean {
        return (pos.row == 0 || pos.row == 3 || pos.col == 0 || pos.col == 3) && !isCornerPosition(pos)
    }
    
    private fun isCornerPosition(pos: Position): Boolean {
        return (pos.row == 0 && pos.col == 0) || 
               (pos.row == 0 && pos.col == 3) ||
               (pos.row == 3 && pos.col == 0) || 
               (pos.row == 3 && pos.col == 3)
    }
    
    private fun calculateDistance(p1: Position, p2: Position): Double {
        val dx = (p1.col - p2.col).toDouble()
        val dy = (p1.row - p2.row).toDouble()
        return kotlin.math.sqrt(dx * dx + dy * dy)
    }
    
    /**
     * Generates all combinations of r elements from this list
     */
    private fun <T> List<T>.combinations(r: Int): List<List<T>> {
        if (r > size || r < 0) return emptyList()
        if (r == 0) return listOf(emptyList())
        if (r == size) return listOf(this)
        
        val result = mutableListOf<List<T>>()
        
        fun backtrack(start: Int, current: MutableList<T>) {
            if (current.size == r) {
                result.add(current.toList())
                return
            }
            
            for (i in start until size) {
                current.add(this[i])
                backtrack(i + 1, current)
                current.removeAt(current.size - 1)
            }
        }
        
        backtrack(0, mutableListOf())
        return result
    }
    
    // ===== Scoring System =====
    
    /**
     * Data class representing the quality score of a group
     */
    private data class GroupScore(
        val centerQuality: Double,
        val compactness: Double,
        val strategicValue: Double
    ) {
        val totalScore: Double get() = centerQuality * 0.5 + compactness * 0.3 + strategicValue * 0.2
    }
    
    /**
     * Calculates comprehensive quality score for a group of positions
     */
    private fun calculateGroupScore(group: List<Position>): GroupScore {
        val center = calculateOptimalCenter(group)
        
        val centerQuality = when {
            isCenterPosition(center) -> 1.0
            isEdgePosition(center) -> 0.7
            isCornerPosition(center) -> 0.5
            else -> 0.8
        }
        
        // Compactness: prefer tighter groups (lower average distance from center)
        val avgDistanceFromCenter = group.map { calculateDistance(it, center) }.average()
        val compactness = 1.0 / (1.0 + avgDistanceFromCenter * 0.5)
        
        // Strategic value: prefer groups that leave good positions for future moves
        val strategicValue = calculateStrategicValue(group)
        
        return GroupScore(centerQuality, compactness, strategicValue)
    }
    
    /**
     * Enhanced center calculation considering geometric optimization
     */
    private fun calculateOptimalCenter(positions: List<Position>): Position {
        if (positions.size == 3) {
            return getBestCenterFor3Tiles(positions)
        }
        
        // For other sizes, use geometric center approach
        val avgRow = positions.sumOf { it.row }.toDouble() / positions.size
        val avgCol = positions.sumOf { it.col }.toDouble() / positions.size
        return positions.minByOrNull { 
            kotlin.math.abs(it.row - avgRow) + kotlin.math.abs(it.col - avgCol) 
        } ?: positions.first()
    }
    
    /**
     * Optimized center calculation for 3-tile groups
     */
    private fun getBestCenterFor3Tiles(positions: List<Position>): Position {
        val sorted = positions.sortedWith(compareBy<Position> { it.row }.thenBy { it.col })
        
        // Check if it's a straight line (horizontal or vertical)
        val isHorizontalLine = sorted.all { it.row == sorted[0].row }
        val isVerticalLine = sorted.all { it.col == sorted[0].col }
        
        return when {
            isHorizontalLine || isVerticalLine -> {
                // For straight lines, return the middle tile
                sorted[1]
            }
            else -> {
                // For L-shapes or other patterns, find the most central position
                val avgRow = sorted.sumOf { it.row }.toDouble() / 3
                val avgCol = sorted.sumOf { it.col }.toDouble() / 3
                sorted.minByOrNull { 
                    kotlin.math.abs(it.row - avgRow) + kotlin.math.abs(it.col - avgCol) 
                } ?: sorted.first()
            }
        }
    }
    
    /**
     * Calculates strategic value based on position quality and future move potential
     */
    private fun calculateStrategicValue(group: List<Position>): Double {
        // Higher strategic value for groups that include center positions
        val centerPositions = group.count { isCenterPosition(it) }
        val edgePositions = group.count { isEdgePosition(it) }
        val cornerPositions = group.count { isCornerPosition(it) }
        
        return (centerPositions * 1.0 + edgePositions * 0.6 + cornerPositions * 0.3) / group.size
    }
    
    // ===== Small Group Optimization Algorithms =====
    
    /**
     * Optimizes 4-tile groups by selecting the best triple based on merge center quality
     */
    private fun selectBestTripleFrom4(positions: List<Position>): List<List<Position>> {
        val combinations = positions.combinations(3)
        
        val bestCombination = combinations.maxByOrNull { combination ->
            calculateGroupScore(combination).totalScore
        }
        
        return if (bestCombination != null) {
            listOf(bestCombination)
        } else {
            // Fallback to sequential if scoring fails
            listOf(positions.subList(0, 3))
        }
    }
    
    /**
     * Optimizes 5-tile groups by selecting the best triple that maximizes strategic value
     */
    private fun selectBestTripleFrom5(positions: List<Position>): List<List<Position>> {
        val combinations = positions.combinations(3)
        
        var bestScore = -1.0
        var bestCombination: List<Position>? = null
        
        for (combination in combinations) {
            val score = calculateGroupScore(combination)
            
            // For 5-tile groups, prioritize center quality more heavily
            val adjustedScore = score.centerQuality * 0.7 + score.compactness * 0.2 + score.strategicValue * 0.1
            
            if (adjustedScore > bestScore) {
                bestScore = adjustedScore
                bestCombination = combination
            }
        }
        
        return if (bestCombination != null) {
            listOf(bestCombination)
        } else {
            // Fallback to sequential
            listOf(positions.subList(0, 3))
        }
    }
    
    /**
     * Optimizes 6-tile groups by creating two optimal triples that maximize overall value
     */
    private fun selectBestTriplesFrom6(positions: List<Position>): List<List<Position>> {
        val allTripleCombinations = positions.combinations(3)
        
        var bestScore = -1.0
        var bestPair: Pair<List<Position>, List<Position>>? = null
        
        // Try all combinations of first triple, then select best second triple from remaining
        for (firstTriple in allTripleCombinations) {
            val remaining = positions.filter { it !in firstTriple }
            
            if (remaining.size == 3) {
                val firstScore = calculateGroupScore(firstTriple)
                val secondScore = calculateGroupScore(remaining)
                
                // Combined score with slight preference for balanced groups
                val combinedScore = (firstScore.totalScore + secondScore.totalScore) / 2.0
                val balanceBonus = 1.0 - kotlin.math.abs(firstScore.totalScore - secondScore.totalScore) * 0.1
                val adjustedScore = combinedScore * balanceBonus
                
                if (adjustedScore > bestScore) {
                    bestScore = adjustedScore
                    bestPair = Pair(firstTriple, remaining)
                }
            }
        }
        
        return if (bestPair != null) {
            listOf(bestPair.first, bestPair.second)
        } else {
            // Fallback to sequential grouping
            listOf(positions.subList(0, 3), positions.subList(3, 6))
        }
    }
    
    // ===== Large Group Optimization Algorithms =====
    
    /**
     * Finds the optimal seed position for geometric clustering
     */
    private fun findOptimalSeed(positions: List<Position>): Position {
        return positions.maxByOrNull { pos ->
            val score = when {
                isCenterPosition(pos) -> 1.0
                isEdgePosition(pos) -> 0.7
                isCornerPosition(pos) -> 0.5
                else -> 0.8
            }
            
            // Bonus for positions with many adjacent tiles in the group
            val adjacentCount = getAdjacentPositions(pos).count { it in positions }
            score + (adjacentCount * 0.1)
        } ?: positions.first()
    }
    
    /**
     * Finds the 2 best neighbor positions to form an optimal triangle with the seed
     */
    private fun findOptimalNeighbors(seed: Position, candidates: List<Position>): List<Position> {
        // Score each candidate based on distance from seed and position quality
        val scoredCandidates = candidates.map { candidate ->
            val distance = calculateDistance(seed, candidate)
            val positionScore = when {
                isCenterPosition(candidate) -> 1.0
                isEdgePosition(candidate) -> 0.7
                isCornerPosition(candidate) -> 0.5
                else -> 0.8
            }
            
            // Prefer closer positions but not too close (avoid adjacent-only groups)
            val distanceScore = when {
                distance < 1.5 -> 0.8  // Adjacent tiles
                distance < 2.5 -> 1.0  // Optimal distance
                else -> 0.6            // Far tiles
            }
            
            Pair(candidate, positionScore * 0.6 + distanceScore * 0.4)
        }.sortedByDescending { it.second }
        
        return scoredCandidates.take(2).map { it.first }
    }
    
    fun getMergeScore(mergeGroup: MergeGroup): Int {
        val newTileValue = GameConfig.getNextTileValue(mergeGroup.value)
        return GameConfig.calculateScore(newTileValue, mergeGroup.positions.size)
    }
}

data class MergeGroup(
    val positions: List<Position>,
    val value: Int
) {
    fun getCenterPosition(): Position {
        // For 3 tiles (most common case), use more sophisticated logic
        if (positions.size == 3) {
            return getBestCenterFor3Tiles()
        }
        
        // For other cases, use the geometric center approach
        val avgRow = positions.sumOf { it.row }.toDouble() / positions.size
        val avgCol = positions.sumOf { it.col }.toDouble() / positions.size
        return positions.minByOrNull { 
            kotlin.math.abs(it.row - avgRow) + kotlin.math.abs(it.col - avgCol) 
        } ?: positions.first()
    }
    
    private fun getBestCenterFor3Tiles(): Position {
        val sorted = positions.sortedWith(compareBy<Position> { it.row }.thenBy { it.col })
        
        // Check if it's a straight line (horizontal or vertical)
        val isHorizontalLine = sorted.all { it.row == sorted[0].row }
        val isVerticalLine = sorted.all { it.col == sorted[0].col }
        
        return when {
            isHorizontalLine || isVerticalLine -> {
                // For straight lines, return the middle tile
                sorted[1]
            }
            else -> {
                // For L-shapes or other patterns, use geometric center
                val avgRow = sorted.sumOf { it.row }.toDouble() / 3
                val avgCol = sorted.sumOf { it.col }.toDouble() / 3
                sorted.minByOrNull { 
                    kotlin.math.abs(it.row - avgRow) + kotlin.math.abs(it.col - avgCol) 
                } ?: sorted.first()
            }
        }
    }
}

// Extension methods for MergeDetector - these should be at class level, not inside MergeGroup
/**
 * Checks if a swap between two positions would result in any merges
 * @param board The current game board
 * @param pos1 First position to swap
 * @param pos2 Second position to swap
 * @return True if the swap would create at least one merge
 */
fun MergeDetector.wouldSwapCreateMerges(board: GameBoard, pos1: Position, pos2: Position): Boolean {
    // Basic validation first
    if (!pos1.isValid() || !pos2.isValid() || !pos1.isAdjacent(pos2)) {
        return false
    }
    
    // Create a temporary board to simulate the swap
    val tempBoard = GameBoard()
    copyBoardStateForSwapValidation(board, tempBoard)
    
    // Perform the swap on the temporary board
    val swapSuccess = tempBoard.swapTiles(pos1, pos2)
    if (!swapSuccess) {
        return false
    }
    
    // Check if any merges would be possible after the swap
    val mergeGroups = this.findMergeGroups(tempBoard)
    return mergeGroups.isNotEmpty()
}

/**
 * Gets all possible swap positions that would result in merges
 * Useful for UI hints or validation
 */
fun MergeDetector.getValidSwapPositions(board: GameBoard): List<Pair<Position, Position>> {
    val validSwaps = mutableListOf<Pair<Position, Position>>()
    
    for (row in 0 until GameConfig.BOARD_SIZE) {
        for (col in 0 until GameConfig.BOARD_SIZE) {
            val pos1 = Position(row, col)
            
            // Check all adjacent positions
            val adjacentPositions = getAdjacentPositionsForSwap(pos1)
            for (pos2 in adjacentPositions) {
                // Avoid duplicate pairs (pos1,pos2) and (pos2,pos1)
                if (pos1.row < pos2.row || (pos1.row == pos2.row && pos1.col < pos2.col)) {
                    if (wouldSwapCreateMerges(board, pos1, pos2)) {
                        validSwaps.add(Pair(pos1, pos2))
                    }
                }
            }
        }
    }
    
    return validSwaps
}

/**
 * Helper method to get adjacent positions for swap validation
 */
private fun getAdjacentPositionsForSwap(position: Position): List<Position> {
    val adjacentPositions = mutableListOf<Position>()
    
    val directions = listOf(
        Position(-1, 0), // Up
        Position(1, 0),  // Down
        Position(0, -1), // Left
        Position(0, 1)   // Right
    )
    
    for (direction in directions) {
        val newRow = position.row + direction.row
        val newCol = position.col + direction.col
        val newPosition = Position(newRow, newCol)
        
        if (newPosition.isValid()) {
            adjacentPositions.add(newPosition)
        }
    }
    
    return adjacentPositions
}

private fun copyBoardStateForSwapValidation(from: GameBoard, to: GameBoard) {
    to.clearBoard()
    for (row in 0 until GameConfig.BOARD_SIZE) {
        for (col in 0 until GameConfig.BOARD_SIZE) {
            val position = Position(row, col)
            val tile = from.getTile(position)
            if (tile != null) {
                to.setTile(position, tile)
            }
        }
    }
}