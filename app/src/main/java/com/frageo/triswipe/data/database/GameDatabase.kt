package com.frageo.triswipe.data.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import android.content.Context
import com.frageo.triswipe.data.repository.entities.GameStateEntity
import com.frageo.triswipe.data.repository.entities.GameStatsEntity

@Database(
    entities = [GameStateEntity::class, GameStatsEntity::class],
    version = 2,
    exportSchema = true
)
@TypeConverters(Converters::class)
abstract class GameDatabase : RoomDatabase() {
    
    abstract fun gameDao(): GameDao
    
    companion object {
        @Volatile
        private var INSTANCE: GameDatabase? = null
        
        fun getDatabase(context: Context): GameDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    GameDatabase::class.java,
                    "triswipe_database"
                )
                .addMigrations(*DatabaseMigrations.getAllMigrations())
                // Production-ready: No destructive migration fallback
                // If migration fails, app will crash and show clear error
                // This ensures migration issues are caught during development/testing
                .build()
                INSTANCE = instance
                instance
            }
        }
    }
}