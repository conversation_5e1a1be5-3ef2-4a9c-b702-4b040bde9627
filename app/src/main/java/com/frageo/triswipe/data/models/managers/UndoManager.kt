package com.frageo.triswipe.data.models.managers

import com.frageo.triswipe.data.commands.GameStateSnapshot
import com.frageo.triswipe.data.models.UndoResult

/**
 * Interface for managing game undo/redo functionality.
 * Handles state history, undo operations, and history management.
 * 
 * Extracted from GameEngine to separate concerns and improve testability.
 */
interface UndoManager {
    
    /**
     * Save a game state snapshot to the undo history
     * @param snapshot The game state snapshot to save
     */
    fun saveState(snapshot: GameStateSnapshot)
    
    /**
     * Check if undo operation is available
     * @return true if at least one move can be undone
     */
    fun canUndo(): Boolean
    
    /**
     * Get the number of available undo operations
     * @return Number of moves that can be undone
     */
    fun getUndoCount(): Int
    
    /**
     * Perform an undo operation
     * @return UndoResult containing success status and restored game state
     */
    fun performUndo(): UndoResult
    
    /**
     * Clear all undo history
     */
    fun clearHistory()
    
    /**
     * Get the maximum history size
     * @return Maximum number of moves kept in history
     */
    fun getMaxHistorySize(): Int
    
    /**
     * Set the maximum history size
     * @param maxSize Maximum number of moves to keep in history
     */
    fun setMaxHistorySize(maxSize: Int)
}