package com.frageo.triswipe.data.commands

import com.frageo.triswipe.data.models.Direction
import com.frageo.triswipe.data.models.GameEngineInterface
import com.frageo.triswipe.data.models.MergeAction
import com.frageo.triswipe.data.models.TileMovement
import java.util.UUID

/**
 * Command for handling swipe actions in the game.
 * Encapsulates swipe logic and provides undo functionality.
 */
class SwipeCommand(
    private val direction: Direction
) : GameCommand {
    
    override val id: String = UUID.randomUUID().toString()
    override val description: String = "Swipe ${direction.name.lowercase()}"
    
    private var previousState: GameStateSnapshot? = null
    private var executed = false
    
    override fun execute(engine: GameEngineInterface): CommandResult {
        if (!canExecute(engine)) {
            return CommandResult.failure(
                message = "Cannot execute swipe command",
                gameState = engine.getCurrentGameState()
            )
        }
        
        // Save the current state for undo
        previousState = captureGameState(engine)
        
        // Execute the swipe using the existing engine logic
        val swipeResult = engine.performSwipe(direction)
        executed = swipeResult.success
        
        return if (swipeResult.success) {
            CommandResult.success(
                message = "Swipe ${direction.name.lowercase()} completed",
                gameState = swipeResult.gameState,
                scoreGained = swipeResult.scoreGained,
                additionalData = mapOf(
                    "tileMovements" to swipeResult.tileMovements,
                    "mergeActions" to swipeResult.mergeActions,
                    "direction" to direction
                )
            )
        } else {
            CommandResult.failure(
                message = swipeResult.message,
                gameState = swipeResult.gameState
            )
        }
    }
    
    override fun undo(engine: GameEngineInterface): CommandResult {
        if (!canUndo()) {
            return CommandResult.failure(
                message = "Cannot undo swipe command - not executed or no previous state",
                gameState = engine.getCurrentGameState()
            )
        }
        
        val snapshot = previousState!!
        
        // Restore the previous state
        restoreGameState(engine, snapshot)
        
        return CommandResult.success(
            message = "Swipe ${direction.name.lowercase()} undone",
            gameState = engine.getCurrentGameState(),
            additionalData = mapOf(
                "direction" to direction,
                "undone" to true
            )
        )
    }
    
    override fun canExecute(engine: GameEngineInterface): Boolean {
        val currentState = engine.getCurrentGameState()
        
        // Cannot execute if game is over
        if (currentState.isGameOver) {
            return false
        }
        
        // Use the engine's existing validation logic
        // This is a simplified check - the actual validation happens in execute()
        return true
    }
    
    override fun canUndo(): Boolean {
        return executed && previousState != null
    }
    
    /**
     * Capture the current game state for undo purposes
     */
    private fun captureGameState(engine: GameEngineInterface): GameStateSnapshot {
        val currentState = engine.getCurrentGameState()
        return GameStateSnapshot(
            boardState = currentState.board.map { row ->
                row.copyOf()
            }.toTypedArray(),
            score = currentState.score,
            moves = currentState.moves,
            isGameOver = currentState.isGameOver,
            hasWon = currentState.hasWon,
            totalMerges = 0, // This will be properly tracked when we integrate with GameEngine
            timestamp = System.currentTimeMillis()
        )
    }
    
    /**
     * Restore the game state from a snapshot
     */
    private fun restoreGameState(engine: GameEngineInterface, snapshot: GameStateSnapshot) {
        // Create a new GameState from the snapshot
        val restoredState = com.frageo.triswipe.data.models.GameState(
            board = snapshot.boardState,
            score = snapshot.score,
            moves = snapshot.moves,
            isGameOver = snapshot.isGameOver,
            hasWon = snapshot.hasWon
        )
        
        // Use the engine's existing restore functionality
        engine.setGameState(restoredState)
    }
    
    override fun toString(): String {
        return "SwipeCommand(direction=$direction, executed=$executed, canUndo=${canUndo()})"
    }
}