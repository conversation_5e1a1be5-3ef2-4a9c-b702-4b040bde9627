package com.frageo.triswipe.data.commands

import com.frageo.triswipe.data.models.GameEngineInterface
import com.frageo.triswipe.data.models.GameState

/**
 * Base interface for all game commands using the Command pattern.
 * Encapsulates game actions to enable undo/redo functionality, command history,
 * and macro recording capabilities.
 */
interface GameCommand {
    /**
     * Unique identifier for this command
     */
    val id: String
    
    /**
     * Human-readable description of the command
     */
    val description: String
    
    /**
     * Execute the command on the game engine
     * @param engine The game engine to execute the command on
     * @return The result of the command execution
     */
    fun execute(engine: GameEngineInterface): CommandResult
    
    /**
     * Undo the command on the game engine
     * @param engine The game engine to undo the command on
     * @return The result of the undo operation
     */
    fun undo(engine: GameEngineInterface): CommandResult
    
    /**
     * Check if the command can be executed in the current game state
     * @param engine The game engine to check
     * @return true if the command can be executed, false otherwise
     */
    fun canExecute(engine: GameEngineInterface): Boolean
    
    /**
     * Check if the command can be undone
     * @return true if the command can be undone, false otherwise
     */
    fun canUndo(): Boolean
}

/**
 * Result of a command execution or undo operation
 */
data class CommandResult(
    val success: Boolean,
    val message: String,
    val gameState: GameState,
    val scoreGained: Int = 0,
    val additionalData: Map<String, Any> = emptyMap()
) {
    companion object {
        fun success(
            message: String,
            gameState: GameState,
            scoreGained: Int = 0,
            additionalData: Map<String, Any> = emptyMap()
        ): CommandResult {
            return CommandResult(
                success = true,
                message = message,
                gameState = gameState,
                scoreGained = scoreGained,
                additionalData = additionalData
            )
        }
        
        fun failure(
            message: String,
            gameState: GameState,
            additionalData: Map<String, Any> = emptyMap()
        ): CommandResult {
            return CommandResult(
                success = false,
                message = message,
                gameState = gameState,
                additionalData = additionalData
            )
        }
    }
}

/**
 * Snapshot of game state for undo operations
 */
data class GameStateSnapshot(
    val boardState: Array<Array<com.frageo.triswipe.data.models.Tile?>>,
    val score: Int,
    val moves: Int,
    val isGameOver: Boolean,
    val hasWon: Boolean,
    val totalMerges: Int,
    val timestamp: Long = System.currentTimeMillis()
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        
        other as GameStateSnapshot
        
        if (!boardState.contentDeepEquals(other.boardState)) return false
        if (score != other.score) return false
        if (moves != other.moves) return false
        if (isGameOver != other.isGameOver) return false
        if (hasWon != other.hasWon) return false
        if (totalMerges != other.totalMerges) return false
        if (timestamp != other.timestamp) return false
        
        return true
    }
    
    override fun hashCode(): Int {
        var result = boardState.contentDeepHashCode()
        result = 31 * result + score
        result = 31 * result + moves
        result = 31 * result + isGameOver.hashCode()
        result = 31 * result + hasWon.hashCode()
        result = 31 * result + totalMerges
        result = 31 * result + timestamp.hashCode()
        return result
    }
}