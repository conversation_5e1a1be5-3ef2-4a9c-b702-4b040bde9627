package com.frageo.triswipe.data.models.managers

import com.frageo.triswipe.data.models.SwipeResult
import com.frageo.triswipe.data.models.SwapResult
import com.frageo.triswipe.data.models.MoveResult
import com.frageo.triswipe.data.models.Direction
import com.frageo.triswipe.data.models.Position
import com.frageo.triswipe.data.models.MoveType
import com.frageo.triswipe.data.models.GameBoard
import com.frageo.triswipe.data.models.GameState
import com.frageo.triswipe.data.models.TileMovement
import com.frageo.triswipe.data.models.MergeAction
import com.frageo.triswipe.data.models.MergeDetector
import com.frageo.triswipe.data.models.MergeExecutor
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Implementation of MoveExecutor for executing game moves with validation and state management.
 * 
 * Extracted from GameEngine to separate move execution logic and improve testability.
 * Handles the core game flow: merge-move-merge-spawn sequence with comprehensive validation.
 */
@Singleton
class MoveExecutorImpl @Inject constructor(
    private val mergeDetector: MergeDetector,
    private val mergeExecutor: MergeExecutor,
    private val gameValidator: GameValidator
) : MoveExecutor {
    
    override fun executeSwipe(direction: Direction, currentState: GameState, board: GameBoard): SwipeResult {
        if (currentState.isGameOver) {
            return SwipeResult(currentState, false, "Game is over")
        }
        
        // Pre-validate the move
        val validationResult = gameValidator.validateMove(board, MoveType.SWIPE, MoveParams.SwipeParams(direction))
        if (!validationResult.isValid) {
            return SwipeResult(currentState, false, validationResult.reason)
        }
        
        // Execute the game flow
        val flowResult = executeGameFlow(direction, board)
        if (!flowResult.success) {
            return SwipeResult(currentState, false, flowResult.message)
        }
        
        // Update game state
        val newGameState = updateGameState(currentState, flowResult.scoreGained, flowResult.mergeActions.size)
        
        return SwipeResult(
            gameState = newGameState,
            success = true,
            message = "Swipe completed",
            tileMovements = flowResult.tileMovements,
            mergeActions = flowResult.mergeActions,
            scoreGained = flowResult.scoreGained
        )
    }
    
    override fun executeSwap(pos1: Position, pos2: Position, currentState: GameState, board: GameBoard): SwapResult {
        if (currentState.isGameOver) {
            return SwapResult(currentState, false, "Game is over")
        }
        
        // Pre-validate the move
        val validationResult = gameValidator.validateMove(board, MoveType.SWAP, MoveParams.SwapParams(pos1, pos2))
        if (!validationResult.isValid) {
            return SwapResult(currentState, false, validationResult.reason)
        }
        
        // Execute the swap
        val swapSuccess = board.swapTiles(pos1, pos2)
        if (!swapSuccess) {
            return SwapResult(currentState, false, "Swap failed")
        }
        
        // Validate board after swap
        val afterSwapValidation = (gameValidator as GameValidatorImpl).validateBoardAfterMove(board, "swap")
        if (!afterSwapValidation.isValid) {
            return SwapResult(currentState, false, afterSwapValidation.reason)
        }
        
        // Execute auto merges after swap
        val autoMergeResult = mergeExecutor.executeAutoMerges(board, mergeDetector)
        
        // Spawn new tile if merges occurred and there's space
        val newTileSpawned = if (board.hasEmptySpaces() && autoMergeResult.mergeActions.isNotEmpty()) {
            board.spawnRandomTile() != null
        } else false
        
        // Update game state
        val newGameState = updateGameState(currentState, autoMergeResult.totalScore, autoMergeResult.mergeActions.size)
        
        return SwapResult(
            gameState = newGameState,
            success = true,
            message = "Swap completed",
            mergeActions = autoMergeResult.mergeActions,
            scoreGained = autoMergeResult.totalScore
        )
    }
    
    override fun executeValidatedMove(
        moveType: MoveType, 
        params: MoveParams, 
        currentState: GameState, 
        board: GameBoard
    ): MoveResult {
        // Comprehensive validation before any move
        val gameValidation = gameValidator.validateGameState(currentState, board)
        if (!gameValidation.isValid) {
            return MoveResult(
                success = false,
                message = "Game state invalid: ${gameValidation.getAllErrors().joinToString(", ")}",
                gameState = currentState
            )
        }
        
        return when (moveType) {
            MoveType.SWIPE -> {
                when (params) {
                    is MoveParams.SwipeParams -> {
                        val result = executeSwipe(params.direction, currentState, board)
                        MoveResult(
                            success = result.success,
                            message = result.message,
                            gameState = result.gameState,
                            tileMovements = result.tileMovements,
                            mergeActions = result.mergeActions,
                            scoreGained = result.scoreGained
                        )
                    }
                    else -> MoveResult(
                        success = false,
                        message = "Direction required for swipe",
                        gameState = currentState
                    )
                }
            }
            MoveType.SWAP -> {
                when (params) {
                    is MoveParams.SwapParams -> {
                        val result = executeSwap(params.pos1, params.pos2, currentState, board)
                        MoveResult(
                            success = result.success,
                            message = result.message,
                            gameState = result.gameState,
                            mergeActions = result.mergeActions,
                            scoreGained = result.scoreGained
                        )
                    }
                    else -> MoveResult(
                        success = false,
                        message = "Both positions required for swap",
                        gameState = currentState
                    )
                }
            }
        }
    }
    
    override fun executeGameFlow(direction: Direction, board: GameBoard): GameFlowResult {
        try {
            // Step 1: Merge existing tiles first (before any movement)
            val preMergeResult = mergeExecutor.executeAutoMerges(board, mergeDetector)
            
            // Step 2: Move all tiles (including newly merged ones)
            val movements = board.moveTiles(direction)
            
            // Double-check that some action occurred (either merges or movements)
            if (movements.isEmpty() && preMergeResult.mergeActions.isEmpty()) {
                return GameFlowResult(
                    success = false,
                    message = "No tiles moved or merged"
                )
            }
            
            // Validate board state after movement
            val postMoveValidation = (gameValidator as GameValidatorImpl).validateBoardAfterMove(board, "movement")
            if (!postMoveValidation.isValid) {
                return GameFlowResult(
                    success = false,
                    message = postMoveValidation.reason
                )
            }
            
            // Step 3: Merge again after movement (cascading merges)
            val postMergeResult = mergeExecutor.executeAutoMerges(board, mergeDetector)
            
            // Step 4: Spawn random tile after everything is settled
            val newTileSpawned = if (board.hasEmptySpaces()) {
                board.spawnRandomTile() != null
            } else false
            
            val allMergeActions = preMergeResult.mergeActions + postMergeResult.mergeActions
            val totalScore = preMergeResult.totalScore + postMergeResult.totalScore
            
            return GameFlowResult(
                success = true,
                message = "Game flow completed successfully",
                tileMovements = movements,
                mergeActions = allMergeActions,
                scoreGained = totalScore,
                newTileSpawned = newTileSpawned
            )
            
        } catch (e: Exception) {
            return GameFlowResult(
                success = false,
                message = "Game flow failed: ${e.message}"
            )
        }
    }
    
    override fun updateGameState(currentState: GameState, scoreGained: Int, mergeCount: Int): GameState {
        // Create updated game state with new score and incremented moves
        val newScore = currentState.score + scoreGained
        val newMoves = currentState.moves + 1
        
        // Note: Win/loss conditions should be checked by the caller (GameEngine)
        // This method only updates the core metrics
        
        return GameState(
            board = currentState.board, // Board will be updated separately
            score = newScore,
            moves = newMoves,
            isGameOver = currentState.isGameOver, // Preserve current state
            hasWon = currentState.hasWon, // Preserve current state
            lastMoveType = currentState.lastMoveType // Preserve current state
        )
    }
    
    /**
     * Helper method to check if a move would result in any changes
     */
    fun wouldMoveChangeBoard(direction: Direction, board: GameBoard): Boolean {
        val tempBoard = GameBoard()
        copyBoardState(board, tempBoard)
        
        val initialState = tempBoard.copyBoard()
        val movements = tempBoard.moveTiles(direction)
        val mergeResult = mergeExecutor.executeAutoMerges(tempBoard, mergeDetector)
        
        return movements.isNotEmpty() || mergeResult.mergeActions.isNotEmpty()
    }
    
    /**
     * Helper method to copy board state for simulation
     */
    private fun copyBoardState(from: GameBoard, to: GameBoard) {
        to.clearBoard()
        for (row in 0 until 4) { // GameConfig.BOARD_SIZE
            for (col in 0 until 4) {
                val position = Position(row, col)
                val tile = from.getTile(position)
                if (tile != null) {
                    to.setTile(position, tile)
                }
            }
        }
    }
}