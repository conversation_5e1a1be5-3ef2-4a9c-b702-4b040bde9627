package com.frageo.triswipe.data.models.managers

import com.frageo.triswipe.data.models.GameValidationResult
import com.frageo.triswipe.data.models.SwipeValidationResult
import com.frageo.triswipe.data.models.SwapValidationResult
import com.frageo.triswipe.data.models.GameState
import com.frageo.triswipe.data.models.GameBoard
import com.frageo.triswipe.data.models.Direction
import com.frageo.triswipe.data.models.Position
import com.frageo.triswipe.data.models.MoveType
import com.frageo.triswipe.data.models.MergeDetector
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Implementation of GameValidator for validating game states and moves.
 * 
 * Extracted from GameEngine to separate validation concerns and improve testability.
 * Provides comprehensive validation of board state, game state, and move legality.
 */
@Singleton
class GameValidatorImpl @Inject constructor(
    private val mergeDetector: MergeDetector
) : GameValidator {
    
    override fun validateGameState(gameState: GameState, board: GameBoard): GameValidationResult {
        val boardValidation = board.validateBoardState()
        val gameErrors = mutableListOf<String>()
        
        // Validate game state consistency
        if (gameState.score < 0) {
            gameErrors.add("Score cannot be negative: ${gameState.score}")
        }
        
        if (gameState.moves < 0) {
            gameErrors.add("Moves cannot be negative: ${gameState.moves}")
        }
        
        // Validate game over logic
        if (gameState.isGameOver && mergeDetector.canMakeMoves(board)) {
            gameErrors.add("Game marked as over but moves are still possible")
        } else if (!gameState.isGameOver && !mergeDetector.canMakeMoves(board)) {
            gameErrors.add("Game should be over as no moves are possible")
        }
        
        // Validate win condition consistency
        if (gameState.hasWon && gameState.isGameOver) {
            // This could be valid - player won but continued playing until game over
        }
        
        return GameValidationResult(
            isValid = boardValidation.isValid && gameErrors.isEmpty(),
            boardValidation = boardValidation,
            gameErrors = gameErrors,
            gameState = gameState
        )
    }
    
    override fun validateSwipe(board: GameBoard, direction: Direction): SwipeValidationResult {
        return board.prevalidateSwipe(direction)
    }
    
    override fun validateSwap(board: GameBoard, pos1: Position, pos2: Position): SwapValidationResult {
        return board.prevalidateSwap(pos1, pos2)
    }
    
    override fun validateMove(board: GameBoard, moveType: MoveType, params: MoveParams): MoveValidationResult {
        return when (moveType) {
            MoveType.SWIPE -> {
                when (params) {
                    is MoveParams.SwipeParams -> {
                        val swipeResult = validateSwipe(board, params.direction)
                        MoveValidationResult(
                            isValid = swipeResult.isValid,
                            reason = swipeResult.reason,
                            suggestions = if (!swipeResult.isValid) {
                                listOf("Try a different swipe direction", "Look for available tile swaps")
                            } else emptyList()
                        )
                    }
                    else -> MoveValidationResult(
                        isValid = false,
                        reason = "Invalid parameters for swipe move - direction required"
                    )
                }
            }
            MoveType.SWAP -> {
                when (params) {
                    is MoveParams.SwapParams -> {
                        val swapResult = validateSwap(board, params.pos1, params.pos2)
                        MoveValidationResult(
                            isValid = swapResult.isValid,
                            reason = swapResult.reason,
                            suggestions = if (!swapResult.isValid) {
                                listOf("Try swapping adjacent tiles", "Look for tiles that create merges")
                            } else emptyList()
                        )
                    }
                    else -> MoveValidationResult(
                        isValid = false,
                        reason = "Invalid parameters for swap move - two positions required"
                    )
                }
            }
        }
    }
    
    override fun isGameStateConsistent(gameState: GameState): Boolean {
        // Basic consistency checks without board access
        return gameState.score >= 0 && 
               gameState.moves >= 0 && 
               !(gameState.hasWon && gameState.score == 0) // If won, should have some score
    }
    
    override fun isBoardStateValid(board: GameBoard): Boolean {
        val validation = board.validateBoardState()
        return validation.isValid
    }
    
    /**
     * Validate board state before performing any move
     */
    fun validateBoardBeforeMove(board: GameBoard): MoveValidationResult {
        val boardValidation = board.validateBoardState()
        
        if (!boardValidation.isValid) {
            return MoveValidationResult(
                isValid = false,
                reason = "Board state invalid: ${boardValidation.errors.joinToString(", ")}",
                suggestions = listOf("Check board integrity", "Restart game if corrupted")
            )
        }
        
        return MoveValidationResult(
            isValid = true,
            reason = "Board state is valid for moves"
        )
    }
    
    /**
     * Validate board state after performing a move
     */
    fun validateBoardAfterMove(board: GameBoard, moveDescription: String): MoveValidationResult {
        val boardValidation = board.validateBoardState()
        
        if (!boardValidation.isValid) {
            return MoveValidationResult(
                isValid = false,
                reason = "Board corrupted after $moveDescription: ${boardValidation.errors.joinToString(", ")}",
                suggestions = listOf("Undo the move", "Report this as a bug")
            )
        }
        
        return MoveValidationResult(
            isValid = true,
            reason = "Board state is valid after $moveDescription"
        )
    }
    
    /**
     * Check if any moves are available on the board
     */
    fun validateMovesAvailable(board: GameBoard): MoveValidationResult {
        val canMakeMoves = mergeDetector.canMakeMoves(board)
        
        return MoveValidationResult(
            isValid = canMakeMoves,
            reason = if (canMakeMoves) "Moves are available" else "No moves available - game over",
            suggestions = if (!canMakeMoves) {
                listOf("Game should end", "Check for win condition")
            } else emptyList()
        )
    }
}