package com.frageo.triswipe.data.preferences

import android.content.Context
import android.content.SharedPreferences
import androidx.core.content.edit
import com.frageo.triswipe.data.models.TileTheme
import com.frageo.triswipe.data.models.ThemeMode
import com.frageo.triswipe.data.models.SwapMode

class GamePreferences(context: Context, preferenceName: String = "triswipe_preferences") {

    private val preferences: SharedPreferences = context.getSharedPreferences(
        preferenceName,
        Context.MODE_PRIVATE
    )
    
    companion object {
        private const val KEY_FREE_GAMES_REMAINING = "free_games_remaining"
        private const val KEY_IS_PREMIUM_USER = "is_premium_user"
        private const val KEY_ANIMATION_SPEED = "animation_speed"
        private const val KEY_GESTURE_THRESHOLD = "gesture_threshold"
        private const val KEY_AUTO_SAVE_ENABLED = "auto_save_enabled"
        private const val KEY_SELECTED_THEME = "selected_theme"
        private const val KEY_THEME_MODE = "theme_mode"
        private const val KEY_SWAP_MODE = "swap_mode"
        
        private const val DEFAULT_FREE_GAMES = 30
        private const val DEFAULT_ANIMATION_SPEED = 1.0f
        private const val DEFAULT_GESTURE_THRESHOLD = 0.5f
        private const val DEFAULT_AUTO_SAVE = true
    }
    
    // Monetization
    fun getFreeGamesRemaining(): Int = preferences.getInt(KEY_FREE_GAMES_REMAINING, DEFAULT_FREE_GAMES)
    
    fun setFreeGamesRemaining(remaining: Int) {
        preferences.edit { putInt(KEY_FREE_GAMES_REMAINING, remaining) }
    }
    
    fun decrementFreeGames() {
        val current = getFreeGamesRemaining()
        if (current > 0) {
            setFreeGamesRemaining(current - 1)
        }
    }
    
    fun isPremiumUser(): Boolean = preferences.getBoolean(KEY_IS_PREMIUM_USER, false)
    
    fun setPremiumUser(isPremium: Boolean) {
        preferences.edit { putBoolean(KEY_IS_PREMIUM_USER, isPremium) }
    }
    
    // Settings
    fun getAnimationSpeed(): Float = preferences.getFloat(KEY_ANIMATION_SPEED, DEFAULT_ANIMATION_SPEED)
    
    fun setAnimationSpeed(speed: Float) {
        preferences.edit { putFloat(KEY_ANIMATION_SPEED, speed) }
    }
    
    fun getGestureThreshold(): Float = preferences.getFloat(KEY_GESTURE_THRESHOLD, DEFAULT_GESTURE_THRESHOLD)
    
    fun setGestureThreshold(threshold: Float) {
        preferences.edit { putFloat(KEY_GESTURE_THRESHOLD, threshold) }
    }
    
    // Auto-save
    fun isAutoSaveEnabled(): Boolean = preferences.getBoolean(KEY_AUTO_SAVE_ENABLED, DEFAULT_AUTO_SAVE)
    
    fun setAutoSaveEnabled(enabled: Boolean) {
        preferences.edit { putBoolean(KEY_AUTO_SAVE_ENABLED, enabled) }
    }
    
    // Themes
    fun getSelectedTheme(): TileTheme {
        val themeString = preferences.getString(KEY_SELECTED_THEME, TileTheme.CLASSIC.name)
        return try {
            TileTheme.valueOf(themeString ?: TileTheme.CLASSIC.name)
        } catch (e: IllegalArgumentException) {
            TileTheme.CLASSIC
        }
    }
    
    fun setSelectedTheme(theme: TileTheme) {
        preferences.edit { putString(KEY_SELECTED_THEME, theme.name) }
    }
    
    // Theme mode settings
    fun getThemeMode(): ThemeMode {
        val themeModeString = preferences.getString(KEY_THEME_MODE, ThemeMode.AUTO.name)
        return ThemeMode.fromString(themeModeString)
    }
    
    fun setThemeMode(themeMode: ThemeMode) {
        preferences.edit { putString(KEY_THEME_MODE, themeMode.name) }
    }
    
    // Swap Mode Settings
    fun getSwapMode(): SwapMode {
        val swapModeString = preferences.getString(KEY_SWAP_MODE, SwapMode.FREE_SWAPPING.name)
        return SwapMode.fromString(swapModeString)
    }
    
    fun setSwapMode(swapMode: SwapMode) {
        preferences.edit { putString(KEY_SWAP_MODE, swapMode.name) }
    }

    // Reset all preferences
    fun resetAll() {
        preferences.edit { clear() }
    }
}