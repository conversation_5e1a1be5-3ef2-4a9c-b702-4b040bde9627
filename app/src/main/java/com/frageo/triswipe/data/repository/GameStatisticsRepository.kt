package com.frageo.triswipe.data.repository

import com.frageo.triswipe.data.models.GameStats
import kotlinx.coroutines.flow.Flow

/**
 * Repository interface for game statistics persistence and operations.
 * Handles saving, loading, and updating game statistics data.
 */
interface GameStatisticsRepository {
    
    /**
     * Save comprehensive game statistics to persistent storage.
     */
    suspend fun saveGameStats(stats: GameStats)
    
    /**
     * Load current game statistics from persistent storage.
     * @return GameStats (never null - returns default if none exist)
     */
    suspend fun loadGameStats(): GameStats
    
    /**
     * Get a reactive Flow of game statistics for UI updates.
     * @return Flow<GameStats> that emits whenever statistics change
     */
    fun getGameStatsFlow(): Flow<GameStats>
    
    /**
     * Update high score if the provided score is higher than current.
     * @param score New score to potentially set as high score
     */
    suspend fun updateHighScore(score: Int)
    
    /**
     * Increment the total number of games played by 1.
     */
    suspend fun incrementGamesPlayed()
    
    /**
     * Increment the total number of games won by 1.
     */
    suspend fun incrementGamesWon()
    
    /**
     * Increment the total number of moves by 1.
     * Used for event-based move tracking.
     */
    suspend fun incrementTotalMoves()
    

    /**
     * Batch process multiple merge operations efficiently.
     * Updates total merges count and best tile value based on the provided tile values.
     * This method is optimized for background processing to reduce database operations.
     *
     * @param tileValues List of tile values created by merge operations
     */
    suspend fun addMerges(tileValues: List<Int>)

    /**
     * Record a game start event.
     * This is an alias for incrementGamesPlayed() but provides semantic clarity
     * for the event-driven statistics system.
     */
    suspend fun recordGameStart() = incrementGamesPlayed()
}