package com.frageo.triswipe.data.models

class MergeExecutor {
    
    fun executeMerges(board: GameBoard, mergeDetector: MergeDetector): MergeResult {
        val mergeGroups = mergeDetector.findMergeGroups(board)
        if (mergeGroups.isEmpty()) {
            return MergeResult(emptyList(), 0)
        }
        
        val mergeActions = mutableListOf<MergeAction>()
        var totalScore = 0
        
        for (mergeGroup in mergeGroups) {
            val mergeAction = executeMergeGroup(board, mergeGroup, mergeDetector)
            mergeActions.add(mergeAction)
            totalScore += mergeAction.scoreGained
        }
        
        return MergeResult(mergeActions, totalScore)
    }
    
    private fun executeMergeGroup(board: GameBoard, mergeGroup: MergeGroup, mergeDetector: MergeDetector): MergeAction {
        val centerPosition = mergeGroup.getCenterPosition()
        val newTileValue = GameConfig.getNextTileValue(mergeGroup.value)
        val newTile = Tile(newTileValue, centerPosition, origin = TileOrigin.MERGE)
        
        val removedTiles = mutableListOf<Tile>()
        
        for (position in mergeGroup.positions) {
            val tile = board.getTile(position)
            if (tile != null) {
                removedTiles.add(tile)
                board.setTile(position, null)
            }
        }
        
        board.setTile(centerPosition, newTile)
        
        val scoreGained = mergeDetector.getMergeScore(mergeGroup)
        
        // Animation data will be handled by the unified animation system
        
        return MergeAction(
            removedTiles = removedTiles,
            createdTile = newTile,
            scoreGained = scoreGained,
            mergePosition = centerPosition
        )
    }
    
    fun executeAutoMerges(board: GameBoard, mergeDetector: MergeDetector): AutoMergeResult {
        val allMergeActions = mutableListOf<MergeAction>()
        var totalScore = 0
        var mergeCount = 0
        
        while (mergeDetector.hasValidMerges(board)) {
            val mergeResult = executeMerges(board, mergeDetector)
            allMergeActions.addAll(mergeResult.mergeActions)
            totalScore += mergeResult.totalScore
            mergeCount++
            
            if (mergeCount > 10) {
                break
            }
        }
        
        return AutoMergeResult(
            mergeActions = allMergeActions,
            totalScore = totalScore,
            mergeCount = mergeCount
        )
    }
    
    fun canCreateChain(board: GameBoard, mergeDetector: MergeDetector): Boolean {
        val mergeGroups = mergeDetector.findMergeGroups(board)
        if (mergeGroups.isEmpty()) {
            return false
        }
        
        val tempBoard = GameBoard()
        copyBoardState(board, tempBoard)
        
        executeMerges(tempBoard, mergeDetector)
        
        return mergeDetector.hasValidMerges(tempBoard)
    }
    
    private fun copyBoardState(from: GameBoard, to: GameBoard) {
        to.clearBoard()
        for (row in 0 until GameConfig.BOARD_SIZE) {
            for (col in 0 until GameConfig.BOARD_SIZE) {
                val position = Position(row, col)
                val tile = from.getTile(position)
                if (tile != null) {
                    to.setTile(position, tile)
                }
            }
        }
    }
    
    fun simulateMergeChain(board: GameBoard, mergeDetector: MergeDetector): MergeChainSimulation {
        val tempBoard = GameBoard()
        copyBoardState(board, tempBoard)
        
        val chainSteps = mutableListOf<MergeResult>()
        var totalScore = 0
        var stepCount = 0
        
        while (mergeDetector.hasValidMerges(tempBoard) && stepCount < 10) {
            val mergeResult = executeMerges(tempBoard, mergeDetector)
            chainSteps.add(mergeResult)
            totalScore += mergeResult.totalScore
            stepCount++
        }
        
        return MergeChainSimulation(
            steps = chainSteps,
            totalScore = totalScore,
            finalBoard = tempBoard.copyBoard()
        )
    }
    
    fun getHighestTileValue(board: GameBoard): Int {
        var highestValue = 0
        for (row in 0 until GameConfig.BOARD_SIZE) {
            for (col in 0 until GameConfig.BOARD_SIZE) {
                val tile = board.getTile(Position(row, col))
                if (tile != null && tile.value > highestValue) {
                    highestValue = tile.value
                }
            }
        }
        return highestValue
    }
    
    fun checkWinCondition(board: GameBoard): Boolean {
        return getHighestTileValue(board) >= GameConfig.WIN_TILE_VALUE
    }
}

data class MergeAction(
    val removedTiles: List<Tile>,
    val createdTile: Tile,
    val scoreGained: Int,
    val mergePosition: Position
)

data class MergeResult(
    val mergeActions: List<MergeAction>,
    val totalScore: Int
)

data class AutoMergeResult(
    val mergeActions: List<MergeAction>,
    val totalScore: Int,
    val mergeCount: Int
)

data class MergeChainSimulation(
    val steps: List<MergeResult>,
    val totalScore: Int,
    val finalBoard: Array<Array<Tile?>>
)