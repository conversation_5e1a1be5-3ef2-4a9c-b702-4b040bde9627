package com.frageo.triswipe.data.database

import androidx.room.TypeConverter
import com.frageo.triswipe.data.models.Position
import com.frageo.triswipe.data.models.Tile
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

/**
 * Simple type converters using JSON serialization.
 */
class Converters {
    
    private val gson = Gson()
    
    @TypeConverter
    fun fromTileBoardArray(board: Array<Array<Tile?>>): String {
        return gson.toJson(board)
    }
    
    @TypeConverter
    fun toTileBoardArray(boardData: String): Array<Array<Tile?>> {
        return try {
            val cleanData = if (boardData.startsWith("JSON:")) {
                boardData.removePrefix("JSON:")
            } else {
                boardData // Legacy format (no prefix)
            }
            val type = object : TypeToken<Array<Array<Tile?>>>() {}.type
            gson.fromJson(cleanData, type)
        } catch (e: Exception) {
            // Return empty board on error
            Array(4) { Array<Tile?>(4) { null } }
        }
    }
    
    @TypeConverter
    fun fromTile(tile: Tile?): String? {
        return if (tile == null) null else gson.toJson(tile)
    }
    
    @TypeConverter
    fun toTile(tileJson: String?): Tile? {
        return if (tileJson == null) null else gson.fromJson(tileJson, Tile::class.java)
    }
    
    @TypeConverter
    fun fromPosition(position: Position): String {
        return gson.toJson(position)
    }
    
    @TypeConverter
    fun toPosition(positionJson: String): Position {
        return gson.fromJson(positionJson, Position::class.java)
    }
}