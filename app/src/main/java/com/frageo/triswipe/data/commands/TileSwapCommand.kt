package com.frageo.triswipe.data.commands

import com.frageo.triswipe.data.models.GameEngineInterface
import com.frageo.triswipe.data.models.Position
import java.util.UUID

/**
 * Command for handling tile swap actions (tap-to-swap functionality).
 * Encapsulates tile swapping logic and provides undo functionality.
 */
class TileSwapCommand(
    private val position1: Position,
    private val position2: Position
) : GameCommand {
    
    override val id: String = UUID.randomUUID().toString()
    override val description: String = "Swap tiles at (${position1.row},${position1.col}) and (${position2.row},${position2.col})"
    
    private var previousState: GameStateSnapshot? = null
    private var executed = false
    
    override fun execute(engine: GameEngineInterface): CommandResult {
        if (!canExecute(engine)) {
            return CommandResult.failure(
                message = "Cannot execute tile swap command",
                gameState = engine.getCurrentGameState()
            )
        }
        
        // Save the current state for undo
        previousState = captureGameState(engine)
        
        // Execute the swap using the existing engine logic
        val swapResult = engine.performTileSwap(position1, position2)
        executed = swapResult.success
        
        return if (swapResult.success) {
            CommandResult.success(
                message = "Tile swap completed",
                gameState = swapResult.gameState,
                scoreGained = swapResult.scoreGained,
                additionalData = mapOf(
                    "position1" to position1,
                    "position2" to position2,
                    "mergeActions" to swapResult.mergeActions
                )
            )
        } else {
            CommandResult.failure(
                message = swapResult.message,
                gameState = swapResult.gameState
            )
        }
    }
    
    override fun undo(engine: GameEngineInterface): CommandResult {
        if (!canUndo()) {
            return CommandResult.failure(
                message = "Cannot undo tile swap command - not executed or no previous state",
                gameState = engine.getCurrentGameState()
            )
        }
        
        val snapshot = previousState!!
        
        // Restore the previous state
        restoreGameState(engine, snapshot)
        
        return CommandResult.success(
            message = "Tile swap undone",
            gameState = engine.getCurrentGameState(),
            additionalData = mapOf(
                "position1" to position1,
                "position2" to position2,
                "undone" to true
            )
        )
    }
    
    override fun canExecute(engine: GameEngineInterface): Boolean {
        val currentState = engine.getCurrentGameState()
        
        // Cannot execute if game is over
        if (currentState.isGameOver) {
            return false
        }
        
        // Validate positions are within bounds
        if (!position1.isValid() || !position2.isValid()) {
            return false
        }
        
        // Check if positions are adjacent
        if (!arePositionsAdjacent(position1, position2)) {
            return false
        }
        
        // Use the engine's existing validation logic
        // This is a simplified check - the actual validation happens in execute()
        return true
    }
    
    override fun canUndo(): Boolean {
        return executed && previousState != null
    }
    
    /**
     * Check if two positions are adjacent (horizontally or vertically)
     */
    private fun arePositionsAdjacent(pos1: Position, pos2: Position): Boolean {
        val rowDiff = kotlin.math.abs(pos1.row - pos2.row)
        val colDiff = kotlin.math.abs(pos1.col - pos2.col)
        
        // Adjacent means exactly one step in one direction
        return (rowDiff == 1 && colDiff == 0) || (rowDiff == 0 && colDiff == 1)
    }
    
    /**
     * Capture the current game state for undo purposes
     */
    private fun captureGameState(engine: GameEngineInterface): GameStateSnapshot {
        val currentState = engine.getCurrentGameState()
        return GameStateSnapshot(
            boardState = currentState.board.map { row ->
                row.copyOf()
            }.toTypedArray(),
            score = currentState.score,
            moves = currentState.moves,
            isGameOver = currentState.isGameOver,
            hasWon = currentState.hasWon,
            totalMerges = 0, // This will be properly tracked when we integrate with GameEngine
            timestamp = System.currentTimeMillis()
        )
    }
    
    /**
     * Restore the game state from a snapshot
     */
    private fun restoreGameState(engine: GameEngineInterface, snapshot: GameStateSnapshot) {
        // Create a new GameState from the snapshot
        val restoredState = com.frageo.triswipe.data.models.GameState(
            board = snapshot.boardState,
            score = snapshot.score,
            moves = snapshot.moves,
            isGameOver = snapshot.isGameOver,
            hasWon = snapshot.hasWon
        )
        
        // Use the engine's existing restore functionality
        engine.setGameState(restoredState)
    }
    
    override fun toString(): String {
        return "TileSwapCommand(pos1=$position1, pos2=$position2, executed=$executed, canUndo=${canUndo()})"
    }
}