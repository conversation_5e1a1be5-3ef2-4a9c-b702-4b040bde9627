package com.frageo.triswipe.data.models

import java.util.UUID

enum class TileOrigin {
    SPAWN,  // New tile that spawned after a move
    MERGE,  // Tile created from merging other tiles
    EXISTING  // Tile that was already on the board
}

data class Tile(
    val value: Int,
    val position: Position,
    val id: String = UUID.randomUUID().toString(),
    val origin: TileOrigin = TileOrigin.EXISTING
) {
    fun withPosition(newPosition: Position): Tile {
        return copy(position = newPosition)
    }
    
    fun canMergeWith(other: Tile): Boolean {
        return value == other.value
    }
    
    fun merge(): Tile {
        return copy(value = value * 3, origin = TileOrigin.MERGE)
    }
    
    fun asSpawnTile(): Tile {
        return copy(origin = TileOrigin.SPAWN)
    }
    
    fun asExistingTile(): Tile {
        return copy(origin = TileOrigin.EXISTING)
    }
}