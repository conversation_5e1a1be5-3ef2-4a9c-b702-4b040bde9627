package com.frageo.triswipe.data.database

import androidx.room.*
import com.frageo.triswipe.data.repository.entities.GameStateEntity
import com.frageo.triswipe.data.repository.entities.GameStatsEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface GameDao {
    
    // Game State Operations
    @Query("SELECT * FROM game_state WHERE id = :id")
    suspend fun getGameState(id: String = "current_game"): GameStateEntity?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertGameState(gameState: GameStateEntity)
    
    @Query("DELETE FROM game_state WHERE id = :id")
    suspend fun deleteGameState(id: String = "current_game")
    
    // Game Statistics Operations
    @Query("SELECT * FROM game_stats WHERE id = :id")
    suspend fun getGameStats(id: String = "global_stats"): GameStatsEntity?
    
    @Query("SELECT * FROM game_stats WHERE id = :id")
    fun getGameStatsFlow(id: String = "global_stats"): Flow<GameStatsEntity?>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertGameStats(gameStats: GameStatsEntity)
    
    @Query("UPDATE game_stats SET highScore = :score, lastUpdated = :timestamp WHERE id = :id")
    suspend fun updateHighScore(score: Int, timestamp: Long = System.currentTimeMillis(), id: String = "global_stats")
    
    @Query("UPDATE game_stats SET gamesPlayed = gamesPlayed + 1, lastUpdated = :timestamp WHERE id = :id")
    suspend fun incrementGamesPlayed(timestamp: Long = System.currentTimeMillis(), id: String = "global_stats")
    
    @Query("UPDATE game_stats SET gamesWon = gamesWon + 1, lastUpdated = :timestamp WHERE id = :id")
    suspend fun incrementGamesWon(timestamp: Long = System.currentTimeMillis(), id: String = "global_stats")
    
    @Query("UPDATE game_stats SET totalMoves = totalMoves + 1, lastUpdated = :timestamp WHERE id = :id")
    suspend fun incrementTotalMoves(timestamp: Long = System.currentTimeMillis(), id: String = "global_stats")
    
    @Query("DELETE FROM game_stats WHERE id = :id")
    suspend fun deleteGameStats(id: String = "global_stats")
    
    @Query("DELETE FROM game_state")
    suspend fun clearAllGameStates()
    
    @Query("DELETE FROM game_stats")
    suspend fun clearAllGameStats()
}