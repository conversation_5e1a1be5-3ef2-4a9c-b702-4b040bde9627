package com.frageo.triswipe.data.models

import com.frageo.triswipe.data.commands.GameStateSnapshot
import com.frageo.triswipe.data.models.managers.UndoManager
import com.frageo.triswipe.data.models.managers.MoveAnalyzer
import com.frageo.triswipe.data.models.managers.GameValidator
import com.frageo.triswipe.data.models.managers.MoveExecutor
import com.frageo.triswipe.data.models.managers.MoveParams
import com.frageo.triswipe.data.models.managers.SwapModeManager
import com.frageo.triswipe.data.repository.GameStatisticsManager
import javax.inject.Inject

class GameEngine @Inject constructor(
    private val undoManager: UndoManager,
    private val moveAnalyzer: MoveAnalyzer,
    private val gameValidator: GameValidator,
    private val moveExecutor: MoveExecutor,
    private val mergeDetector: MergeDetector,
    private val mergeExecutor: MergeExecutor,
    private val statisticsManager: GameStatisticsManager,
    private val swapModeManager: SwapModeManager
) : GameEngineInterface {
    private val board = GameBoard()
    
    private var currentScore = 0
    private var currentMoves = 0
    private var isGameOver = false
    private var hasWon = false
    private var totalMerges = 0
    private var gameStartTime = 0L
    
    override fun initializeGame(): GameState {
        board.initializeBoard()
        currentScore = 0
        currentMoves = 0
        isGameOver = false
        hasWon = false
        totalMerges = 0
        gameStartTime = System.currentTimeMillis()
        
        // Clear undo history for new game
        undoManager.clearHistory()
        
        // Reset swap mode state for new game
        swapModeManager.resetForNewGame()
        
        return getCurrentGameState()
    }
    
    override fun performSwipe(direction: Direction): SwipeResult {
        // Save current state for undo functionality
        saveCurrentStateToHistory()
        
        // Execute swipe using MoveExecutor
        val result = moveExecutor.executeSwipe(direction, getCurrentGameState(), board)
        
        // Update internal game state from result
        if (result.success) {
            currentScore = result.gameState.score
            currentMoves = result.gameState.moves
            isGameOver = result.gameState.isGameOver
            hasWon = result.gameState.hasWon
            totalMerges += result.mergeActions.size

            // Update swap mode state after swipe
            swapModeManager.onSwipePerformed()
            
            // Check for tile upgrades and notify swap mode manager
            checkForTileUpgrades(result.mergeActions)

            // Emit merge events for statistics tracking
            emitMergeEvents(result.mergeActions)
            
            // Record score update for real-time high score tracking
            statisticsManager.recordScoreUpdate(currentScore)

            // Check win and game over conditions
            checkWinCondition()
            checkGameOverCondition()
            
            // Return result with updated game state
            return SwipeResult(
                gameState = getCurrentGameState(),
                success = result.success,
                message = result.message,
                tileMovements = result.tileMovements,
                mergeActions = result.mergeActions,
                scoreGained = result.scoreGained
            )
        }
        
        return result
    }
    
    override fun performTileSwap(pos1: Position, pos2: Position): SwapResult {
        // Check if swap is allowed by current swap mode
        val swapValidation = swapModeManager.isSwapAllowed(pos1, pos2, board, getCurrentGameState())
        if (!swapValidation.isValid) {
            return SwapResult(
                gameState = getCurrentGameState(),
                success = false,
                message = swapValidation.reason
            )
        }
        
        // Save current state for undo functionality
        saveCurrentStateToHistory()
        
        // Execute swap using MoveExecutor
        val result = moveExecutor.executeSwap(pos1, pos2, getCurrentGameState(), board)
        
        // Update internal game state from result
        if (result.success) {
            currentScore = result.gameState.score
            currentMoves = result.gameState.moves
            isGameOver = result.gameState.isGameOver
            hasWon = result.gameState.hasWon
            totalMerges += result.mergeActions.size

            // Update swap mode state after successful swap
            swapModeManager.onSwapPerformed(result.mergeActions)
            
            // Check for tile upgrades and notify swap mode manager
            checkForTileUpgrades(result.mergeActions)

            // Emit merge events for statistics tracking
            emitMergeEvents(result.mergeActions)
            
            // Record score update for real-time high score tracking
            statisticsManager.recordScoreUpdate(currentScore)

            // Check win and game over conditions
            checkWinCondition()
            checkGameOverCondition()
            
            // Return result with updated game state
            return SwapResult(
                gameState = getCurrentGameState(),
                success = result.success,
                message = result.message,
                mergeActions = result.mergeActions,
                scoreGained = result.scoreGained
            )
        }
        
        return result
    }
    
    private fun checkWinCondition() {
        if (!hasWon && mergeExecutor.checkWinCondition(board)) {
            hasWon = true
        }
    }
    
    private fun checkGameOverCondition() {
        if (!mergeDetector.canMakeMoves(board)) {
            isGameOver = true
        }
    }
    
    override fun getCurrentGameState(): GameState {
        return GameState(
            board = board.copyBoard(),
            score = currentScore,
            moves = currentMoves,
            isGameOver = isGameOver,
            hasWon = hasWon
        )
    }
    
    override fun setGameState(gameState: GameState) {
        board.restoreFromArray(gameState.board)
        currentScore = gameState.score
        currentMoves = gameState.moves
        isGameOver = gameState.isGameOver
        hasWon = gameState.hasWon
        // Reset tracking for restored game
        totalMerges = 0
        gameStartTime = System.currentTimeMillis()
    }
    
    // Undo functionality methods (premium feature) - delegated to UndoManager
    private fun saveCurrentStateToHistory() {
        val snapshot = GameStateSnapshot(
            boardState = board.copyBoard(),
            score = currentScore,
            moves = currentMoves,
            isGameOver = isGameOver,
            hasWon = hasWon,
            totalMerges = totalMerges
        )
        undoManager.saveState(snapshot)
    }
    
    override fun canUndo(): Boolean {
        return undoManager.canUndo()
    }
    
    override fun getUndoCount(): Int {
        return undoManager.getUndoCount()
    }
    
    override fun performUndo(): UndoResult {
        val result = undoManager.performUndo()
        
        if (result.success) {
            // Restore the game state from the undo result
            board.restoreFromArray(result.gameState.board)
            currentScore = result.gameState.score
            currentMoves = result.gameState.moves
            isGameOver = result.gameState.isGameOver
            hasWon = result.gameState.hasWon
            // Note: totalMerges is not restored to maintain current session tracking
        }
        
        return UndoResult(
            success = result.success,
            message = result.message,
            gameState = getCurrentGameState()
        )
    }
    
    fun clearUndoHistory() {
        undoManager.clearHistory()
    }
    
    fun updateTileOrigin(position: Position, newOrigin: TileOrigin) {
        board.updateTileOrigin(position, newOrigin)
    }
    
    fun validateGameState(): GameValidationResult {
        return gameValidator.validateGameState(getCurrentGameState(), board)
    }
    
    fun getAvailableMoves(): List<AvailableMove> {
        return moveAnalyzer.getAvailableMoves(board)
    }
    
    
    fun getBestMove(): AvailableMove? {
        return moveAnalyzer.getBestMove(board)
    }
    
    override fun getGameStats(): GameStatistics {
        return GameStatistics(
            score = currentScore,
            moves = currentMoves,
            highestTile = mergeExecutor.getHighestTileValue(board),
            emptySpaces = board.getEmptyPositions().size,
            availableMoves = moveAnalyzer.getAvailableMovesCount(board),
            canWin = !hasWon && mergeExecutor.checkWinCondition(board)
        )
    }
    
    override fun getTotalMerges(): Int = totalMerges
    
    override fun getPlayTimeMs(): Long = if (gameStartTime > 0) {
        System.currentTimeMillis() - gameStartTime
    } else {
        0L
    }
    
    fun performValidatedMove(moveType: MoveType, direction: Direction? = null, pos1: Position? = null, pos2: Position? = null): MoveResult {
        // Create move parameters based on type
        val params = when (moveType) {
            MoveType.SWIPE -> {
                if (direction == null) {
                    return MoveResult(
                        success = false,
                        message = "Direction required for swipe",
                        gameState = getCurrentGameState()
                    )
                }
                MoveParams.SwipeParams(direction)
            }
            MoveType.SWAP -> {
                if (pos1 == null || pos2 == null) {
                    return MoveResult(
                        success = false,
                        message = "Both positions required for swap",
                        gameState = getCurrentGameState()
                    )
                }
                MoveParams.SwapParams(pos1, pos2)
            }
        }
        
        // Save state for undo
        saveCurrentStateToHistory()
        
        // Execute validated move using MoveExecutor
        val result = moveExecutor.executeValidatedMove(moveType, params, getCurrentGameState(), board)
        
        // Update internal game state from result
        if (result.success) {
            currentScore = result.gameState.score
            currentMoves = result.gameState.moves
            isGameOver = result.gameState.isGameOver
            hasWon = result.gameState.hasWon
            totalMerges += result.mergeActions.size

            // Emit merge events for statistics tracking
            emitMergeEvents(result.mergeActions)
            
            // Record score update for real-time high score tracking
            statisticsManager.recordScoreUpdate(currentScore)

            // Check win and game over conditions
            checkWinCondition()
            checkGameOverCondition()
            
            // Return result with updated game state
            return MoveResult(
                success = result.success,
                message = result.message,
                gameState = getCurrentGameState(),
                tileMovements = result.tileMovements,
                mergeActions = result.mergeActions,
                scoreGained = result.scoreGained
            )
        }
        
        return result
    }
    
    fun restartGame(): GameState {
        return initializeGame()
    }

    /**
     * Emits merge events for statistics tracking.
     * This connects the game engine to the event-driven statistics system.
     */
    private fun emitMergeEvents(mergeActions: List<MergeAction>) {
        for (mergeAction in mergeActions) {
            // Record the tile value that was created by the merge
            statisticsManager.recordMerge(mergeAction.createdTile.value)
        }
    }
    
    /**
     * Check for tile upgrades and notify swap mode manager
     */
    private fun checkForTileUpgrades(mergeActions: List<MergeAction>) {
        for (mergeAction in mergeActions) {
            swapModeManager.onTileUpgrade(mergeAction.createdTile.value, currentMoves)
        }
    }
    
    /**
     * Get valid swap positions for the selected tile based on current swap mode
     */
    override fun getValidSwapPositions(selectedPosition: Position): List<Position> {
        return swapModeManager.getValidSwapPositions(selectedPosition, board, getCurrentGameState())
    }
    
    /**
     * Get current swap mode status message
     */
    override fun getSwapModeStatus(): String? {
        return swapModeManager.getStatusMessage()
    }
}

data class SwipeResult(
    val gameState: GameState,
    val success: Boolean,
    val message: String,
    val tileMovements: List<TileMovement> = emptyList(),
    val mergeActions: List<MergeAction> = emptyList(),
    val scoreGained: Int = 0
)

data class SwapResult(
    val gameState: GameState,
    val success: Boolean,
    val message: String,
    val mergeActions: List<MergeAction> = emptyList(),
    val scoreGained: Int = 0
)

data class AvailableMove(
    val type: MoveType,
    val direction: Direction? = null,
    val position1: Position? = null,
    val position2: Position? = null,
    val estimatedScore: Int = 0,
    val tileMovements: Int = 0
)

enum class MoveType {
    SWIPE, SWAP
}

data class GameStatistics(
    val score: Int,
    val moves: Int,
    val highestTile: Int,
    val emptySpaces: Int,
    val availableMoves: Int,
    val canWin: Boolean
)

data class GameValidationResult(
    val isValid: Boolean,
    val boardValidation: BoardValidationResult,
    val gameErrors: List<String>,
    val gameState: GameState
) {
    fun getAllErrors(): List<String> {
        return boardValidation.errors + gameErrors
    }
    
    fun getAllWarnings(): List<String> {
        return boardValidation.warnings
    }
}

data class MoveResult(
    val success: Boolean,
    val message: String,
    val gameState: GameState,
    val tileMovements: List<TileMovement> = emptyList(),
    val mergeActions: List<MergeAction> = emptyList(),
    val scoreGained: Int = 0
)


// Result class for undo operations
data class UndoResult(
    val success: Boolean,
    val message: String,
    val gameState: GameState
)