package com.frageo.triswipe.data.repository

import com.frageo.triswipe.data.database.GameDao
import com.frageo.triswipe.data.models.GameStats
import com.frageo.triswipe.data.preferences.GamePreferences
import com.frageo.triswipe.data.repository.entities.GameStatsEntity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Implementation of GameStatisticsRepository for game statistics persistence operations.
 * Uses Room database for statistics storage and GamePreferences for freemium data integration.
 */
@Singleton
class GameStatisticsRepositoryImpl @Inject constructor(
    private val gameDao: GameDao,
    private val gamePreferences: GamePreferences
) : GameStatisticsRepository {
    
    override suspend fun saveGameStats(stats: GameStats) {
        val entity = GameStatsEntity(
            highScore = stats.highScore,
            gamesPlayed = stats.gamesPlayed,
            gamesWon = stats.gamesWon,
            totalMoves = stats.totalMoves,
            highestTile = stats.highestTile,          // Use correct field
            bestTile = stats.bestTile,                // Add new field
            averageScore = stats.averageScore,        // Now Double, no conversion needed
            bestTime = stats.bestTime,                // Use actual value from stats
            totalPlayTime = stats.totalPlayTime,
            totalMerges = stats.totalMerges,
            longestWinStreak = stats.longestWinStreak,
            currentWinStreak = stats.currentWinStreak
        )
        
        gameDao.insertGameStats(entity)
    }
    
    override suspend fun loadGameStats(): GameStats {
        val entity = gameDao.getGameStats()
        return entity?.let {
            GameStats(
                highScore = it.highScore,
                gamesPlayed = it.gamesPlayed,
                gamesWon = it.gamesWon,
                totalMoves = it.totalMoves,
                totalMerges = it.totalMerges,
                highestTile = it.highestTile,            // Correct field mapping
                bestTile = it.bestTile,                  // Add new field
                averageScore = it.averageScore,          // Now Double, no conversion needed
                bestTime = it.bestTime,                  // Add missing field
                totalPlayTime = it.totalPlayTime,
                longestWinStreak = it.longestWinStreak,
                currentWinStreak = it.currentWinStreak
                // NOTE: Premium fields removed - handle separately via MonetizationRepository
            )
        } ?: GameStats() // Return default empty stats if none exist
    }
    
    override fun getGameStatsFlow(): Flow<GameStats> {
        return gameDao.getGameStatsFlow().map { entity ->
            entity?.let {
                GameStats(
                    highScore = it.highScore,
                    gamesPlayed = it.gamesPlayed,
                    gamesWon = it.gamesWon,
                    totalMoves = it.totalMoves,
                    totalMerges = it.totalMerges,
                    highestTile = it.highestTile,            // Correct field mapping
                    bestTile = it.bestTile,                  // Add new field
                    averageScore = it.averageScore,          // Now Double, no conversion needed
                    bestTime = it.bestTime,                  // Add missing field
                    totalPlayTime = it.totalPlayTime,
                    longestWinStreak = it.longestWinStreak,
                    currentWinStreak = it.currentWinStreak
                    // NOTE: Premium fields removed - handle separately via MonetizationRepository
                )
            } ?: GameStats()
        }
    }
    
    override suspend fun updateHighScore(score: Int) {
        // Ensure stats record exists
        val currentStats = gameDao.getGameStats()
        if (currentStats == null) {
            gameDao.insertGameStats(GameStatsEntity(highScore = score))
        } else if (score > currentStats.highScore) {
            gameDao.updateHighScore(score)
        }
    }
    
    override suspend fun incrementGamesPlayed() {
        // Ensure stats record exists
        val currentStats = gameDao.getGameStats()
        if (currentStats == null) {
            gameDao.insertGameStats(GameStatsEntity(gamesPlayed = 1))
        } else {
            gameDao.incrementGamesPlayed()
        }
    }
    
    override suspend fun incrementGamesWon() {
        // Ensure stats record exists
        val currentStats = gameDao.getGameStats()
        if (currentStats == null) {
            gameDao.insertGameStats(GameStatsEntity(gamesWon = 1))
        } else {
            gameDao.incrementGamesWon()
        }
    }
    
    override suspend fun incrementTotalMoves() {
        // Ensure stats record exists
        val currentStats = gameDao.getGameStats()
        if (currentStats == null) {
            gameDao.insertGameStats(GameStatsEntity(totalMoves = 1))
        } else {
            gameDao.incrementTotalMoves()
        }
    }

    override suspend fun addMerges(tileValues: List<Int>) = withContext(Dispatchers.IO) {
        if (tileValues.isEmpty()) return@withContext

        val current = loadGameStats()
        val mergeCount = tileValues.size
        val maxTileValue = tileValues.maxOrNull() ?: 0

        val updated = current.copy(
            totalMerges = current.totalMerges + mergeCount,
            bestTile = maxOf(current.bestTile, maxTileValue),
            highestTile = maxOf(current.highestTile, maxTileValue)  // Update both fields
        )

        saveGameStats(updated)
    }
}