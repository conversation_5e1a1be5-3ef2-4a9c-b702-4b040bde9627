package com.frageo.triswipe.data.models.managers

import com.frageo.triswipe.data.models.AvailableMove
import com.frageo.triswipe.data.models.GameBoard
import com.frageo.triswipe.data.models.Position
import com.frageo.triswipe.data.models.Direction
import com.frageo.triswipe.data.models.MoveType
import com.frageo.triswipe.data.models.GameConfig
import com.frageo.triswipe.data.models.MergeDetector
import com.frageo.triswipe.data.models.MergeExecutor
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Implementation of MoveAnalyzer for analyzing available moves and calculating optimal strategies.
 * 
 * Extracted from GameEngine to separate move analysis concerns and improve testability.
 * Provides AI-ready move analysis with scoring and simulation capabilities.
 */
@Singleton
class MoveAnalyzerImpl @Inject constructor(
    private val mergeDetector: MergeDetector,
    private val mergeExecutor: MergeExecutor
) : MoveAnalyzer {
    
    override fun getAvailableMoves(board: GameBoard): List<AvailableMove> {
        val availableMoves = mutableListOf<AvailableMove>()
        
        // Analyze swipe moves
        val directions = listOf(Direction.UP, Direction.DOWN, Direction.LEFT, Direction.RIGHT)
        for (direction in directions) {
            val tempBoard = GameBoard()
            copyBoardState(board, tempBoard)
            val movements = tempBoard.moveTiles(direction)
            
            if (movements.isNotEmpty()) {
                val simulation = mergeExecutor.simulateMergeChain(tempBoard, mergeDetector)
                availableMoves.add(
                    AvailableMove(
                        type = MoveType.SWIPE,
                        direction = direction,
                        estimatedScore = simulation.totalScore,
                        tileMovements = movements.size
                    )
                )
            }
        }
        
        // Analyze swap moves
        for (row in 0 until GameConfig.BOARD_SIZE) {
            for (col in 0 until GameConfig.BOARD_SIZE) {
                val pos1 = Position(row, col)
                val adjacentPositions = getAdjacentPositions(pos1)
                
                for (pos2 in adjacentPositions) {
                    // Only check each pair once by using ordering
                    if (pos1.row < pos2.row || (pos1.row == pos2.row && pos1.col < pos2.col)) {
                        val tempBoard = GameBoard()
                        copyBoardState(board, tempBoard)
                        
                        if (tempBoard.swapTiles(pos1, pos2)) {
                            val simulation = mergeExecutor.simulateMergeChain(tempBoard, mergeDetector)
                            if (simulation.totalScore > 0) {
                                availableMoves.add(
                                    AvailableMove(
                                        type = MoveType.SWAP,
                                        position1 = pos1,
                                        position2 = pos2,
                                        estimatedScore = simulation.totalScore
                                    )
                                )
                            }
                        }
                    }
                }
            }
        }
        
        return availableMoves.sortedByDescending { it.estimatedScore }
    }
    
    override fun getBestMove(board: GameBoard): AvailableMove? {
        return getAvailableMoves(board).firstOrNull()
    }
    
    override fun simulateMove(board: GameBoard, move: AvailableMove): MoveSimulationResult {
        val tempBoard = GameBoard()
        copyBoardState(board, tempBoard)
        
        val initialEmptySpaces = tempBoard.getEmptyPositions().size
        
        when (move.type) {
            MoveType.SWIPE -> {
                val direction = move.direction ?: return createFailedSimulation()
                val movements = tempBoard.moveTiles(direction)
                val simulation = mergeExecutor.simulateMergeChain(tempBoard, mergeDetector)
                
                val finalEmptySpaces = tempBoard.getEmptyPositions().size
                val wouldWin = mergeExecutor.checkWinCondition(tempBoard)
                val wouldLose = !mergeDetector.canMakeMoves(tempBoard)
                
                return MoveSimulationResult(
                    estimatedScore = simulation.totalScore,
                    tileMovements = movements.size,
                    mergeCount = simulation.steps.sumOf { it.mergeActions.size },
                    newEmptySpaces = finalEmptySpaces - initialEmptySpaces,
                    wouldWin = wouldWin,
                    wouldLose = wouldLose
                )
            }
            MoveType.SWAP -> {
                val pos1 = move.position1 ?: return createFailedSimulation()
                val pos2 = move.position2 ?: return createFailedSimulation()
                
                if (tempBoard.swapTiles(pos1, pos2)) {
                    val simulation = mergeExecutor.simulateMergeChain(tempBoard, mergeDetector)
                    
                    val finalEmptySpaces = tempBoard.getEmptyPositions().size
                    val wouldWin = mergeExecutor.checkWinCondition(tempBoard)
                    val wouldLose = !mergeDetector.canMakeMoves(tempBoard)
                    
                    return MoveSimulationResult(
                        estimatedScore = simulation.totalScore,
                        tileMovements = 2, // Swap always moves 2 tiles
                        mergeCount = simulation.steps.sumOf { it.mergeActions.size },
                        newEmptySpaces = finalEmptySpaces - initialEmptySpaces,
                        wouldWin = wouldWin,
                        wouldLose = wouldLose
                    )
                }
                return createFailedSimulation()
            }
        }
    }
    
    override fun getValidSwipeDirections(board: GameBoard): List<Direction> {
        val validDirections = mutableListOf<Direction>()
        val directions = listOf(Direction.UP, Direction.DOWN, Direction.LEFT, Direction.RIGHT)
        
        for (direction in directions) {
            val tempBoard = GameBoard()
            copyBoardState(board, tempBoard)
            val movements = tempBoard.moveTiles(direction)
            
            if (movements.isNotEmpty()) {
                validDirections.add(direction)
            }
        }
        
        return validDirections
    }
    
    override fun getValidSwapPositions(board: GameBoard): List<Pair<Position, Position>> {
        val validSwaps = mutableListOf<Pair<Position, Position>>()
        
        for (row in 0 until GameConfig.BOARD_SIZE) {
            for (col in 0 until GameConfig.BOARD_SIZE) {
                val pos1 = Position(row, col)
                val adjacentPositions = getAdjacentPositions(pos1)
                
                for (pos2 in adjacentPositions) {
                    // Only check each pair once by using ordering
                    if (pos1.row < pos2.row || (pos1.row == pos2.row && pos1.col < pos2.col)) {
                        val tempBoard = GameBoard()
                        copyBoardState(board, tempBoard)
                        
                        if (tempBoard.swapTiles(pos1, pos2)) {
                            val simulation = mergeExecutor.simulateMergeChain(tempBoard, mergeDetector)
                            if (simulation.totalScore > 0) {
                                validSwaps.add(Pair(pos1, pos2))
                            }
                        }
                    }
                }
            }
        }
        
        return validSwaps
    }
    
    override fun getAvailableMovesCount(board: GameBoard): Int {
        return getAvailableMoves(board).size
    }
    
    private fun getAdjacentPositions(position: Position): List<Position> {
        val adjacentPositions = mutableListOf<Position>()
        
        val directions = listOf(
            Position(-1, 0), // Up
            Position(1, 0),  // Down
            Position(0, -1), // Left
            Position(0, 1)   // Right
        )
        
        for (direction in directions) {
            val newRow = position.row + direction.row
            val newCol = position.col + direction.col
            val newPosition = Position(newRow, newCol)
            
            if (newPosition.isValid()) {
                adjacentPositions.add(newPosition)
            }
        }
        
        return adjacentPositions
    }
    
    private fun copyBoardState(from: GameBoard, to: GameBoard) {
        to.clearBoard()
        for (row in 0 until GameConfig.BOARD_SIZE) {
            for (col in 0 until GameConfig.BOARD_SIZE) {
                val position = Position(row, col)
                val tile = from.getTile(position)
                if (tile != null) {
                    to.setTile(position, tile)
                }
            }
        }
    }
    
    private fun createFailedSimulation(): MoveSimulationResult {
        return MoveSimulationResult(
            estimatedScore = 0,
            tileMovements = 0,
            mergeCount = 0,
            newEmptySpaces = 0,
            wouldWin = false,
            wouldLose = false
        )
    }
}