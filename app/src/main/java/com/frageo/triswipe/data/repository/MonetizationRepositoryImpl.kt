package com.frageo.triswipe.data.repository

import com.frageo.triswipe.data.preferences.GamePreferences
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Implementation of MonetizationRepository for premium features and billing management.
 * Uses SharedPreferences through GamePreferences for persistent storage of monetization data.
 */
@Singleton
class MonetizationRepositoryImpl @Inject constructor(
    private val gamePreferences: GamePreferences
) : MonetizationRepository {
    
    override suspend fun getFreeGamesRemaining(): Int {
        return gamePreferences.getFreeGamesRemaining()
    }
    
    override suspend fun setFreeGamesRemaining(count: Int) {
        gamePreferences.setFreeGamesRemaining(count)
    }
    
    override suspend fun decrementFreeGames() {
        gamePreferences.decrementFreeGames()
    }
    
    override suspend fun isPremiumUser(): Boolean {
        return gamePreferences.isPremiumUser()
    }
    
    override suspend fun setPremiumUser(isPremium: Boolean) {
        gamePreferences.setPremiumUser(isPremium)
    }
}