package com.frageo.triswipe.data.models

object GameConfig {
    const val BOARD_SIZE = 4
    const val MIN_TILES_FOR_MERGE = 3
    const val WIN_TILE_VALUE = 2187
    const val INITIAL_TILES_COUNT = 2
    
    val TILE_VALUES = listOf(1, 3, 9, 27, 81, 243, 729, 2187, 6561, 19683)
    
    val SPAWN_TILE_VALUES = listOf(1, 3)
    val SPAWN_PROBABILITIES = mapOf(
        1 to 0.75f,
        3 to 0.25f
    )
    
    const val MERGE_SCORE_MULTIPLIER = 10
    const val COMBO_SCORE_MULTIPLIER = 2
    
    const val SWIPE_THRESHOLD = 15f
    const val TAP_THRESHOLD = 200L
    
    const val ANIMATION_DURATION_MS = 150L
    const val TILE_SPAWN_DELAY_MS = 50L
    
    const val FREE_GAMES_LIMIT = 30
    const val PREMIUM_PRICE_CENTS = 199
    
    /**
     * Returns the next tile value in the progression sequence.
     * @param currentValue The current tile value
     * @return The next value in sequence, or the same value if at maximum
     */
    fun getNextTileValue(currentValue: Int): Int {
        val currentIndex = TILE_VALUES.indexOf(currentValue)
        return if (currentIndex != -1 && currentIndex < TILE_VALUES.size - 1) {
            TILE_VALUES[currentIndex + 1]
        } else {
            currentValue
        }
    }
    
    fun getTileColor(value: Int, theme: TileTheme = TileTheme.CLASSIC): String {
        return theme.getTileColor(value)
    }
    
    fun getTileTextColor(value: Int, theme: TileTheme = TileTheme.CLASSIC): String {
        return theme.getTileTextColor(value)
    }
    
    /**
     * Calculates score for a tile merge operation.
     * @param tileValue The value of the merged tile
     * @param comboCount Number of consecutive merges (for combo multiplier)
     * @return The calculated score points
     */
    fun calculateScore(tileValue: Int, comboCount: Int = 1): Int {
        return (tileValue * MERGE_SCORE_MULTIPLIER) * (comboCount * COMBO_SCORE_MULTIPLIER)
    }
    
    /**
     * Checks if a tile value represents a winning condition.
     * @param value The tile value to check
     * @return True if this tile value wins the game
     */
    fun isWinningTile(value: Int): Boolean {
        return value >= WIN_TILE_VALUE
    }
}