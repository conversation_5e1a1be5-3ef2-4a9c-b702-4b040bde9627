package com.frageo.triswipe.data.models.managers

import com.frageo.triswipe.data.models.GameValidationResult
import com.frageo.triswipe.data.models.SwipeValidationResult
import com.frageo.triswipe.data.models.SwapValidationResult
import com.frageo.triswipe.data.models.GameState
import com.frageo.triswipe.data.models.GameBoard
import com.frageo.triswipe.data.models.Direction
import com.frageo.triswipe.data.models.Position
import com.frageo.triswipe.data.models.MoveType

/**
 * Interface for validating game states and moves.
 * Handles comprehensive validation of board state, game state, and move legality.
 * 
 * Extracted from GameEngine to separate validation concerns and improve testability.
 */
interface GameValidator {
    
    /**
     * Validate the complete game state including board and game rules
     * @param gameState The game state to validate
     * @param board The game board to validate
     * @return Comprehensive validation result with errors and warnings
     */
    fun validateGameState(gameState: GameState, board: GameBoard): GameValidationResult
    
    /**
     * Pre-validate a swipe move before execution
     * @param board The game board to validate against
     * @param direction The swipe direction to validate
     * @return Validation result indicating if swipe is valid
     */
    fun validateSwipe(board: GameBoard, direction: Direction): SwipeValidationResult
    
    /**
     * Pre-validate a tile swap move before execution
     * @param board The game board to validate against
     * @param pos1 First position for swap
     * @param pos2 Second position for swap
     * @return Validation result indicating if swap is valid
     */
    fun validateSwap(board: GameBoard, pos1: Position, pos2: Position): SwapValidationResult
    
    /**
     * Validate a move based on type and parameters
     * @param board The game board to validate against
     * @param moveType The type of move to validate
     * @param params The move parameters (direction or positions)
     * @return Generic validation result for the move
     */
    fun validateMove(board: GameBoard, moveType: MoveType, params: MoveParams): MoveValidationResult
    
    /**
     * Check if the game state is consistent and valid
     * @param gameState The game state to check
     * @return true if game state is internally consistent
     */
    fun isGameStateConsistent(gameState: GameState): Boolean
    
    /**
     * Check if board state is corrupted or invalid
     * @param board The game board to check
     * @return true if board state is valid
     */
    fun isBoardStateValid(board: GameBoard): Boolean
}

/**
 * Parameters for move validation
 */
sealed class MoveParams {
    data class SwipeParams(val direction: Direction) : MoveParams()
    data class SwapParams(val pos1: Position, val pos2: Position) : MoveParams()
}

/**
 * Generic move validation result
 */
data class MoveValidationResult(
    val isValid: Boolean,
    val reason: String,
    val suggestions: List<String> = emptyList()
)