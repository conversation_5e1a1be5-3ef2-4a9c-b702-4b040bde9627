package com.frageo.triswipe.data.repository

import com.frageo.triswipe.data.events.StatisticsEvent
import com.frageo.triswipe.data.events.StatisticsEventProcessor
import com.frageo.triswipe.data.models.GameState
import com.frageo.triswipe.data.models.GameStats
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class GameStatisticsManager @Inject constructor(
    private val eventProcessor: StatisticsEventProcessor,
    private val monetizationRepository: MonetizationRepository
) {
    
    // MAIN THREAD: Non-blocking event emission for game tracking

    /**
     * Record a merge operation during gameplay.
     * This method is non-blocking and emits events for background processing.
     */
    fun recordMerge(tileValue: Int) {
        eventProcessor.processEvent(StatisticsEvent.MergePerformed(tileValue))
    }

    /**
     * Record when a new game starts.
     * This method is non-blocking and emits events for background processing.
     */
    fun recordGameStart() {
        eventProcessor.processEvent(StatisticsEvent.GameStarted)
    }

    /**
     * Record when a game is completed (won or lost).
     * This method is non-blocking and emits events for background processing.
     */
    fun recordGameCompletion(gameState: GameState, playTime: Long, merges: Int) {
        eventProcessor.processEvent(
            StatisticsEvent.GameCompleted(gameState, playTime, merges)
        )
    }

    /**
     * Record a score update during gameplay for real-time high score tracking.
     * This method is non-blocking and updates high score immediately if exceeded.
     */
    fun recordScoreUpdate(currentScore: Int) {
        val currentHighScore = getCurrentStatistics().highScore
        if (currentScore > currentHighScore) {
            eventProcessor.processEvent(StatisticsEvent.HighScoreAchieved(currentScore))
        }
    }

    /**
     * Record a move performed during gameplay.
     * This method is non-blocking and emits events for background processing.
     */
    fun recordMove(moveType: String) {
        eventProcessor.processEvent(StatisticsEvent.MovePerformed(moveType))
    }

    /**
     * Record when a free game is consumed.
     * This method is non-blocking and emits events for analytics and UI sync.
     */
    fun recordFreeGameConsumed(gamesRemaining: Int) {
        eventProcessor.processEvent(StatisticsEvent.FreeGameConsumed(gamesRemaining))
    }

    /**
     * Record when premium status changes.
     * This method is non-blocking and emits events for statistics and UI sync.
     */
    fun recordPremiumStatusChanged(isPremium: Boolean) {
        eventProcessor.processEvent(StatisticsEvent.PremiumStatusChanged(isPremium))
    }


    
    // UI binding - now uses real-time state from event processor
    fun getStatisticsFlow(): Flow<GameStats> = eventProcessor.currentStats

    // Immediate stats access - now uses real-time state
    fun getCurrentStatistics(): GameStats = eventProcessor.currentStats.value

    
    // NOTE: Premium/freemium logic moved to MonetizationRepository
    // GameStats no longer handles game availability checks
    
    // Decrement free games (for freemium)
    suspend fun consumeFreeGame() {
        if (!monetizationRepository.isPremiumUser()) {
            monetizationRepository.decrementFreeGames()
        }
    }
    
    // Get games remaining for free users
    suspend fun getFreeGamesRemaining(): Int {
        return monetizationRepository.getFreeGamesRemaining()
    }
}