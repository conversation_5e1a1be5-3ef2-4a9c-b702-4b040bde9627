package com.frageo.triswipe.data.events

import com.frageo.triswipe.data.models.GameState

/**
 * Sealed class hierarchy for statistics events in the TriSwipe game.
 * 
 * This event system enables non-blocking statistics tracking by emitting events
 * on the main thread and processing them asynchronously in the background.
 * This eliminates the 10-50ms database operation delays that were causing
 * frame drops during intense gameplay sequences.
 */
sealed class StatisticsEvent {
    
    /**
     * Event emitted when a merge operation is performed during gameplay.
     * 
     * @param tileValue The value of the tile created by the merge operation
     */
    data class MergePerformed(val tileValue: Int) : StatisticsEvent()
    
    /**
     * Event emitted when a new game is started.
     * Used to track games played statistics.
     */
    data object GameStarted : StatisticsEvent()
    
    /**
     * Event emitted when a game is completed (either won or lost).
     * 
     * @param gameState The final state of the completed game
     * @param playTime The total play time in milliseconds
     * @param sessionMerges The total number of merges performed during this game session
     */
    data class GameCompleted(
        val gameState: GameState,
        val playTime: Long,
        val sessionMerges: Int
    ) : StatisticsEvent()
    
    /**
     * Event emitted when the user's premium status changes.
     * Used to update statistics tracking and freemium game limits.
     * 
     * @param isPremium True if the user has premium status, false otherwise
     */
    data class PremiumStatusChanged(val isPremium: Boolean) : StatisticsEvent()
    
    /**
     * Event emitted when a new high score is achieved during gameplay.
     * Enables real-time high score updates in the UI.
     * 
     * @param newHighScore The new high score value
     */
    data class HighScoreAchieved(val newHighScore: Int) : StatisticsEvent()
    
    /**
     * Event emitted when a move is performed during gameplay.
     * Used to track total moves without blocking gameplay with database operations.
     * 
     * @param moveType The type of move performed ("SWIPE" or "TAP_SWAP")
     */
    data class MovePerformed(val moveType: String) : StatisticsEvent()
    
    /**
     * Event emitted when a free game is consumed by a non-premium user.
     * Used to track freemium usage analytics and sync UI state.
     * 
     * @param gamesRemaining The number of free games remaining after consumption
     */
    data class FreeGameConsumed(val gamesRemaining: Int) : StatisticsEvent()
}
