package com.frageo.triswipe.data.models

enum class TileTheme(
    val displayName: String,
    val isPremium: Boolean,
    val description: String
) {
    CLASSIC("Classic", false, "Original warm colors"),
    MIDNIGHT("Midnight", false, "Sleek dark blue-purple theme"),
    OCEAN("Ocean", true, "Cool blues and teals like ocean depths"),
    FOREST("Forest", true, "Rich greens with earthy brown accents"),
    SUNSET("Sunset", true, "Warm oranges and purples like twilight"),
    NEON("Neon", true, "Bright electric colors for high energy"),
    MONOCHROME("Monochrome", true, "Clean black-to-white progression");

    fun getTileColor(value: Int): String {
        return when (this) {
            CLASSIC -> getClassicColor(value)
            MIDNIGHT -> getMidnightColor(value)
            OCEAN -> getOceanColor(value)
            FOREST -> getForestColor(value)
            SUNSET -> getSunsetColor(value)
            NEON -> getNeonColor(value)
            MONOCHROME -> getMonochromeColor(value)
        }
    }

    fun getTileTextColor(value: Int): String {
        return when (this) {
            CLASSIC -> getClassicTextColor(value)
            MIDNIGHT -> getMidnightTextColor(value)
            OCEAN -> getOceanTextColor(value)
            FOREST -> getForestTextColor(value)
            SUNSET -> getSunsetTextColor(value)
            NEON -> getNeonTextColor(value)
            MONOCHROME -> getMonochromeTextColor(value)
        }
    }

    private fun getClassicColor(value: Int): String {
        return when (value) {
            1 -> "#F5F4F0"        // Light cream
            3 -> "#EFE4D3"        // Warm beige
            9 -> "#FFB366"        // Vibrant orange
            27 -> "#FF9A56"       // Rich orange
            81 -> "#FF7B5A"       // Orange-red
            243 -> "#FF5E5B"      // Bright red
            729 -> "#FFD93D"      // Golden yellow
            2187 -> "#6BCF7F"     // Fresh green
            6561 -> "#4DABF7"     // Sky blue
            19683 -> "#9775FA"    // Purple
            else -> "#495057"     // Dark gray
        }
    }

    private fun getMidnightColor(value: Int): String {
        return when (value) {
            1 -> "#1E1E2E"        // Deep dark blue-gray
            3 -> "#2A2A4A"        // Dark midnight blue
            9 -> "#3B3B6B"        // Medium dark blue
            27 -> "#4A4A7C"       // Deeper blue
            81 -> "#5D5D8D"       // Blue-purple
            243 -> "#7070A0"      // Light blue-purple
            729 -> "#8585B3"      // Soft blue-purple
            2187 -> "#9A9AC6"     // Light periwinkle
            6561 -> "#AFAFD9"     // Very light blue
            19683 -> "#C4C4EC"    // Pale blue
            else -> "#12121A"     // Very deep dark blue
        }
    }

    private fun getClassicTextColor(value: Int): String {
        return when (value) {
            1, 3 -> "#495057"         // Dark gray for light tiles
            9, 27, 81, 243, 729 -> "#FFFFFF"  // White for warm colors
            else -> "#FFFFFF"         // White for all other tiles
        }
    }

    private fun getMidnightTextColor(value: Int): String {
        return when (value) {
            1, 3, 9, 27, 81, 243 -> "#FFFFFF"      // White for darker blue tiles
            729, 2187 -> "#1A1A2A"                  // Dark blue for medium tiles  
            6561, 19683 -> "#12121A"                // Very dark blue for lightest tiles
            else -> "#FFFFFF"                       // White for fallback
        }
    }

    // Ocean Theme - Cool blues and teals like ocean depths
    private fun getOceanColor(value: Int): String {
        return when (value) {
            1 -> "#0D1B2A"        // Deep ocean blue
            3 -> "#1B263B"        // Dark navy
            9 -> "#2C4E80"        // Deep blue
            27 -> "#4A90B8"       // Medium blue
            81 -> "#5FB3D4"       // Light blue
            243 -> "#7AC7E3"      // Sky blue
            729 -> "#96D9F2"      // Light cyan
            2187 -> "#B2EBFF"     // Very light blue
            6561 -> "#CCF2FF"     // Pale cyan
            19683 -> "#E6F9FF"    // Almost white blue
            else -> "#041E2A"     // Very deep ocean
        }
    }

    private fun getOceanTextColor(value: Int): String {
        return when (value) {
            1, 3, 9, 27 -> "#FFFFFF"               // White for dark ocean
            81, 243 -> "#0D1B2A"                   // Deep blue for medium
            else -> "#041E2A"                      // Very deep blue for light
        }
    }

    // Forest Theme - Rich greens with earthy brown accents
    private fun getForestColor(value: Int): String {
        return when (value) {
            1 -> "#1B2A1A"        // Deep forest green
            3 -> "#2D4A2B"        // Dark green
            9 -> "#3E6B3C"        // Medium forest green
            27 -> "#5A8C58"       // Fresh green
            81 -> "#76AD74"       // Light green
            243 -> "#92CE90"      // Bright green
            729 -> "#AEEFAC"      // Very light green
            2187 -> "#C4F0C2"     // Pale green
            6561 -> "#DAF6D8"     // Very pale green
            19683 -> "#F0FCF0"    // Almost white green
            else -> "#0F1A0E"     // Very deep forest
        }
    }

    private fun getForestTextColor(value: Int): String {
        return when (value) {
            1, 3, 9, 27 -> "#FFFFFF"               // White for dark forest
            81, 243 -> "#1B2A1A"                   // Deep green for medium
            else -> "#0F1A0E"                      // Very deep green for light
        }
    }

    // Sunset Theme - Warm oranges and purples like twilight
    private fun getSunsetColor(value: Int): String {
        return when (value) {
            1 -> "#2A1A2D"        // Deep purple night
            3 -> "#4A1F3E"        // Dark purple
            9 -> "#6B2C5F"        // Medium purple
            27 -> "#8C3A80"       // Bright purple
            81 -> "#AD52A1"       // Light purple
            243 -> "#CE70C2"      // Pink-purple
            729 -> "#EF8EE3"      // Light pink
            2187 -> "#F2A6D4"     // Pale pink
            6561 -> "#F5BED5"     // Very pale pink
            19683 -> "#F8D6D6"    // Almost white pink
            else -> "#1A0E1D"     // Very deep night
        }
    }

    private fun getSunsetTextColor(value: Int): String {
        return when (value) {
            1, 3, 9, 27 -> "#FFFFFF"               // White for dark sunset
            81, 243 -> "#2A1A2D"                   // Deep purple for medium
            else -> "#1A0E1D"                      // Very deep purple for light
        }
    }

    // Neon Theme - Bright electric colors for high energy
    private fun getNeonColor(value: Int): String {
        return when (value) {
            1 -> "#0A0A0A"        // Deep black
            3 -> "#1A001A"        // Dark magenta
            9 -> "#330033"        // Medium magenta
            27 -> "#FF00FF"       // Bright magenta
            81 -> "#FF3399"       // Hot pink
            243 -> "#FF66CC"      // Light pink
            729 -> "#FF99FF"      // Neon pink
            2187 -> "#FFCCFF"     // Pale neon
            6561 -> "#FFE6FF"     // Very pale neon
            19683 -> "#FFF0FF"    // Almost white neon
            else -> "#000000"     // Pure black
        }
    }

    private fun getNeonTextColor(value: Int): String {
        return when (value) {
            1, 3, 9 -> "#FFFFFF"                   // White for black/dark
            27 -> "#000000"                        // Black for bright magenta
            81, 243 -> "#0A0A0A"                   // Deep black for bright colors
            else -> "#000000"                      // Black for light neon
        }
    }

    // Monochrome Theme - Clean black-to-white progression
    private fun getMonochromeColor(value: Int): String {
        return when (value) {
            1 -> "#000000"        // Pure black
            3 -> "#1A1A1A"        // Very dark gray
            9 -> "#333333"        // Dark gray
            27 -> "#4D4D4D"       // Medium-dark gray
            81 -> "#666666"       // Medium gray
            243 -> "#808080"      // Light-medium gray
            729 -> "#999999"      // Light gray
            2187 -> "#B3B3B3"     // Very light gray
            6561 -> "#CCCCCC"     // Pale gray
            19683 -> "#E6E6E6"    // Almost white
            else -> "#000000"     // Pure black
        }
    }

    private fun getMonochromeTextColor(value: Int): String {
        return when (value) {
            1, 3, 9, 27, 81 -> "#FFFFFF"           // White for dark grays
            else -> "#000000"                      // Black for light grays
        }
    }

    fun getBoardBackgroundColors(): Pair<String, String> {
        return when (this) {
            CLASSIC -> Pair("#BBADA0", "#A49389")        // Original warm brown gradient
            MIDNIGHT -> Pair("#2A2A4A", "#1E1E2E")       // Deep blue-purple gradient matching tiles
            OCEAN -> Pair("#1B263B", "#0D1B2A")          // Deep ocean blue gradient
            FOREST -> Pair("#2D4A2B", "#1B2A1A")         // Rich forest green gradient
            SUNSET -> Pair("#4A1F3E", "#2A1A2D")         // Deep purple-night gradient
            NEON -> Pair("#1A001A", "#0A0A0A")           // Dark with subtle magenta undertones
            MONOCHROME -> Pair("#333333", "#1A1A1A")     // Clean dark gray gradient
        }
    }

    fun getBoardAccentColor(): String {
        return when (this) {
            CLASSIC -> "#8F7A66"      // Warm brown accent
            MIDNIGHT -> "#7070A0"     // Light blue-purple accent
            OCEAN -> "#4A90B8"        // Medium blue accent
            FOREST -> "#5A8C58"       // Fresh green accent
            SUNSET -> "#8C3A80"       // Bright purple accent
            NEON -> "#FF00FF"         // Bright magenta accent
            MONOCHROME -> "#666666"   // Medium gray accent
        }
    }

    companion object {
        fun getAvailableThemes(isPremium: Boolean): List<TileTheme> {
            return if (isPremium) {
                values().toList()
            } else {
                listOf(CLASSIC, MIDNIGHT)
            }
        }
    }
}