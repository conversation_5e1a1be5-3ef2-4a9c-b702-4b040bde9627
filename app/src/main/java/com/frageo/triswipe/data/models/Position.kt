package com.frageo.triswipe.data.models

data class Position(
    val row: Int,
    val col: Int
) {
    fun isValid(boardSize: Int = 4): <PERSON>olean {
        return row in 0 until boardSize && col in 0 until boardSize
    }
    
    fun isAdjacent(other: Position): Bo<PERSON>an {
        val rowDiff = kotlin.math.abs(row - other.row)
        val colDiff = kotlin.math.abs(col - other.col)
        return (rowDiff == 1 && colDiff == 0) || (rowDiff == 0 && colDiff == 1)
    }
}