package com.frageo.triswipe.data.models.managers

import com.frageo.triswipe.data.models.AvailableMove
import com.frageo.triswipe.data.models.GameBoard
import com.frageo.triswipe.data.models.Position
import com.frageo.triswipe.data.models.Direction
import com.frageo.triswipe.data.models.MoveType

/**
 * Interface for analyzing available moves and calculating optimal strategies.
 * Handles move discovery, scoring, and simulation for AI and hint systems.
 * 
 * Extracted from GameEngine to separate move analysis concerns.
 */
interface MoveAnalyzer {
    
    /**
     * Get all available moves on the current board
     * @param board The game board to analyze
     * @return List of available moves sorted by estimated score (descending)
     */
    fun getAvailableMoves(board: GameBoard): List<AvailableMove>
    
    /**
     * Get the best available move based on scoring algorithm
     * @param board The game board to analyze
     * @return The highest-scoring available move, or null if no moves available
     */
    fun getBestMove(board: GameBoard): AvailableMove?
    
    /**
     * Simulate the outcome of a move without modifying the board
     * @param board The game board to simulate on
     * @param move The move to simulate
     * @return Simulation result with score and board state changes
     */
    fun simulateMove(board: GameBoard, move: AvailableMove): MoveSimulationResult
    
    /**
     * Get all valid swipe directions from current board state
     * @param board The game board to analyze
     * @return List of directions that would result in tile movement
     */
    fun getValidSwipeDirections(board: GameBoard): List<Direction>
    
    /**
     * Get all valid tile swap positions from current board state
     * @param board The game board to analyze
     * @return List of position pairs that can be swapped
     */
    fun getValidSwapPositions(board: GameBoard): List<Pair<Position, Position>>
    
    /**
     * Count total number of available moves
     * @param board The game board to analyze
     * @return Total count of available moves (swipes + swaps)
     */
    fun getAvailableMovesCount(board: GameBoard): Int
}

/**
 * Result of simulating a move on the game board
 */
data class MoveSimulationResult(
    val estimatedScore: Int,
    val tileMovements: Int,
    val mergeCount: Int,
    val newEmptySpaces: Int,
    val wouldWin: Boolean,
    val wouldLose: Boolean
)