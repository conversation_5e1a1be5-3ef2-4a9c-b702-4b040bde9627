package com.frageo.triswipe.data.repository

import com.frageo.triswipe.data.models.TileTheme
import com.frageo.triswipe.data.models.ThemeMode
import com.frageo.triswipe.data.models.SwapMode
import com.frageo.triswipe.data.preferences.GamePreferences
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Implementation of UserPreferencesRepository for user settings and preferences management.
 * Uses SharedPreferences through GamePreferences for persistent storage.
 */
@Singleton
class UserPreferencesRepositoryImpl @Inject constructor(
    private val gamePreferences: GamePreferences
) : UserPreferencesRepository {
    
    // Animation Settings
    override suspend fun getAnimationSpeed(): Float {
        return gamePreferences.getAnimationSpeed()
    }
    
    override suspend fun setAnimationSpeed(speed: Float) {
        gamePreferences.setAnimationSpeed(speed)
    }
    
    // Gesture Settings
    override suspend fun getGestureThreshold(): Float {
        return gamePreferences.getGestureThreshold()
    }
    
    override suspend fun setGestureThreshold(threshold: Float) {
        gamePreferences.setGestureThreshold(threshold)
    }
    
    // Auto-save Settings
    override suspend fun enableAutoSave(enable: Boolean) {
        gamePreferences.setAutoSaveEnabled(enable)
    }
    
    override suspend fun isAutoSaveEnabled(): Boolean {
        return gamePreferences.isAutoSaveEnabled()
    }
    
    // Theme Settings
    override suspend fun getSelectedTheme(): TileTheme {
        return gamePreferences.getSelectedTheme()
    }
    
    override suspend fun setSelectedTheme(theme: TileTheme) {
        gamePreferences.setSelectedTheme(theme)
    }
    
    override suspend fun getThemeMode(): ThemeMode {
        return gamePreferences.getThemeMode()
    }
    
    override suspend fun setThemeMode(themeMode: ThemeMode) {
        gamePreferences.setThemeMode(themeMode)
    }
    
    // Swap Mode Settings
    override suspend fun getSwapMode(): SwapMode {
        return gamePreferences.getSwapMode()
    }
    
    override suspend fun setSwapMode(swapMode: SwapMode) {
        gamePreferences.setSwapMode(swapMode)
    }
}