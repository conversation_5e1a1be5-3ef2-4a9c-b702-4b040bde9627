package com.frageo.triswipe.data.models.managers

import com.frageo.triswipe.data.commands.GameStateSnapshot
import com.frageo.triswipe.data.models.UndoResult
import com.frageo.triswipe.data.models.GameState
import com.frageo.triswipe.data.models.Tile
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Implementation of UndoManager for managing game undo/redo functionality.
 * 
 * Extracted from GameEngine to separate undo concerns and improve testability.
 * Maintains a history of game state snapshots with configurable size limits.
 */
@Singleton
class UndoManagerImpl @Inject constructor() : UndoManager {
    
    private val gameStateHistory = mutableListOf<GameStateSnapshot>()
    private var maxHistorySize = 10 // Default to 10 moves for undo
    
    override fun saveState(snapshot: GameStateSnapshot) {
        gameStateHistory.add(snapshot)
        
        // Keep only the last maxHistorySize states
        if (gameStateHistory.size > maxHistorySize) {
            gameStateHistory.removeAt(0)
        }
    }
    
    override fun canUndo(): Bo<PERSON>an {
        return gameStateHistory.isNotEmpty()
    }
    
    override fun getUndoCount(): Int {
        return gameStateHistory.size
    }
    
    override fun performUndo(): UndoResult {
        if (gameStateHistory.isEmpty()) {
            return UndoResult(
                success = false,
                message = "No moves to undo",
                gameState = createEmptyGameState()
            )
        }
        
        val previousState = gameStateHistory.removeLastOrNull()
            ?: return UndoResult(
                success = false,
                message = "Failed to retrieve previous state",
                gameState = createEmptyGameState()
            )
        
        // Convert snapshot back to GameState
        val restoredGameState = GameState(
            board = previousState.boardState,
            score = previousState.score,
            moves = previousState.moves,
            isGameOver = previousState.isGameOver,
            hasWon = previousState.hasWon
        )
        
        return UndoResult(
            success = true,
            message = "Move undone successfully",
            gameState = restoredGameState
        )
    }
    
    override fun clearHistory() {
        gameStateHistory.clear()
    }
    
    override fun getMaxHistorySize(): Int {
        return maxHistorySize
    }
    
    override fun setMaxHistorySize(maxSize: Int) {
        require(maxSize > 0) { "Max history size must be positive" }
        maxHistorySize = maxSize
        
        // Trim history if new size is smaller
        while (gameStateHistory.size > maxHistorySize) {
            gameStateHistory.removeAt(0)
        }
    }
    
    /**
     * Get the current history for debugging/testing
     */
    fun getHistory(): List<GameStateSnapshot> {
        return gameStateHistory.toList()
    }
    
    /**
     * Get the most recent snapshot without removing it
     */
    fun peekLastSnapshot(): GameStateSnapshot? {
        return gameStateHistory.lastOrNull()
    }
    
    private fun createEmptyGameState(): GameState {
        // Create an empty 4x4 board with all null tiles
        val emptyBoard = Array(4) { Array<Tile?>(4) { null } }
        return GameState(
            board = emptyBoard,
            score = 0,
            moves = 0,
            isGameOver = false,
            hasWon = false
        )
    }
}