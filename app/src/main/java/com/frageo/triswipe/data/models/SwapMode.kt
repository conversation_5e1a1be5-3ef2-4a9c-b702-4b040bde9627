package com.frageo.triswipe.data.models

/**
 * Defines different swap modes that control how tile swapping behaves in the game.
 * Each mode provides different strategic constraints and gameplay experiences.
 */
enum class SwapMode(
    val displayName: String,
    val description: String,
    val isPremiumFeature: Boolean = false
) {
    /**
     * Default mode - players can swap any adjacent tiles freely
     */
    FREE_SWAPPING(
        displayName = "Free Swapping",
        description = "Swap any adjacent tiles freely"
    ),
    
    /**
     * Strategic mode - swaps must result in at least one merge
     */
    MERGE_ONLY(
        displayName = "Merge-Only Swapping",
        description = "Swaps must create at least one merge",
        isPremiumFeature = true
    ),
    
    /**
     * Limited mode - only one swap allowed between swipe moves
     */
    ONE_SWAP_PER_TURN(
        displayName = "One Swap Per Turn",
        description = "One swap allowed between swipes",
        isPremiumFeature = true
    ),
    
    /**
     * Achievement mode - swap only unlocked after creating new tile values
     */
    SWAP_ON_UPGRADE(
        displayName = "Swap on Upgrade",
        description = "Swap unlocked after reaching new tile values",
        isPremiumFeature = true
    ),
    
    /**
     * Pure swipe mode - no swapping allowed, swipe gestures only
     */
    NO_SWAPPING(
        displayName = "No Swapping",
        description = "Swipe gestures only, no tile swapping",
        isPremiumFeature = true
    );
    
    companion object {
        /**
         * Get SwapMode from string, with fallback to FREE_SWAPPING
         */
        fun fromString(value: String?): SwapMode {
            return try {
                valueOf(value ?: FREE_SWAPPING.name)
            } catch (e: IllegalArgumentException) {
                FREE_SWAPPING
            }
        }
        
        /**
         * Get all available swap modes for the current user
         */
        fun getAvailableModes(isPremiumUser: Boolean): List<SwapMode> {
            return if (isPremiumUser) {
                values().toList()
            } else {
                listOf(FREE_SWAPPING)
            }
        }
        
        /**
         * Get the default swap mode for new users
         */
        fun getDefault(): SwapMode = FREE_SWAPPING
    }
}

/**
 * State tracking for swap modes that have per-game or per-turn limitations
 */
data class SwapModeState(
    val currentMode: SwapMode = SwapMode.FREE_SWAPPING,
    val swapsUsedThisTurn: Int = 0,
    val swapUnlockedForTurn: Boolean = true,
    val highestTileValueAchieved: Int = 1,
    val lastUpgradeMove: Int = 0
) {
    /**
     * Check if swapping is currently allowed based on the mode and state
     */
    fun isSwappingAllowed(currentMoves: Int): Boolean {
        return when (currentMode) {
            SwapMode.FREE_SWAPPING -> true
            SwapMode.MERGE_ONLY -> true // Validation happens at swap execution
            SwapMode.ONE_SWAP_PER_TURN -> swapsUsedThisTurn == 0
            SwapMode.SWAP_ON_UPGRADE -> swapUnlockedForTurn
            SwapMode.NO_SWAPPING -> false
        }
    }
    
    /**
     * Update state after a swap is performed
     */
    fun afterSwap(): SwapModeState {
        return when (currentMode) {
            SwapMode.ONE_SWAP_PER_TURN -> copy(swapsUsedThisTurn = swapsUsedThisTurn + 1)
            SwapMode.SWAP_ON_UPGRADE -> copy(swapUnlockedForTurn = false)
            else -> this
        }
    }
    
    /**
     * Update state after a swipe is performed (resets turn-based limitations)
     */
    fun afterSwipe(): SwapModeState {
        return when (currentMode) {
            SwapMode.ONE_SWAP_PER_TURN -> copy(swapsUsedThisTurn = 0)
            else -> this
        }
    }
    
    /**
     * Update state when a new tile value is achieved
     */
    fun onTileUpgrade(newTileValue: Int, currentMoves: Int): SwapModeState {
        return if (newTileValue > highestTileValueAchieved) {
            copy(
                highestTileValueAchieved = newTileValue,
                lastUpgradeMove = currentMoves,
                swapUnlockedForTurn = currentMode == SwapMode.SWAP_ON_UPGRADE
            )
        } else {
            this
        }
    }
    
    /**
     * Get user-friendly status message for current swap availability
     */
    fun getStatusMessage(): String? {
        return when (currentMode) {
            SwapMode.FREE_SWAPPING -> null
            SwapMode.MERGE_ONLY -> "Swaps must create merges"
            SwapMode.ONE_SWAP_PER_TURN -> {
                if (swapsUsedThisTurn > 0) "Swap used this turn - swipe to reset"
                else "One swap available this turn"
            }
            SwapMode.SWAP_ON_UPGRADE -> {
                if (swapUnlockedForTurn) "Swap unlocked! Use it wisely"
                else "Reach a new tile value to unlock swap"
            }
            SwapMode.NO_SWAPPING -> "Swapping disabled - swipe only"
        }
    }
}