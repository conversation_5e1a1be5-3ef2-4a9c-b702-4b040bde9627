package com.frageo.triswipe.data.models.managers

import com.frageo.triswipe.data.models.SwapMode
import com.frageo.triswipe.data.models.SwapModeState
import com.frageo.triswipe.data.models.Position
import com.frageo.triswipe.data.models.GameBoard
import com.frageo.triswipe.data.models.GameState
import com.frageo.triswipe.data.models.MergeDetector
import com.frageo.triswipe.data.models.wouldSwapCreateMerges
import com.frageo.triswipe.data.repository.UserPreferencesRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Manages swap mode logic and state tracking.
 * Handles validation, state updates, and user feedback for different swap modes.
 */
@Singleton
class SwapModeManager @Inject constructor(
    private val userPreferencesRepository: UserPreferencesRepository,
    private val mergeDetector: MergeDetector
) {
    
    private val _swapModeState = MutableStateFlow(SwapModeState())
    val swapModeState: StateFlow<SwapModeState> = _swapModeState.asStateFlow()
    
    /**
     * Initialize the swap mode manager with current settings
     */
    suspend fun initialize() {
        val currentMode = userPreferencesRepository.getSwapMode()
        _swapModeState.value = SwapModeState(currentMode = currentMode)
    }
    
    /**
     * Update the current swap mode
     */
    suspend fun setSwapMode(newMode: SwapMode) {
        userPreferencesRepository.setSwapMode(newMode)
        _swapModeState.value = _swapModeState.value.copy(
            currentMode = newMode,
            swapsUsedThisTurn = 0,
            swapUnlockedForTurn = newMode != SwapMode.SWAP_ON_UPGRADE
        )
    }
    
    /**
     * Check if a swap is allowed based on current mode and state
     */
    fun isSwapAllowed(pos1: Position, pos2: Position, board: GameBoard, gameState: GameState): SwapValidationResult {
        val currentState = _swapModeState.value
        
        // First check if swapping is allowed by the current mode state
        if (!currentState.isSwappingAllowed(gameState.moves)) {
            return SwapValidationResult(
                isValid = false,
                reason = getSwapDeniedReason(currentState),
                canRetryLater = canRetrySwapLater(currentState)
            )
        }
        
        // Basic position validation
        if (!pos1.isValid() || !pos2.isValid() || !pos1.isAdjacent(pos2)) {
            return SwapValidationResult(
                isValid = false,
                reason = "Positions must be adjacent and valid"
            )
        }
        
        // Mode-specific validation
        return when (currentState.currentMode) {
            SwapMode.FREE_SWAPPING -> validateFreeSwap(pos1, pos2, board)
            SwapMode.MERGE_ONLY -> validateMergeOnlySwap(pos1, pos2, board)
            SwapMode.ONE_SWAP_PER_TURN -> validateOneSwapPerTurn(pos1, pos2, board, currentState)
            SwapMode.SWAP_ON_UPGRADE -> validateSwapOnUpgrade(pos1, pos2, board, currentState)
            SwapMode.NO_SWAPPING -> SwapValidationResult(
                isValid = false,
                reason = "Swapping is disabled in this mode"
            )
        }
    }
    
    /**
     * Update state after a successful swap
     */
    fun onSwapPerformed(mergeActions: List<com.frageo.triswipe.data.models.MergeAction>) {
        _swapModeState.value = _swapModeState.value.afterSwap()
    }
    
    /**
     * Update state after a swipe move
     */
    fun onSwipePerformed() {
        _swapModeState.value = _swapModeState.value.afterSwipe()
    }
    
    /**
     * Update state when new tile values are achieved
     */
    fun onTileUpgrade(newTileValue: Int, currentMoves: Int) {
        _swapModeState.value = _swapModeState.value.onTileUpgrade(newTileValue, currentMoves)
    }
    
    /**
     * Get available swap positions based on current mode
     */
    fun getValidSwapPositions(selectedPosition: Position, board: GameBoard, gameState: GameState): List<Position> {
        val currentState = _swapModeState.value
        
        if (!currentState.isSwappingAllowed(gameState.moves)) {
            return emptyList()
        }
        
        val adjacentPositions = getAdjacentPositions(selectedPosition)
        
        return when (currentState.currentMode) {
            SwapMode.FREE_SWAPPING, SwapMode.ONE_SWAP_PER_TURN, SwapMode.SWAP_ON_UPGRADE -> {
                adjacentPositions.filter { adjacent ->
                    isSwapAllowed(selectedPosition, adjacent, board, gameState).isValid
                }
            }
            SwapMode.MERGE_ONLY -> {
                adjacentPositions.filter { adjacent ->
                    mergeDetector.wouldSwapCreateMerges(board, selectedPosition, adjacent)
                }
            }
            SwapMode.NO_SWAPPING -> emptyList()
        }
    }
    
    /**
     * Get current status message for UI display
     */
    fun getStatusMessage(): String? {
        return _swapModeState.value.getStatusMessage()
    }
    
    /**
     * Reset state for new game
     */
    fun resetForNewGame() {
        val currentMode = _swapModeState.value.currentMode
        _swapModeState.value = SwapModeState(
            currentMode = currentMode,
            swapUnlockedForTurn = currentMode != SwapMode.SWAP_ON_UPGRADE
        )
    }
    
    // Private validation methods
    
    private fun validateFreeSwap(pos1: Position, pos2: Position, board: GameBoard): SwapValidationResult {
        val tile1 = board.getTile(pos1)
        val tile2 = board.getTile(pos2)
        
        if (tile1 == null && tile2 == null) {
            return SwapValidationResult(
                isValid = false,
                reason = "Cannot swap two empty positions"
            )
        }
        
        return SwapValidationResult(isValid = true, reason = "Swap is valid")
    }
    
    private fun validateMergeOnlySwap(pos1: Position, pos2: Position, board: GameBoard): SwapValidationResult {
        if (!mergeDetector.wouldSwapCreateMerges(board, pos1, pos2)) {
            return SwapValidationResult(
                isValid = false,
                reason = "Swap must create at least one merge"
            )
        }
        
        return SwapValidationResult(isValid = true, reason = "Swap will create merges")
    }
    
    private fun validateOneSwapPerTurn(pos1: Position, pos2: Position, board: GameBoard, state: SwapModeState): SwapValidationResult {
        if (state.swapsUsedThisTurn > 0) {
            return SwapValidationResult(
                isValid = false,
                reason = "One swap per turn already used",
                canRetryLater = true
            )
        }
        
        return validateFreeSwap(pos1, pos2, board)
    }
    
    private fun validateSwapOnUpgrade(pos1: Position, pos2: Position, board: GameBoard, state: SwapModeState): SwapValidationResult {
        if (!state.swapUnlockedForTurn) {
            return SwapValidationResult(
                isValid = false,
                reason = "Reach a new tile value to unlock swap",
                canRetryLater = true
            )
        }
        
        return validateFreeSwap(pos1, pos2, board)
    }
    
    private fun getSwapDeniedReason(state: SwapModeState): String {
        return when (state.currentMode) {
            SwapMode.ONE_SWAP_PER_TURN -> "One swap per turn already used"
            SwapMode.SWAP_ON_UPGRADE -> "Reach a new tile value to unlock swap"
            SwapMode.NO_SWAPPING -> "Swapping is disabled"
            else -> "Swap not allowed"
        }
    }
    
    private fun canRetrySwapLater(state: SwapModeState): Boolean {
        return when (state.currentMode) {
            SwapMode.ONE_SWAP_PER_TURN -> true // Can retry after swipe
            SwapMode.SWAP_ON_UPGRADE -> true // Can retry after upgrade
            SwapMode.NO_SWAPPING -> false // Never allowed
            else -> false
        }
    }
    
    private fun getAdjacentPositions(position: Position): List<Position> {
        val directions = listOf(
            Position(-1, 0), // Up
            Position(1, 0),  // Down
            Position(0, -1), // Left
            Position(0, 1)   // Right
        )
        
        return directions.mapNotNull { direction ->
            val newRow = position.row + direction.row
            val newCol = position.col + direction.col
            val newPosition = Position(newRow, newCol)
            
            if (newPosition.isValid()) newPosition else null
        }
    }
}

/**
 * Result of swap validation with additional context
 */
data class SwapValidationResult(
    val isValid: Boolean,
    val reason: String,
    val canRetryLater: Boolean = false
)