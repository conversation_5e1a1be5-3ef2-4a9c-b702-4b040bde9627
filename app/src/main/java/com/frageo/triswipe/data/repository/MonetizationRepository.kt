package com.frageo.triswipe.data.repository

/**
 * Repository interface for monetization and premium features management.
 * Handles free game limits, premium status, and billing-related data.
 */
interface MonetizationRepository {
    
    /**
     * Get the number of free games remaining for non-premium users.
     * @return Number of free games remaining (0 if exhausted or premium user)
     */
    suspend fun getFreeGamesRemaining(): Int
    
    /**
     * Set the number of free games remaining.
     * @param count Number of free games to set
     */
    suspend fun setFreeGamesRemaining(count: Int)
    
    /**
     * Decrement free games counter by 1.
     * Only affects non-premium users.
     */
    suspend fun decrementFreeGames()
    
    /**
     * Check if user has purchased premium status.
     * @return True if user has premium, false otherwise
     */
    suspend fun isPremiumUser(): Boolean
    
    /**
     * Set the user's premium status.
     * @param isPremium True to grant premium status, false to revoke
     */
    suspend fun setPremiumUser(isPremium: Boolean)
}