package com.frageo.triswipe.data.database

import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase

/**
 * Central repository for all database migrations in TriSwipe.
 * 
 * This object provides a structured approach to database schema evolution,
 * ensuring user data is preserved across app updates.
 * 
 * Migration Naming Convention:
 * - MIGRATION_X_Y: Migration from version X to version Y
 * - Each migration should include comprehensive comments explaining changes
 * 
 * Current Schema Status:
 * - Version 1: Original schema (destructive migrations used)
 * - Version 2: Schema cleanup - aligned GameStatsEntity with GameStats model
 *   - Added bestTile field for consistency
 *   - Changed averageScore from Float to Double
 *   - Removed premium fields from database (moved to preferences)
 */
object DatabaseMigrations {
    
    /**
     * Migration 1→2: Schema cleanup and model alignment
     * 
     * Changes made:
     * - Added bestTile field for consistency with GameStats model
     * - Changed averageScore from REAL (Float) to proper REAL (Double) 
     * - Removed premium fields from database (moved to SharedPreferences)
     * - Ensured all fields have proper default values
     */
    val MIGRATION_1_2 = object : Migration(1, 2) {
        override fun migrate(db: SupportSQLiteDatabase) {
            // Create new table with correct schema
            db.execSQL("""
                CREATE TABLE game_stats_new (
                    id TEXT PRIMARY KEY NOT NULL,
                    highScore INTEGER NOT NULL DEFAULT 0,
                    gamesPlayed INTEGER NOT NULL DEFAULT 0,
                    gamesWon INTEGER NOT NULL DEFAULT 0,
                    totalMoves INTEGER NOT NULL DEFAULT 0,
                    highestTile INTEGER NOT NULL DEFAULT 0,
                    bestTile INTEGER NOT NULL DEFAULT 0,
                    averageScore REAL NOT NULL DEFAULT 0.0,
                    bestTime INTEGER NOT NULL DEFAULT 0,
                    totalPlayTime INTEGER NOT NULL DEFAULT 0,
                    totalMerges INTEGER NOT NULL DEFAULT 0,
                    longestWinStreak INTEGER NOT NULL DEFAULT 0,
                    currentWinStreak INTEGER NOT NULL DEFAULT 0,
                    lastUpdated INTEGER NOT NULL DEFAULT 0
                )
            """)
            
            // Copy data from old table, ignoring premium columns
            db.execSQL("""
                INSERT INTO game_stats_new (
                    id, highScore, gamesPlayed, gamesWon, totalMoves,
                    highestTile, bestTile, averageScore, bestTime, totalPlayTime,
                    totalMerges, longestWinStreak, currentWinStreak, lastUpdated
                )
                SELECT 
                    COALESCE(id, 'global_stats'),
                    COALESCE(highScore, 0),
                    COALESCE(gamesPlayed, 0),
                    COALESCE(gamesWon, 0),
                    COALESCE(totalMoves, 0),
                    COALESCE(highestTile, 0),
                    COALESCE(highestTile, 0) as bestTile,
                    COALESCE(averageScore, 0.0),
                    COALESCE(bestTime, 0),
                    COALESCE(totalPlayTime, 0),
                    COALESCE(totalMerges, 0),
                    COALESCE(longestWinStreak, 0),
                    COALESCE(currentWinStreak, 0),
                    COALESCE(lastUpdated, strftime('%s', 'now') * 1000)
                FROM game_stats
            """)
            
            // Drop old table and rename new table
            db.execSQL("DROP TABLE game_stats")
            db.execSQL("ALTER TABLE game_stats_new RENAME TO game_stats")
        }
    }
    
    /**
     * Example Migration 2→3: Adding a new column for premium activation tracking
     * 
     * This is a practice migration to demonstrate the infrastructure.
     * In a real scenario, this would add premium activation date tracking.
     */
    val MIGRATION_2_3 = object : Migration(2, 3) {
        override fun migrate(db: SupportSQLiteDatabase) {
            // Add new column to track when premium was activated
            db.execSQL(
                "ALTER TABLE game_stats ADD COLUMN premiumActivationDate INTEGER DEFAULT NULL"
            )
        }
    }
    
    /**
     * Example Migration 3→4: Adding achievements table
     * 
     * This demonstrates adding a completely new table with proper constraints.
     */
    val MIGRATION_3_4 = object : Migration(3, 4) {
        override fun migrate(db: SupportSQLiteDatabase) {
            // Create new achievements table
            db.execSQL("""
                CREATE TABLE IF NOT EXISTS achievements (
                    id TEXT PRIMARY KEY NOT NULL,
                    type TEXT NOT NULL,
                    unlockedAt INTEGER NOT NULL,
                    progress INTEGER NOT NULL DEFAULT 0,
                    target INTEGER NOT NULL DEFAULT 1,
                    description TEXT NOT NULL DEFAULT ''
                )
            """)
            
            // Create index for faster queries
            db.execSQL(
                "CREATE INDEX IF NOT EXISTS index_achievements_type ON achievements(type)"
            )
        }
    }
    
    /**
     * Example Migration 4→5: Complex data transformation
     * 
     * This demonstrates more complex migrations involving data restructuring.
     * Shows how to handle backwards compatibility and data validation.
     */
    val MIGRATION_4_5 = object : Migration(4, 5) {
        override fun migrate(db: SupportSQLiteDatabase) {
            // Create new optimized stats table structure
            db.execSQL("""
                CREATE TABLE game_stats_new (
                    id TEXT PRIMARY KEY NOT NULL,
                    highScore INTEGER NOT NULL DEFAULT 0,
                    gamesPlayed INTEGER NOT NULL DEFAULT 0,
                    gamesWon INTEGER NOT NULL DEFAULT 0,
                    totalMoves INTEGER NOT NULL DEFAULT 0,
                    highestTile INTEGER NOT NULL DEFAULT 0,
                    bestTile INTEGER NOT NULL DEFAULT 0,
                    averageScore REAL NOT NULL DEFAULT 0.0,
                    bestTime INTEGER NOT NULL DEFAULT 0,
                    totalPlayTime INTEGER NOT NULL DEFAULT 0,
                    totalMerges INTEGER NOT NULL DEFAULT 0,
                    longestWinStreak INTEGER NOT NULL DEFAULT 0,
                    currentWinStreak INTEGER NOT NULL DEFAULT 0,
                    lastUpdated INTEGER NOT NULL DEFAULT 0,
                    -- New optimized fields
                    statsVersion INTEGER NOT NULL DEFAULT 2,
                    dataIntegrityHash TEXT DEFAULT NULL
                )
            """)
            
            // Copy data from old table to new table with validation
            db.execSQL("""
                INSERT INTO game_stats_new (
                    id, highScore, gamesPlayed, gamesWon, totalMoves,
                    highestTile, bestTile, averageScore, bestTime, totalPlayTime,
                    totalMerges, longestWinStreak, currentWinStreak, lastUpdated,
                    statsVersion
                )
                SELECT 
                    id, 
                    COALESCE(highScore, 0),
                    COALESCE(gamesPlayed, 0),
                    COALESCE(gamesWon, 0),
                    COALESCE(totalMoves, 0),
                    COALESCE(highestTile, 0),
                    COALESCE(bestTile, 0),
                    COALESCE(averageScore, 0.0),
                    COALESCE(bestTime, 0),
                    COALESCE(totalPlayTime, 0),
                    COALESCE(totalMerges, 0),
                    COALESCE(longestWinStreak, 0),
                    COALESCE(currentWinStreak, 0),
                    COALESCE(lastUpdated, 0),
                    2
                FROM game_stats
            """)
            
            // Drop old table and rename new table
            db.execSQL("DROP TABLE game_stats")
            db.execSQL("ALTER TABLE game_stats_new RENAME TO game_stats")
        }
    }
    
    /**
     * Get all available migrations in order.
     * 
     * This method ensures migrations are applied in the correct sequence.
     * Always add new migrations to the end of this array.
     * 
     * @return Array of all migrations from version 1 onwards
     */
    fun getAllMigrations(): Array<Migration> {
        return arrayOf(
            MIGRATION_1_2,
            MIGRATION_2_3,
            MIGRATION_3_4,
            MIGRATION_4_5
        )
    }
    
    /**
     * Get migrations for a specific version range.
     * 
     * Useful for testing specific migration paths or troubleshooting.
     * 
     * @param fromVersion Starting version (inclusive)
     * @param toVersion Ending version (inclusive)
     * @return Array of migrations needed for the version range
     */
    fun getMigrationsForRange(fromVersion: Int, toVersion: Int): Array<Migration> {
        return getAllMigrations().filter { migration ->
            migration.startVersion >= fromVersion && migration.endVersion <= toVersion
        }.toTypedArray()
    }
    
    /**
     * Validate migration path exists.
     * 
     * Ensures there's a continuous migration path from one version to another.
     * Useful for detecting gaps in migration coverage.
     * 
     * @param fromVersion Starting version
     * @param toVersion Target version
     * @return true if a complete migration path exists
     */
    fun hasCompleteMigrationPath(fromVersion: Int, toVersion: Int): Boolean {
        if (fromVersion >= toVersion) return true
        
        val migrations = getAllMigrations()
        var currentVersion = fromVersion
        
        while (currentVersion < toVersion) {
            val nextMigration = migrations.find { it.startVersion == currentVersion }
            if (nextMigration == null) {
                return false
            }
            currentVersion = nextMigration.endVersion
        }
        
        return currentVersion == toVersion
    }
}