package com.frageo.triswipe.data.models

data class GameStats(
    val highScore: Int = 0,
    val gamesPlayed: Int = 0,
    val gamesWon: Int = 0,
    val totalMoves: Int = 0,
    val totalMerges: Int = 0,
    val highestTile: Int = 0,            // Match Entity field name
    val bestTile: Int = 0,               // Add for consistency
    val averageScore: Double = 0.0,      // Match Entity type
    val bestTime: Long = 0L,             // ADD: Missing field from Entity
    val totalPlayTime: Long = 0L,
    val longestWinStreak: Int = 0,
    val currentWinStreak: Int = 0
    // NOTE: Premium fields (freeGamesRemaining, hasPurchasedPremium) moved to preferences layer
) {
    fun withHighScore(newHighScore: Int): GameStats {
        return copy(highScore = maxOf(highScore, newHighScore))
    }
    
    fun withGamePlayed(score: Int, moves: Int, won: Boolean, playTime: Long): GameStats {
        val newGamesPlayed = gamesPlayed + 1
        val newGamesWon = if (won) gamesWon + 1 else gamesWon
        val newTotalMoves = totalMoves + moves
        val newTotalPlayTime = totalPlayTime + playTime
        val newAverageScore = (averageScore * gamesPlayed + score) / newGamesPlayed
        val newCurrentWinStreak = if (won) currentWinStreak + 1 else 0
        val newLongestWinStreak = if (won) maxOf(longestWinStreak, newCurrentWinStreak) else longestWinStreak
        
        return copy(
            highScore = maxOf(highScore, score),
            gamesPlayed = newGamesPlayed,
            gamesWon = newGamesWon,
            totalMoves = newTotalMoves,
            averageScore = newAverageScore,
            currentWinStreak = newCurrentWinStreak,
            longestWinStreak = newLongestWinStreak,
            totalPlayTime = newTotalPlayTime
        )
    }
    
    fun withMerge(newTile: Int): GameStats {
        return copy(
            totalMerges = totalMerges + 1,
            bestTile = maxOf(bestTile, newTile),
            highestTile = maxOf(highestTile, newTile)  // Update both fields for consistency
        )
    }
    
    val averageMovesPerGame: Double
        get() = if (gamesPlayed > 0) totalMoves.toDouble() / gamesPlayed else 0.0
    
    val averagePlayTimePerGame: Double
        get() = if (gamesPlayed > 0) totalPlayTime.toDouble() / gamesPlayed else 0.0
}