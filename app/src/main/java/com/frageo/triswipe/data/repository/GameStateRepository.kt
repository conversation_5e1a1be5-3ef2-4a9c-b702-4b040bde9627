package com.frageo.triswipe.data.repository

import com.frageo.triswipe.data.models.GameState

/**
 * Repository interface for game state persistence operations.
 * Handles saving, loading, and clearing game state data.
 */
interface GameStateRepository {
    
    /**
     * Save the current game state to persistent storage.
     */
    suspend fun saveGameState(gameState: GameState)
    
    /**
     * Load the saved game state from persistent storage.
     * @return GameState if found, null if no saved state exists
     */
    suspend fun loadGameState(): GameState?
    
    /**
     * Clear the saved game state from persistent storage.
     */
    suspend fun clearGameState()
}