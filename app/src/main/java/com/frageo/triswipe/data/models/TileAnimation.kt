package com.frageo.triswipe.data.models

import com.frageo.triswipe.animation.AnimationConfig

/**
 * Animation type enumeration for unified tile animation system
 */
enum class TileAnimationType {
    MOVE,               // Regular tile movement (swipe gestures)
    MERGE_TO_CENTER     // Tile merging animation flying to center
}

/**
 * Unified data class for all tile animations, replacing TileMovement and MergeWarpAnimation
 */
data class TileAnimation(
    val tile: Tile,
    val fromPosition: Position,
    val toPosition: Position,
    val animationType: TileAnimationType,
    val duration: Int = 300,    // Animation duration in ms
    val delay: Int = 0          // Animation delay in ms
) {
    /**
     * Calculates the interpolated position during animation
     */
    fun getInterpolatedPosition(progress: Float): Pair<Float, Float> {
        val deltaRow = toPosition.row - fromPosition.row
        val deltaCol = toPosition.col - fromPosition.col
        
        return Pair(
            fromPosition.row + deltaRow * progress,
            fromPosition.col + deltaCol * progress
        )
    }
    
    /**
     * Calculates alpha value for merge animations (fade out at end)
     */
    fun getAlpha(progress: Float): Float {
        return when (animationType) {
            TileAnimationType.MOVE -> 1f
            TileAnimationType.MERGE_TO_CENTER -> when {
                progress < AnimationConfig.FADE_START_PROGRESS -> 1f
                progress < AnimationConfig.FADE_END_PROGRESS -> 1f - (progress - AnimationConfig.FADE_START_PROGRESS) * AnimationConfig.FADE_MULTIPLIER
                else -> 0f
            }
        }
    }
    
    /**
     * Calculates scale value for merge animations (shrink at end)
     */
    fun getScale(progress: Float): Float {
        return when (animationType) {
            TileAnimationType.MOVE -> 1f
            TileAnimationType.MERGE_TO_CENTER -> when {
                progress < AnimationConfig.SCALE_START_PROGRESS -> 1f
                else -> 1f - (progress - AnimationConfig.SCALE_START_PROGRESS) * AnimationConfig.SCALE_MULTIPLIER
            }.coerceAtLeast(0.1f)
        }
    }
    
    /**
     * Checks if this is a merge animation
     */
    fun isMergeAnimation(): Boolean {
        return animationType == TileAnimationType.MERGE_TO_CENTER
    }
    
    /**
     * Checks if this is a movement animation
     */
    fun isMoveAnimation(): Boolean {
        return animationType == TileAnimationType.MOVE
    }
}