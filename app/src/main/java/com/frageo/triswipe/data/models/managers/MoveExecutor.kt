package com.frageo.triswipe.data.models.managers

import com.frageo.triswipe.data.models.SwipeResult
import com.frageo.triswipe.data.models.SwapResult
import com.frageo.triswipe.data.models.MoveResult
import com.frageo.triswipe.data.models.Direction
import com.frageo.triswipe.data.models.Position
import com.frageo.triswipe.data.models.MoveType
import com.frageo.triswipe.data.models.GameBoard
import com.frageo.triswipe.data.models.GameState
import com.frageo.triswipe.data.models.TileMovement
import com.frageo.triswipe.data.models.MergeAction
import com.frageo.triswipe.data.models.Tile

/**
 * Interface for executing game moves with validation and state management.
 * Handles the actual execution of swipes, swaps, and combined move operations.
 * 
 * Extracted from GameEngine to separate move execution logic and improve testability.
 */
interface MoveExecutor {
    
    /**
     * Execute a swipe move in the specified direction
     * @param direction The direction to swipe
     * @param currentState The current game state
     * @param board The game board to operate on
     * @return Result of the swipe operation
     */
    fun executeSwipe(direction: Direction, currentState: GameState, board: GameBoard): SwipeResult
    
    /**
     * Execute a tile swap between two positions
     * @param pos1 First position to swap
     * @param pos2 Second position to swap
     * @param currentState The current game state
     * @param board The game board to operate on
     * @return Result of the swap operation
     */
    fun executeSwap(pos1: Position, pos2: Position, currentState: GameState, board: GameBoard): SwapResult
    
    /**
     * Execute a validated move with comprehensive validation and error handling
     * @param moveType The type of move to execute
     * @param params The move parameters
     * @param currentState The current game state
     * @param board The game board to operate on
     * @return Generic move result with success status and details
     */
    fun executeValidatedMove(
        moveType: MoveType, 
        params: MoveParams, 
        currentState: GameState, 
        board: GameBoard
    ): MoveResult
    
    /**
     * Execute the core game flow: merge-move-merge-spawn sequence
     * @param direction The swipe direction
     * @param board The game board to operate on
     * @return Execution details including movements and merges
     */
    fun executeGameFlow(direction: Direction, board: GameBoard): GameFlowResult
    
    /**
     * Update game state after a successful move
     * @param currentState The current game state
     * @param scoreGained Points gained from the move
     * @param mergeCount Number of merges that occurred
     * @return Updated game state
     */
    fun updateGameState(currentState: GameState, scoreGained: Int, mergeCount: Int): GameState
}

/**
 * Result of executing the complete game flow
 */
data class GameFlowResult(
    val success: Boolean,
    val message: String,
    val tileMovements: List<TileMovement> = emptyList(),
    val mergeActions: List<MergeAction> = emptyList(),
    val scoreGained: Int = 0,
    val newTileSpawned: Boolean = false
)