package com.frageo.triswipe.data.models

enum class ThemeMode(
    val displayName: String,
    val description: String
) {
    AUTO("Auto", "Follow system setting"),
    LIGHT("Light", "Always use light theme"),
    DARK("Dark", "Always use dark theme");

    companion object {
        fun fromString(value: String?): ThemeMode {
            return try {
                valueOf(value ?: AUTO.name)
            } catch (e: IllegalArgumentException) {
                AUTO
            }
        }
    }
}