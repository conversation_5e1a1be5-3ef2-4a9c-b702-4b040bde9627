package com.frageo.triswipe.data.repository.entities

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "game_stats")
data class GameStatsEntity(
    @PrimaryKey
    val id: String = "global_stats",
    val highScore: Int = 0,
    val gamesPlayed: Int = 0,
    val gamesWon: Int = 0,
    val totalMoves: Int = 0,
    val highestTile: Int = 0,           // Keep existing field name
    val bestTile: Int = 0,              // ADD: For consistency with GameStats model
    val averageScore: Double = 0.0,     // CHANGE: Float -> Double for consistency
    val bestTime: Long = 0L,
    val totalPlayTime: Long = 0L,
    val totalMerges: Int = 0,           // Track total number of merges performed
    val longestWinStreak: Int = 0,      // Best consecutive wins achieved
    val currentWinStreak: Int = 0,      // Current consecutive wins
    val lastUpdated: Long = System.currentTimeMillis()
)