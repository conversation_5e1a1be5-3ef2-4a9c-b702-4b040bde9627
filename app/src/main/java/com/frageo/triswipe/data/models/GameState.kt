package com.frageo.triswipe.data.models

data class GameState(
    val board: Array<Array<Tile?>>,
    val score: Int = 0,
    val moves: Int = 0,
    val isGameOver: Boolean = false,
    val hasWon: Boolean = false,
    val lastMoveType: String? = null
) {
    init {
        require(board.size == 4) { "Board must be 4x4" }
        require(board.all { it.size == 4 }) { "Board must be 4x4" }
    }
    
    fun withScore(newScore: Int): GameState {
        return copy(score = newScore)
    }
    
    fun withMoves(newMoves: Int): GameState {
        return copy(moves = newMoves)
    }
    
    fun withGameOver(gameOver: Boolean): GameState {
        return copy(isGameOver = gameOver)
    }
    
    fun withWon(won: Boolean): GameState {
        return copy(hasWon = won)
    }
    
    fun getTileAt(position: Position): Tile? {
        return if (position.isValid()) {
            board[position.row][position.col]
        } else {
            null
        }
    }
    
    fun isEmpty(position: Position): Boolean {
        return getTileAt(position) == null
    }
    
    fun getEmptyPositions(): List<Position> {
        val emptyPositions = mutableListOf<Position>()
        for (row in 0 until 4) {
            for (col in 0 until 4) {
                val position = Position(row, col)
                if (isEmpty(position)) {
                    emptyPositions.add(position)
                }
            }
        }
        return emptyPositions
    }
    
    fun hasEmptySpaces(): Boolean {
        return getEmptyPositions().isNotEmpty()
    }
    
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        
        other as GameState
        
        if (!board.contentDeepEquals(other.board)) return false
        if (score != other.score) return false
        if (moves != other.moves) return false
        if (isGameOver != other.isGameOver) return false
        if (hasWon != other.hasWon) return false
        if (lastMoveType != other.lastMoveType) return false
        
        return true
    }
    
    override fun hashCode(): Int {
        var result = board.contentDeepHashCode()
        result = 31 * result + score
        result = 31 * result + moves
        result = 31 * result + isGameOver.hashCode()
        result = 31 * result + hasWon.hashCode()
        result = 31 * result + (lastMoveType?.hashCode() ?: 0)
        return result
    }
}