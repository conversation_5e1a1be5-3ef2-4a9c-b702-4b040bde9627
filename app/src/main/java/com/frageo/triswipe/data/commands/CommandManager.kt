package com.frageo.triswipe.data.commands

import com.frageo.triswipe.data.models.GameEngineInterface
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Manages command execution and history for the game.
 * Provides advanced undo/redo functionality and command tracking.
 */
@Singleton
class CommandManager @Inject constructor() {
    
    private val executedCommands = mutableListOf<GameCommand>()
    private val maxHistorySize = 50 // Increased from 10 for better UX
    
    private val _commandHistory = MutableStateFlow<List<GameCommand>>(emptyList())
    val commandHistory: StateFlow<List<GameCommand>> = _commandHistory.asStateFlow()
    
    private val _canUndo = MutableStateFlow(false)
    val canUndo: StateFlow<Boolean> = _canUndo.asStateFlow()
    
    private val _undoCount = MutableStateFlow(0)
    val undoCount: StateFlow<Int> = _undoCount.asStateFlow()
    
    /**
     * Execute a command and add it to the history
     */
    fun executeCommand(command: GameCommand, engine: GameEngineInterface): CommandResult {
        val result = command.execute(engine)
        
        if (result.success) {
            addToHistory(command)
        }
        
        return result
    }
    
    /**
     * Undo the last executed command
     */
    fun undoLastCommand(engine: GameEngineInterface): CommandResult {
        if (executedCommands.isEmpty()) {
            return CommandResult.failure(
                message = "No commands to undo",
                gameState = engine.getCurrentGameState()
            )
        }
        
        val lastCommand = executedCommands.removeLastOrNull()
            ?: return CommandResult.failure(
                message = "Failed to retrieve last command",
                gameState = engine.getCurrentGameState()
            )
        
        val result = lastCommand.undo(engine)
        updateStateFlows()
        
        return result
    }
    
    
    
    
    /**
     * Clear command history
     */
    fun clearHistory() {
        executedCommands.clear()
        updateStateFlows()
    }
    
    /**
     * Check if we can undo commands
     */
    fun canUndoCommand(): Boolean {
        return executedCommands.isNotEmpty() && executedCommands.any { it.canUndo() }
    }
    
    /**
     * Get the number of commands that can be undone
     */
    fun getUndoableCommandCount(): Int {
        return executedCommands.count { it.canUndo() }
    }
    
    /**
     * Get command history statistics
     */
    
    
    /**
     * Add a command to the history
     */
    private fun addToHistory(command: GameCommand) {
        executedCommands.add(command)
        
        // Keep only the last maxHistorySize commands
        if (executedCommands.size > maxHistorySize) {
            executedCommands.removeAt(0)
        }
        
        updateStateFlows()
    }
    
    /**
     * Update the reactive state flows
     */
    private fun updateStateFlows() {
        _commandHistory.value = executedCommands.toList()
        _canUndo.value = canUndoCommand()
        _undoCount.value = getUndoableCommandCount()
    }
    
}



/**
 * Macro command that executes multiple commands in sequence
 */
class MacroCommand(
    private val commands: List<GameCommand>,
    private val macroDescription: String
) : GameCommand {
    
    override val id: String = "macro_${java.util.UUID.randomUUID()}"
    override val description: String = macroDescription
    
    private val executedCommands = mutableListOf<GameCommand>()
    
    override fun execute(engine: GameEngineInterface): CommandResult {
        val results = mutableListOf<CommandResult>()
        
        for (command in commands) {
            val result = command.execute(engine)
            results.add(result)
            
            if (result.success) {
                executedCommands.add(command)
            } else {
                // If any command fails, undo all previously executed commands
                undoExecutedCommands(engine)
                return CommandResult.failure(
                    message = "Macro command failed at: ${command.description}",
                    gameState = engine.getCurrentGameState(),
                    additionalData = mapOf(
                        "failedCommand" to command,
                        "results" to results
                    )
                )
            }
        }
        
        return CommandResult.success(
            message = "Macro command completed: $macroDescription",
            gameState = engine.getCurrentGameState(),
            additionalData = mapOf(
                "executedCommands" to executedCommands.size,
                "results" to results
            )
        )
    }
    
    override fun undo(engine: GameEngineInterface): CommandResult {
        return undoExecutedCommands(engine)
    }
    
    override fun canExecute(engine: GameEngineInterface): Boolean {
        return commands.all { it.canExecute(engine) }
    }
    
    override fun canUndo(): Boolean {
        return executedCommands.isNotEmpty() && executedCommands.all { it.canUndo() }
    }
    
    private fun undoExecutedCommands(engine: GameEngineInterface): CommandResult {
        val results = mutableListOf<CommandResult>()
        
        // Undo in reverse order
        for (command in executedCommands.reversed()) {
            val result = command.undo(engine)
            results.add(result)
            
            if (!result.success) {
                return CommandResult.failure(
                    message = "Failed to undo macro command at: ${command.description}",
                    gameState = engine.getCurrentGameState(),
                    additionalData = mapOf(
                        "failedCommand" to command,
                        "results" to results
                    )
                )
            }
        }
        
        executedCommands.clear()
        
        return CommandResult.success(
            message = "Macro command undone: $macroDescription",
            gameState = engine.getCurrentGameState(),
            additionalData = mapOf(
                "undoneCommands" to results.size,
                "results" to results
            )
        )
    }
}