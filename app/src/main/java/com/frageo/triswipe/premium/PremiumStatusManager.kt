package com.frageo.triswipe.premium

import com.frageo.triswipe.data.repository.MonetizationRepository
import com.frageo.triswipe.data.repository.GameStatisticsManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Single source of truth for premium status management.
 * 
 * This class centralizes all premium-related state and logic, providing reactive flows
 * that UI components can observe. It eliminates synchronization issues between the
 * repository and UI state by maintaining a single authoritative source.
 * 
 * Key benefits:
 * - Reactive updates via StateFlow
 * - Centralized premium logic
 * - No sync issues between repository and UI
 * - Simplified premium status management
 */
@Singleton
class PremiumStatusManager @Inject constructor(
    private val monetizationRepository: MonetizationRepository,
    private val statisticsManager: GameStatisticsManager
) {
    
    // Single source of truth for premium status
    private val _isPremiumUser = MutableStateFlow(false)
    val isPremiumUser: StateFlow<Boolean> = _isPremiumUser.asStateFlow()
    
    // Single source of truth for free games remaining
    private val _freeGamesRemaining = MutableStateFlow(30)
    val freeGamesRemaining: StateFlow<Int> = _freeGamesRemaining.asStateFlow()
    
    /**
     * Initialize premium status from repository.
     * Should be called once during app startup.
     */
    suspend fun initialize() {
        _isPremiumUser.value = monetizationRepository.isPremiumUser()
        _freeGamesRemaining.value = monetizationRepository.getFreeGamesRemaining()
    }
    
    /**
     * Update premium status and persist to repository.
     * Automatically updates all reactive flows.
     * 
     * @param isPremium true to activate premium, false to deactivate
     */
    suspend fun setPremiumStatus(isPremium: Boolean) {
        // Update repository first
        monetizationRepository.setPremiumUser(isPremium)
        
        // Update reactive state
        _isPremiumUser.value = isPremium
        
        // If activating premium, set unlimited games
        if (isPremium) {
            monetizationRepository.setFreeGamesRemaining(Int.MAX_VALUE)
            _freeGamesRemaining.value = Int.MAX_VALUE
        }
        
        // Emit event for statistics tracking and UI synchronization
        statisticsManager.recordPremiumStatusChanged(isPremium)
    }
    
    /**
     * Decrement free games for non-premium users.
     * Premium users are unaffected.
     */
    suspend fun decrementFreeGames() {
        if (!_isPremiumUser.value) {
            monetizationRepository.decrementFreeGames()
            val gamesRemaining = monetizationRepository.getFreeGamesRemaining()
            _freeGamesRemaining.value = gamesRemaining
            
            // Emit event for analytics and UI synchronization
            statisticsManager.recordFreeGameConsumed(gamesRemaining)
        }
    }
    
    /**
     * Check if user can start a new game.
     * Premium users can always start games.
     * Free users need remaining games > 0.
     */
    fun canStartNewGame(): Boolean {
        return _isPremiumUser.value || _freeGamesRemaining.value > 0
    }
    
    /**
     * Reset premium status to free user (for testing/debugging).
     */
    suspend fun resetToFreeUser() {
        monetizationRepository.setPremiumUser(false)
        monetizationRepository.setFreeGamesRemaining(30)
        _isPremiumUser.value = false
        _freeGamesRemaining.value = 30
    }
    
    // Convenience methods for immediate value access (non-reactive)
    
    /**
     * Get current premium status value (non-reactive).
     * For reactive updates, observe isPremiumUser flow.
     */
    fun isPremiumUserValue(): Boolean = _isPremiumUser.value
    
    /**
     * Get current free games remaining value (non-reactive).
     * For reactive updates, observe freeGamesRemaining flow.
     */
    fun freeGamesRemainingValue(): Int = _freeGamesRemaining.value
}
