package com.frageo.triswipe.animation

/**
 * Centralized animation configuration constants.
 * Contains all timing, progress, and animation-related settings for the game.
 */
object AnimationConfig {
    // Animation durations (in milliseconds)
    const val MOVE_DURATION = 250
    const val MERGE_DURATION = 400
    const val SPAWN_DURATION = 350
    
    // Animation delays and timing
    const val STAGGER_DELAY = 15
    
    // Merge animation fade and scale thresholds
    const val FADE_START_PROGRESS = 0.6f
    const val FADE_END_PROGRESS = 0.9f
    const val SCALE_START_PROGRESS = 0.8f
    
    // Fade calculation constants
    const val FADE_MULTIPLIER = 3.33f // For fade out in last 30% (1 / (0.9 - 0.6))
    
    // Scale calculation constants
    const val SCALE_MULTIPLIER = 5.0f // For scale down in last 20% (1 / (1.0 - 0.8))
}