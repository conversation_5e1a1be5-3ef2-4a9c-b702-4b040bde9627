package com.frageo.triswipe.animation

import com.frageo.triswipe.data.models.Direction
import com.frageo.triswipe.data.models.GameConfig
import com.frageo.triswipe.data.models.ImmutableBoard
import com.frageo.triswipe.data.models.MergeAction
import com.frageo.triswipe.data.models.Position
import com.frageo.triswipe.data.models.Tile
import com.frageo.triswipe.data.models.TileAnimation
import com.frageo.triswipe.data.models.TileAnimationType
import com.frageo.triswipe.data.models.TileMovement
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Centralized animation controller responsible for creating and managing
 * all tile animations in the game. Separates animation logic from business logic.
 */
@Singleton
class AnimationController @Inject constructor() {
    
    /**
     * Creates unified animations from tile movements and merge actions.
     * Combines movement and merge animations into a cohesive animation sequence.
     * 
     * @param tileMovements List of tile movements from swipe/swap operations
     * @param mergeActions List of merge actions that occurred
     * @param direction The direction of movement (null for swaps)
     * @param originalBoard The board state before the move (for accurate merge positioning)
     * @return List of TileAnimation objects ready for UI consumption
     */
    fun createUnifiedAnimations(
        tileMovements: List<TileMovement>,
        mergeActions: List<MergeAction>,
        direction: Direction?,
        originalBoard: ImmutableBoard
    ): List<TileAnimation> {
        val animations = mutableListOf<TileAnimation>()
        
        // Convert tile movements to MOVE animations
        tileMovements.forEach { movement ->
            animations.add(
                TileAnimation(
                    tile = movement.tile,
                    fromPosition = movement.tile.position,
                    toPosition = movement.toPosition,
                    animationType = TileAnimationType.MOVE,
                    duration = AnimationConfig.MOVE_DURATION,
                    delay = calculateMovementDelay(movement.tile.position, direction)
                )
            )
        }
        
        // Convert merge actions to MERGE_TO_CENTER animations
        mergeActions.forEach { mergeAction ->
            mergeAction.removedTiles.forEach { tile ->
                // Find the original position of this tile before the swipe
                val originalPosition = findOriginalTilePosition(tile, originalBoard, direction) ?: tile.position
                
                animations.add(
                    TileAnimation(
                        tile = tile,
                        fromPosition = originalPosition, // Use original position before swipe
                        toPosition = mergeAction.mergePosition, // Fly to final merge center
                        animationType = TileAnimationType.MERGE_TO_CENTER,
                        duration = AnimationConfig.MERGE_DURATION,
                        delay = 0 // Merge animations should start immediately
                    )
                )
            }
        }
        
        return animations
    }
    
    /**
     * Creates visual movement mappings for legacy support.
     * Maps original positions to target positions for simple movement tracking.
     * 
     * @param oldBoard The board state before movement
     * @param newBoard The board state after movement
     * @param direction The direction of the swipe
     * @return Map of Position to Position showing tile movements
     */
    fun createVisualMovements(
        oldBoard: ImmutableBoard, 
        newBoard: ImmutableBoard, 
        direction: Direction
    ): Map<Position, Position> {
        val movements = mutableMapOf<Position, Position>()
        
        // This is a simplified implementation - could be enhanced based on actual usage
        for (row in 0 until GameConfig.BOARD_SIZE) {
            for (col in 0 until GameConfig.BOARD_SIZE) {
                val newPos = Position(row, col)
                val newTile = newBoard.getTile(newPos)
                
                if (newTile != null) {
                    // Find where this tile came from
                    val originalPos = findOriginalTilePosition(newTile, oldBoard, direction)
                    if (originalPos != null && originalPos != newPos) {
                        movements[originalPos] = newPos
                    }
                }
            }
        }
        
        return movements
    }
    
    /**
     * Calculates animation delay based on tile position and movement direction.
     * Creates staggered animation effects for more visually appealing movements.
     * 
     * @param position The position of the tile
     * @param direction The direction of movement (null for no staggering)
     * @return Delay in milliseconds
     */
    fun calculateMovementDelay(position: Position, direction: Direction?): Int {
        return when (direction) {
            Direction.UP, Direction.DOWN -> position.row * AnimationConfig.STAGGER_DELAY
            Direction.LEFT, Direction.RIGHT -> position.col * AnimationConfig.STAGGER_DELAY
            null -> 0
        }
    }
    
    /**
     * Finds the original position of a tile before a swipe movement.
     * Uses multiple strategies to accurately track tile origins for merge animations.
     * 
     * @param tile The tile to find the original position for
     * @param originalBoard The board state before movement
     * @param direction The direction of movement
     * @return The original position, or null if not found
     */
    private fun findOriginalTilePosition(
        tile: Tile,
        originalBoard: ImmutableBoard,
        direction: Direction?
    ): Position? {
        // Strategy 1: Try to find by tile ID if available
        for (row in 0 until GameConfig.BOARD_SIZE) {
            for (col in 0 until GameConfig.BOARD_SIZE) {
                val originalPos = Position(row, col)
                val originalTile = originalBoard.getTile(originalPos)
                
                // Perfect match by ID
                if (originalTile != null && originalTile.id == tile.id) {
                    return originalPos
                }
            }
        }
        
        // Strategy 2: Find closest tile by value that could have moved to this position
        var bestMatch: Position? = null
        var bestDistance = Int.MAX_VALUE
        
        for (row in 0 until GameConfig.BOARD_SIZE) {
            for (col in 0 until GameConfig.BOARD_SIZE) {
                val originalPos = Position(row, col)
                val originalTile = originalBoard.getTile(originalPos)
                
                // Check if this original tile matches our target tile value
                if (originalTile != null && originalTile.value == tile.value) {
                    // Check if this tile could have moved to the current tile's position
                    if (couldTileMoveTo(originalPos, tile.position, direction)) {
                        // Calculate distance to find the most likely source
                        val distance = when (direction) {
                            Direction.UP -> originalPos.row - tile.position.row
                            Direction.DOWN -> tile.position.row - originalPos.row
                            Direction.LEFT -> originalPos.col - tile.position.col
                            Direction.RIGHT -> tile.position.col - originalPos.col
                            null -> 0
                        }
                        
                        if (distance < bestDistance) {
                            bestDistance = distance
                            bestMatch = originalPos
                        }
                    }
                }
            }
        }
        
        return bestMatch // Return the closest valid match
    }
    
    /**
     * Checks if a tile at fromPos could move to toPos given the swipe direction.
     * Validates movement constraints based on game rules.
     * 
     * @param fromPos The starting position
     * @param toPos The target position
     * @param direction The movement direction
     * @return True if the movement is possible, false otherwise
     */
    private fun couldTileMoveTo(fromPos: Position, toPos: Position, direction: Direction?): Boolean {
        return when (direction) {
            Direction.UP -> fromPos.col == toPos.col && fromPos.row >= toPos.row
            Direction.DOWN -> fromPos.col == toPos.col && fromPos.row <= toPos.row
            Direction.LEFT -> fromPos.row == toPos.row && fromPos.col >= toPos.col
            Direction.RIGHT -> fromPos.row == toPos.row && fromPos.col <= toPos.col
            null -> false
        }
    }
}