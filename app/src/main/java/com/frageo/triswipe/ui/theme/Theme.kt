package com.frageo.triswipe.ui.theme

import android.app.Activity
import android.os.Build
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.dynamicDarkColorScheme
import androidx.compose.material3.dynamicLightColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.SideEffect
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalView
import androidx.core.view.WindowCompat
import com.frageo.triswipe.data.models.ThemeMode

// TriSwipe custom color scheme based on 2048-style design
private val TriSwipeLightColorScheme = lightColorScheme(
    primary = Color(0xFF8F7A66),
    onPrimary = Color.White,
    primaryContainer = Color(0xFFBBADA0),
    onPrimaryContainer = Color(0xFF776E65),
    secondary = Color(0xFFEDC850),
    onSecondary = Color(0xFF776E65),
    secondaryContainer = Color(0xFFF2B179),
    onSecondaryContainer = Color(0xFF776E65),
    tertiary = Color(0xFFF67C5F),
    onTertiary = Color.White,
    tertiaryContainer = Color(0xFFEEE4DA),
    onTertiaryContainer = Color(0xFF776E65),
    error = Color(0xFFE74C3C),
    onError = Color.White,
    errorContainer = Color(0xFFFFDAD6),
    onErrorContainer = Color(0xFF410002),
    background = Color(0xFFFAF8EF),
    onBackground = Color(0xFF776E65),
    surface = Color.White,
    onSurface = Color(0xFF776E65),
    surfaceVariant = Color(0xFFCCC0B3),
    onSurfaceVariant = Color(0xFF776E65),
    outline = Color(0xFFBBADA0),
    outlineVariant = Color(0xFFEEE4DA),
    scrim = Color(0x80000000),
    inverseSurface = Color(0xFF776E65),
    inverseOnSurface = Color.White,
    inversePrimary = Color(0xFFEEE4DA)
)

private val TriSwipeDarkColorScheme = darkColorScheme(
    // Primary colors - warm browns that match the game aesthetic
    primary = Color(0xFFBBADA0),           // Warm beige (main interactive elements)
    onPrimary = Color(0xFF3E3A35),         // Darker brown for text on primary
    primaryContainer = Color(0xFF8F7A66),  // Deeper brown for containers
    onPrimaryContainer = Color(0xFFF5F4F0), // Light cream for text on containers
    
    // Secondary colors - golden accent
    secondary = Color(0xFFEDC850),         // Gold for highlights
    onSecondary = Color(0xFF3E3A35),       // Dark brown for text on gold
    secondaryContainer = Color(0xFFB8941F), // Darker gold for containers
    onSecondaryContainer = Color(0xFFF5F4F0), // Light for text on dark gold
    
    // Tertiary colors - warm orange accent
    tertiary = Color(0xFFF67C5F),          // Warm orange for special elements
    onTertiary = Color(0xFF3E3A35),        // Dark text on orange
    tertiaryContainer = Color(0xFFCD5E42), // Darker orange container
    onTertiaryContainer = Color(0xFFF5F4F0), // Light text on dark orange
    
    // Error colors - keeping Material 3 standard for accessibility
    error = Color(0xFFFFB4AB),
    onError = Color(0xFF690005),
    errorContainer = Color(0xFF93000A),
    onErrorContainer = Color(0xFFFFDAD6),
    
    // Background colors - warm dark theme
    background = Color(0xFF1A1A1A),        // Warmer dark background (was cold #1C1B1F)
    onBackground = Color(0xFFF0EBE6),      // Warm off-white for text
    surface = Color(0xFF242424),           // Slightly lighter warm surface
    onSurface = Color(0xFFF0EBE6),         // Consistent warm text color
    
    // Surface variations
    surfaceVariant = Color(0xFF5A534C),    // Muted brown for subtle surfaces
    onSurfaceVariant = Color(0xFFD7CCC8), // Light brown for text on variants
    
    // Outlines and borders
    outline = Color(0xFF8F7A66),           // Warm brown outline
    outlineVariant = Color(0xFF5A534C),    // Subtle outline variant
    
    // System colors
    scrim = Color(0xFF000000),             // Pure black for overlays
    inverseSurface = Color(0xFFF0EBE6),    // Light surface for inverse
    inverseOnSurface = Color(0xFF1A1A1A),  // Dark text on light surface
    inversePrimary = Color(0xFF8F7A66)     // Primary color for inverse context
)

@Composable
fun TriSwipeTheme(
    themeMode: ThemeMode = ThemeMode.AUTO,
    // Dynamic color is available on Android 12+
    dynamicColor: Boolean = false, // Disabled for consistent branding
    content: @Composable () -> Unit
) {
    val systemInDarkTheme = isSystemInDarkTheme()
    
    // Determine if we should use dark theme based on theme mode
    val useDarkTheme = when (themeMode) {
        ThemeMode.AUTO -> systemInDarkTheme
        ThemeMode.LIGHT -> false
        ThemeMode.DARK -> true
    }
    
    val colorScheme = when {
        dynamicColor && Build.VERSION.SDK_INT >= Build.VERSION_CODES.S -> {
            val context = LocalContext.current
            if (useDarkTheme) dynamicDarkColorScheme(context) else dynamicLightColorScheme(context)
        }

        useDarkTheme -> TriSwipeDarkColorScheme
        else -> TriSwipeLightColorScheme
    }
    val view = LocalView.current
    if (!view.isInEditMode) {
        SideEffect {
            val window = (view.context as Activity).window
            WindowCompat.setDecorFitsSystemWindows(window, false)
            WindowCompat.getInsetsController(window, view).isAppearanceLightStatusBars = !useDarkTheme
        }
    }

    MaterialTheme(
        colorScheme = colorScheme,
        typography = Typography,
        content = content
    )
}

