package com.frageo.triswipe.ui.theme

import androidx.compose.runtime.Composable
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.sp
import androidx.compose.ui.unit.TextUnit

/**
 * Responsive typography system for TriSwipe
 * Provides adaptive font sizes based on screen size and context
 */
object AppTypography {
    
    @Composable
    fun getDisplayLarge(): TextUnit {
        val screenSize = Dimensions.getScreenSize()
        return when (screenSize) {
            Dimensions.ScreenSize.COMPACT -> 40.sp
            Dimensions.ScreenSize.MEDIUM -> 48.sp
            Dimensions.ScreenSize.EXPANDED -> 56.sp
        }
    }
    
    @Composable
    fun getDisplayMedium(): TextUnit {
        val screenSize = Dimensions.getScreenSize()
        return when (screenSize) {
            Dimensions.ScreenSize.COMPACT -> 32.sp
            Dimensions.ScreenSize.MEDIUM -> 40.sp
            Dimensions.ScreenSize.EXPANDED -> 48.sp
        }
    }
    
    @Composable
    fun getDisplaySmall(): TextUnit {
        val screenSize = Dimensions.getScreenSize()
        return when (screenSize) {
            Dimensions.ScreenSize.COMPACT -> 28.sp
            Dimensions.ScreenSize.MEDIUM -> 32.sp
            Dimensions.ScreenSize.EXPANDED -> 36.sp
        }
    }
    
    @Composable
    fun getHeadlineLarge(): TextUnit {
        val screenSize = Dimensions.getScreenSize()
        return when (screenSize) {
            Dimensions.ScreenSize.COMPACT -> 20.sp
            Dimensions.ScreenSize.MEDIUM -> 24.sp
            Dimensions.ScreenSize.EXPANDED -> 28.sp
        }
    }
    
    @Composable
    fun getHeadlineMedium(): TextUnit {
        val screenSize = Dimensions.getScreenSize()
        return when (screenSize) {
            Dimensions.ScreenSize.COMPACT -> 18.sp
            Dimensions.ScreenSize.MEDIUM -> 20.sp
            Dimensions.ScreenSize.EXPANDED -> 24.sp
        }
    }
    
    @Composable
    fun getHeadlineSmall(): TextUnit {
        val screenSize = Dimensions.getScreenSize()
        return when (screenSize) {
            Dimensions.ScreenSize.COMPACT -> 16.sp
            Dimensions.ScreenSize.MEDIUM -> 18.sp
            Dimensions.ScreenSize.EXPANDED -> 20.sp
        }
    }
    
    @Composable
    fun getBodyLarge(): TextUnit {
        val screenSize = Dimensions.getScreenSize()
        return when (screenSize) {
            Dimensions.ScreenSize.COMPACT -> 14.sp
            Dimensions.ScreenSize.MEDIUM -> 16.sp
            Dimensions.ScreenSize.EXPANDED -> 18.sp
        }
    }
    
    @Composable
    fun getBodyMedium(): TextUnit {
        val screenSize = Dimensions.getScreenSize()
        return when (screenSize) {
            Dimensions.ScreenSize.COMPACT -> 12.sp
            Dimensions.ScreenSize.MEDIUM -> 14.sp
            Dimensions.ScreenSize.EXPANDED -> 16.sp
        }
    }
    
    @Composable
    fun getBodySmall(): TextUnit {
        val screenSize = Dimensions.getScreenSize()
        return when (screenSize) {
            Dimensions.ScreenSize.COMPACT -> 10.sp
            Dimensions.ScreenSize.MEDIUM -> 12.sp
            Dimensions.ScreenSize.EXPANDED -> 14.sp
        }
    }
    
    @Composable
    fun getLabelLarge(): TextUnit {
        val screenSize = Dimensions.getScreenSize()
        return when (screenSize) {
            Dimensions.ScreenSize.COMPACT -> 12.sp
            Dimensions.ScreenSize.MEDIUM -> 14.sp
            Dimensions.ScreenSize.EXPANDED -> 16.sp
        }
    }
    
    @Composable
    fun getLabelMedium(): TextUnit {
        val screenSize = Dimensions.getScreenSize()
        return when (screenSize) {
            Dimensions.ScreenSize.COMPACT -> 10.sp
            Dimensions.ScreenSize.MEDIUM -> 12.sp
            Dimensions.ScreenSize.EXPANDED -> 14.sp
        }
    }
    
    @Composable
    fun getLabelSmall(): TextUnit {
        val screenSize = Dimensions.getScreenSize()
        return when (screenSize) {
            Dimensions.ScreenSize.COMPACT -> 8.sp
            Dimensions.ScreenSize.MEDIUM -> 10.sp
            Dimensions.ScreenSize.EXPANDED -> 12.sp
        }
    }
    
    // Tile-specific typography that scales with tile size
    @Composable
    fun getTileTextSize(tileValue: Int): TextUnit {
        val tileSize = Dimensions.getTileSize()
        val baseFactor = when {
            tileValue >= 1000 -> 0.22f
            tileValue >= 100 -> 0.26f
            tileValue >= 10 -> 0.30f
            else -> 0.35f
        }
        
        // Scale with tile size
        return (tileSize.value * baseFactor).sp
    }
    
    // Common text styles
    object Weight {
        val Light = FontWeight.Light
        val Normal = FontWeight.Normal
        val Medium = FontWeight.Medium
        val SemiBold = FontWeight.SemiBold
        val Bold = FontWeight.Bold
        val ExtraBold = FontWeight.ExtraBold
    }
}