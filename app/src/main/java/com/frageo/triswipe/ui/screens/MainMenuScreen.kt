package com.frageo.triswipe.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.frageo.triswipe.ui.theme.Dimensions
import com.frageo.triswipe.ui.theme.AppTypography
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.compose.ui.platform.LocalContext
import com.frageo.triswipe.billing.PurchaseState
import com.frageo.triswipe.data.models.GameConfig
import com.frageo.triswipe.data.models.TileTheme
import com.frageo.triswipe.ui.components.UpgradePromptDialog
import com.frageo.triswipe.viewmodel.GameViewModel

@Composable
fun MainMenuScreen(
    onNavigateToGame: () -> Unit,
    onNavigateToSettings: () -> Unit,
    modifier: Modifier = Modifier,
    gameViewModel: GameViewModel = viewModel()
) {
    val uiState by gameViewModel.uiState.collectAsStateWithLifecycle()
    val purchaseState by gameViewModel.getPurchaseState().collectAsStateWithLifecycle()
    val isPremiumUser by gameViewModel.getPremiumStatusFlow().collectAsStateWithLifecycle()
    val freeGamesRemaining by gameViewModel.getFreeGamesRemainingFlow().collectAsStateWithLifecycle()
    val configuration = LocalConfiguration.current
    val isLandscape = configuration.screenWidthDp > configuration.screenHeightDp
    val context = LocalContext.current
    
    var showUpgradeDialog by remember { mutableStateOf(false) }
    
    // Handle purchase state changes
    LaunchedEffect(purchaseState) {
        when (purchaseState) {
            is PurchaseState.Success -> {
                showUpgradeDialog = false
                // Premium status automatically updates via PremiumStatusManager flows
            }
            is PurchaseState.Error -> {
                // Keep dialog open, error will be shown
            }
            is PurchaseState.Cancelled -> {
                // Keep dialog open in case user wants to try again
            }
            else -> {
                // Handle other states if needed
            }
        }
    }
    
    Box(
        modifier = modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background)
    ) {
        if (isLandscape) {
            LandscapeMenuLayout(
                hasActiveGame = !uiState.isGameOver && uiState.moves > 0,
                freeGamesRemaining = freeGamesRemaining,
                isPremiumUser = isPremiumUser,
                onNewGame = {
                    if (isPremiumUser || freeGamesRemaining > 0) {
                        gameViewModel.startNewGame()
                        onNavigateToGame()
                    } else {
                        showUpgradeDialog = true
                    }
                },
                onResumeGame = onNavigateToGame,
                onSettings = onNavigateToSettings,
                onShowUpgradeDialog = { showUpgradeDialog = true },
                tileTheme = uiState.selectedTileTheme
            )
        } else {
            PortraitMenuLayout(
                hasActiveGame = !uiState.isGameOver && uiState.moves > 0,
                freeGamesRemaining = freeGamesRemaining,
                isPremiumUser = isPremiumUser,
                onNewGame = {
                    if (isPremiumUser || freeGamesRemaining > 0) {
                        gameViewModel.startNewGame()
                        onNavigateToGame()
                    } else {
                        showUpgradeDialog = true
                    }
                },
                onResumeGame = onNavigateToGame,
                onSettings = onNavigateToSettings,
                onShowUpgradeDialog = { showUpgradeDialog = true },
                tileTheme = uiState.selectedTileTheme
            )
        }
        
        // Show upgrade dialog when needed
        if (showUpgradeDialog) {
            UpgradePromptDialog(
                onDismiss = { showUpgradeDialog = false },
                onUpgrade = {
                    // Launch real billing flow (Phase 10.2)
                    gameViewModel.launchPurchaseFlow(context as android.app.Activity)
                },
                premiumPrice = gameViewModel.getPremiumPrice(),
                isProcessing = purchaseState is PurchaseState.Purchasing,
                isPending = purchaseState is PurchaseState.Pending,
                errorMessage = (purchaseState as? PurchaseState.Error)?.message,
                tileTheme = uiState.selectedTileTheme
            )
        }
    }
}

@Composable
private fun PortraitMenuLayout(
    hasActiveGame: Boolean,
    freeGamesRemaining: Int,
    isPremiumUser: Boolean,
    onNewGame: () -> Unit,
    onResumeGame: () -> Unit,
    onSettings: () -> Unit,
    onShowUpgradeDialog: () -> Unit,
    tileTheme: TileTheme
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(Dimensions.getSpacingXl()),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(Dimensions.getSpacingL())
    ) {
        Spacer(modifier = Modifier.weight(1f))
        
        // Game Title
        Text(
            text = "TriSwipe",
            fontSize = AppTypography.getDisplayLarge(),
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.onBackground
        )
        
        // Game Description
        Text(
            text = "The hybrid puzzle game",
            fontSize = AppTypography.getHeadlineSmall(),
            color = MaterialTheme.colorScheme.onBackground,
            textAlign = TextAlign.Center
        )
        
        Text(
            text = "Swipe to move tiles • Tap to swap adjacent tiles\nMerge 3+ identical tiles • Reach ${GameConfig.WIN_TILE_VALUE} to win!",
            fontSize = AppTypography.getBodyMedium(),
            color = MaterialTheme.colorScheme.onBackground,
            textAlign = TextAlign.Center,
            lineHeight = 20.sp,
            modifier = Modifier.padding(horizontal = Dimensions.getSpacingM())
        )
        
        Spacer(modifier = Modifier.height(Dimensions.getSpacingM()))
        
        // Menu Buttons
        Column(
            verticalArrangement = Arrangement.spacedBy(Dimensions.getSpacingS()),
            modifier = Modifier.fillMaxWidth()
        ) {
            if (hasActiveGame) {
                MenuButton(
                    text = "Resume Game",
                    icon = Icons.Default.PlayArrow,
                    onClick = onResumeGame,
                    isPrimary = true,
                    tileTheme = tileTheme
                )
            }
            
            MenuButton(
                text = if (hasActiveGame) "New Game" else "Start Game",
                icon = Icons.Default.PlayArrow,
                onClick = onNewGame,
                isPrimary = !hasActiveGame,
                enabled = isPremiumUser || freeGamesRemaining > 0,
                tileTheme = tileTheme
            )
            
            MenuButton(
                text = "Settings",
                icon = Icons.Default.Settings,
                onClick = onSettings,
                isPrimary = false,
                tileTheme = tileTheme
            )
        }
        
        Spacer(modifier = Modifier.weight(1f))
        
        // Footer
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(Dimensions.getSpacingXs())
        ) {
            if (!isPremiumUser) {
                TextButton(
                    onClick = onShowUpgradeDialog
                ) {
                    Text(
                        text = "Upgrade to Premium",
                        fontSize = AppTypography.getBodyMedium(),
                        color = MaterialTheme.colorScheme.primary
                    )
                }
            }
            
            Text(
                text = if (isPremiumUser) {
                    "Premium version • Unlimited games"
                } else {
                    "Free version • $freeGamesRemaining games remaining"
                },
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onBackground,
                textAlign = TextAlign.Center
            )
        }
    }
}

@Composable
private fun LandscapeMenuLayout(
    hasActiveGame: Boolean,
    freeGamesRemaining: Int,
    isPremiumUser: Boolean,
    onNewGame: () -> Unit,
    onResumeGame: () -> Unit,
    onSettings: () -> Unit,
    onShowUpgradeDialog: () -> Unit,
    tileTheme: TileTheme
) {
    Row(
        modifier = Modifier
            .fillMaxSize()
            .padding(Dimensions.getSpacingXl()),
        horizontalArrangement = Arrangement.spacedBy(Dimensions.getSpacingXxl())
    ) {
        // Left side - Title and description
        Column(
            modifier = Modifier.weight(1f),
            verticalArrangement = Arrangement.Center
        ) {
            Text(
                text = "TriSwipe",
                style = MaterialTheme.typography.displayMedium.copy(fontWeight = FontWeight.Bold),
                color = MaterialTheme.colorScheme.onBackground
            )
            
            Spacer(modifier = Modifier.height(Dimensions.getSpacingM()))
            
            Text(
                text = "The hybrid puzzle game",
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.onBackground
            )
            
            Spacer(modifier = Modifier.height(Dimensions.getSpacingXs()))
            
            Text(
                text = "Swipe to move tiles • Tap to swap adjacent tiles\nMerge 3+ identical tiles • Reach ${GameConfig.WIN_TILE_VALUE} to win!",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onBackground
            )
        }
        
        // Right side - Menu buttons
        Column(
            modifier = Modifier.weight(1f),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Column(
                verticalArrangement = Arrangement.spacedBy(Dimensions.getSpacingS()),
                modifier = Modifier.fillMaxWidth()
            ) {
                if (hasActiveGame) {
                    MenuButton(
                        text = "Resume Game",
                        icon = Icons.Default.PlayArrow,
                        onClick = onResumeGame,
                        isPrimary = true,
                        tileTheme = tileTheme
                    )
                }
                
                MenuButton(
                    text = if (hasActiveGame) "New Game" else "Start Game",
                    icon = Icons.Default.PlayArrow,
                    onClick = onNewGame,
                    isPrimary = !hasActiveGame,
                    enabled = isPremiumUser || freeGamesRemaining > 0,
                    tileTheme = tileTheme
                )
                
                MenuButton(
                    text = "Settings",
                    icon = Icons.Default.Settings,
                    onClick = onSettings,
                    isPrimary = false,
                    tileTheme = tileTheme
                )
            }
            
            Spacer(modifier = Modifier.height(Dimensions.getSpacingL()))
            
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(Dimensions.getSpacingXs())
            ) {
                if (!isPremiumUser) {
                    TextButton(
                        onClick = onShowUpgradeDialog
                    ) {
                        Text(
                            text = "Upgrade to Premium",
                            fontSize = AppTypography.getBodyMedium(),
                            color = MaterialTheme.colorScheme.primary
                        )
                    }
                }
                
                Text(
                    text = if (isPremiumUser) {
                        "Premium version • Unlimited games"
                    } else {
                        "Free version • $freeGamesRemaining games remaining"
                    },
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onBackground,
                    textAlign = TextAlign.Center
                )
            }
        }
    }
}

@Composable
private fun MenuButton(
    text: String,
    icon: ImageVector,
    onClick: () -> Unit,
    isPrimary: Boolean,
    tileTheme: TileTheme,
    modifier: Modifier = Modifier,
    enabled: Boolean = true
) {
    val accentColor = Color(android.graphics.Color.parseColor(tileTheme.getBoardAccentColor()))
    
    if (isPrimary) {
        // Use FilledTonalButton for primary actions
        FilledTonalButton(
            onClick = onClick,
            enabled = enabled,
            modifier = modifier
                .fillMaxWidth()
                .height(Dimensions.getButtonHeight()),
            colors = ButtonDefaults.filledTonalButtonColors(
                containerColor = accentColor,
                contentColor = Color.White
            ),
            shape = RoundedCornerShape(Dimensions.getCornerRadiusS())
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                modifier = Modifier.size(Dimensions.getButtonIconSize())
            )
            Spacer(modifier = Modifier.width(Dimensions.getSpacingXs()))
            Text(
                text = text,
                style = MaterialTheme.typography.labelLarge,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
        }
    } else {
        // Use OutlinedButton for secondary actions
        OutlinedButton(
            onClick = onClick,
            enabled = enabled,
            modifier = modifier
                .fillMaxWidth()
                .height(Dimensions.getButtonHeight()),
            colors = ButtonDefaults.outlinedButtonColors(
                contentColor = accentColor
            ),
            border = BorderStroke(1.dp, accentColor),
            shape = RoundedCornerShape(Dimensions.getCornerRadiusS())
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                modifier = Modifier.size(Dimensions.getButtonIconSize())
            )
            Spacer(modifier = Modifier.width(Dimensions.getSpacingXs()))
            Text(
                text = text,
                style = MaterialTheme.typography.labelLarge,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
fun MainMenuScreenPreview() {
    MainMenuScreen(
        onNavigateToGame = {},
        onNavigateToSettings = {}
    )
}

@Preview(showBackground = true, widthDp = 800, heightDp = 480)
@Composable
fun MainMenuScreenLandscapePreview() {
    MainMenuScreen(
        onNavigateToGame = {},
        onNavigateToSettings = {}
    )
}