package com.frageo.triswipe.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.ui.graphics.Color
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import com.frageo.triswipe.data.models.TileTheme
import com.frageo.triswipe.data.models.ThemeMode
import com.frageo.triswipe.viewmodel.GameViewModel
import com.frageo.triswipe.ui.components.ResponsiveSettingsLayout

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen(
    onNavigateBack: () -> Unit,
    gameViewModel: GameViewModel,
    modifier: Modifier = Modifier
) {
    // State management
    var swipeSensitivity by remember { mutableFloatStateOf(0.5f) }
    var tapSensitivity by remember { mutableFloatStateOf(0.5f) }
    var selectedTheme by remember { mutableStateOf(TileTheme.CLASSIC) }
    var themeMode by remember { mutableStateOf(ThemeMode.AUTO) }
    
    // Load preferences
    LaunchedEffect(Unit) {
        // Load actual theme mode preference
        themeMode = gameViewModel.getThemeMode()
        // Load tile theme preference
        selectedTheme = gameViewModel.getSelectedTheme()
    }
    
    // Get tile theme colors for theming
    val tileAccentColor = Color(android.graphics.Color.parseColor(selectedTheme.getBoardAccentColor()))
    val (primaryBgColorHex, _) = selectedTheme.getBoardBackgroundColors()
    val primaryBgColor = Color(android.graphics.Color.parseColor(primaryBgColorHex))
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Settings") },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Back",
                            tint = tileAccentColor
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.surface,
                    titleContentColor = tileAccentColor,
                    navigationIconContentColor = tileAccentColor
                )
            )
        },
        modifier = modifier.background(
            primaryBgColor.copy(alpha = 0.02f) // Very subtle background tint
        )
    ) { paddingValues ->
        ResponsiveSettingsLayout(
            gameViewModel = gameViewModel,
            swipeSensitivity = swipeSensitivity,
            onSwipeSensitivityChange = { swipeSensitivity = it },
            tapSensitivity = tapSensitivity,
            onTapSensitivityChange = { tapSensitivity = it },
            selectedTheme = selectedTheme,
            onThemeChange = { 
                selectedTheme = it
                gameViewModel.setSelectedTheme(it)
            },
            themeMode = themeMode,
            onThemeModeChange = { 
                themeMode = it
                gameViewModel.setThemeMode(it)
            },
            modifier = Modifier.padding(paddingValues)
        )
    }
}

@Preview
@Composable
private fun SettingsScreenPreview() {
    // Preview implementation would go here
    // This is simplified for the refactored version
}