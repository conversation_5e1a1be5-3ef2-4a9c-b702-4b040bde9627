package com.frageo.triswipe.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.frageo.triswipe.ui.theme.Dimensions
import com.frageo.triswipe.ui.theme.AppTypography
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.frageo.triswipe.data.models.Direction
import com.frageo.triswipe.data.models.GameConfig
import com.frageo.triswipe.data.models.Position
import com.frageo.triswipe.data.models.Tile
import com.frageo.triswipe.data.models.TileOrigin
import com.frageo.triswipe.data.models.TileTheme
import com.frageo.triswipe.ui.components.GameBoardComponent
import com.frageo.triswipe.ui.components.GameButtons
import com.frageo.triswipe.ui.components.MovesCounter
import com.frageo.triswipe.ui.components.ScoreDisplay
import com.frageo.triswipe.viewmodel.GameUiState
import com.frageo.triswipe.viewmodel.GameViewModel

@Composable
fun GameScreen(
    onNavigateToMenu: () -> Unit,
    modifier: Modifier = Modifier,
    gameViewModel: GameViewModel = viewModel()
) {
    val uiState by gameViewModel.uiState.collectAsStateWithLifecycle()
    val isPremium by gameViewModel.getPremiumStatusFlow().collectAsStateWithLifecycle()
    val configuration = LocalConfiguration.current
    val isLandscape = configuration.screenWidthDp > configuration.screenHeightDp
    val context = LocalContext.current
    
    
    LaunchedEffect(uiState.errorMessage) {
        if (uiState.errorMessage != null) {
            kotlinx.coroutines.delay(3000)
            gameViewModel.clearError()
        }
    }
    
    Box(
        modifier = modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background)
    ) {
        if (isLandscape) {
            LandscapeGameLayout(
                uiState = uiState,
                isPremiumUser = isPremium,
                onSwipeGesture = gameViewModel::onSwipeGesture,
                onTileClick = gameViewModel::onTileClicked,
                onMenuClick = onNavigateToMenu,
                onUndoClick = gameViewModel::performUndo,
                gameViewModel = gameViewModel
            )
        } else {
            PortraitGameLayout(
                uiState = uiState,
                isPremiumUser = isPremium,
                onSwipeGesture = gameViewModel::onSwipeGesture,
                onTileClick = gameViewModel::onTileClicked,
                onMenuClick = onNavigateToMenu,
                onUndoClick = gameViewModel::performUndo,
                gameViewModel = gameViewModel
            )
        }
        
        if (uiState.isLoading) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Black.copy(alpha = 0.3f)),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator(
                    color = MaterialTheme.colorScheme.primary
                )
            }
        }
        
        uiState.errorMessage?.let { errorMessage ->
            ElevatedCard(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .fillMaxWidth()
                    .padding(Dimensions.getSpacingM()),
                colors = CardDefaults.elevatedCardColors(
                    containerColor = MaterialTheme.colorScheme.errorContainer
                ),
                shape = RoundedCornerShape(Dimensions.getCornerRadiusS())
            ) {
                Text(
                    text = errorMessage,
                    color = MaterialTheme.colorScheme.onErrorContainer,
                    style = MaterialTheme.typography.bodyMedium.copy(fontWeight = FontWeight.Medium),
                    textAlign = TextAlign.Center,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(Dimensions.getSpacingM())
                )
            }
        }
        
        if (uiState.hasWon) {
            GameWinDialog(
                score = uiState.score,
                moves = uiState.moves,
                onPlayAgain = gameViewModel::restartGame,
                onMainMenu = onNavigateToMenu,
                tileTheme = uiState.selectedTileTheme
            )
        } else if (uiState.isGameOver) {
            GameOverDialog(
                score = uiState.score,
                moves = uiState.moves,
                onPlayAgain = gameViewModel::restartGame,
                onMainMenu = onNavigateToMenu,
                tileTheme = uiState.selectedTileTheme
            )
        }
    }
}

@Composable
private fun PortraitGameLayout(
    uiState: GameUiState,
    isPremiumUser: Boolean,
    onSwipeGesture: (Direction) -> Unit,
    onTileClick: (Position) -> Unit,
    onMenuClick: () -> Unit,
    onUndoClick: () -> Unit = {},
    gameViewModel: GameViewModel
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
            .padding(Dimensions.getSpacingM()),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(Dimensions.getSpacingM())
    ) {
        Text(
            text = "TriSwipe",
            style = MaterialTheme.typography.displaySmall,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.onBackground,
            modifier = Modifier.padding(top = Dimensions.getSpacingXxl())
        )

        Text(
            text = "Swipe to move • Tap to swap",
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.colorScheme.onBackground,
            textAlign = TextAlign.Center
        )
        
        // Score and moves in a stable row
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(Dimensions.getSpacingM())
        ) {
            ScoreDisplay(
                score = uiState.score,
                modifier = Modifier.weight(1f),
                tileTheme = uiState.selectedTileTheme
            )
            MovesCounter(
                moves = uiState.moves,
                modifier = Modifier.weight(1f),
                tileTheme = uiState.selectedTileTheme
            )
        }
        
        // Fixed height container for swap mode status to prevent layout shifts
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(32.dp), // Fixed height to prevent jumping
            contentAlignment = Alignment.Center
        ) {
            uiState.swapModeStatus?.let { status ->
                com.frageo.triswipe.ui.components.SwapModeStatusIndicator(
                    statusMessage = status,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }
        
        GameBoardComponent(
            board = uiState.board,
            selectedPosition = uiState.selectedTile,
            validSwapPositions = uiState.validSwapPositions,
            isSwapping = uiState.isSwapping,
            isMerging = uiState.isMerging,
            tileAnimations = uiState.tileAnimations,
            onSwipeGesture = onSwipeGesture,
            onTileClick = onTileClick,
            onTileAnimationComplete = null,
            modifier = Modifier.padding(vertical = 16.dp),
            tileTheme = uiState.selectedTileTheme
        )
        
        GameButtons(
            onMenuClick = onMenuClick,
            onUndoClick = onUndoClick,
            canUndo = uiState.canUndo,
            isPremiumUser = isPremiumUser,
            modifier = Modifier.fillMaxWidth(),
            tileTheme = uiState.selectedTileTheme
        )
        
        if (uiState.availableMovesCount > 0) {
            Text(
                text = "Available moves: ${uiState.availableMovesCount}",
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onBackground,
                modifier = Modifier.padding(top = Dimensions.getSpacingXs())
            )
        }
    }
}

@Composable
private fun LandscapeGameLayout(
    uiState: GameUiState,
    isPremiumUser: Boolean,
    onSwipeGesture: (Direction) -> Unit,
    onTileClick: (Position) -> Unit,
    onMenuClick: () -> Unit,
    onUndoClick: () -> Unit = {},
    gameViewModel: GameViewModel
) {
    Row(
        modifier = Modifier
            .fillMaxSize()
            .padding(Dimensions.getSpacingM()),
        horizontalArrangement = Arrangement.spacedBy(Dimensions.getSpacingM())
    ) {
        Column(
            modifier = Modifier.weight(1f),
            verticalArrangement = Arrangement.spacedBy(Dimensions.getSpacingM())
        ) {
            Text(
                text = "TriSwipe",
                style = MaterialTheme.typography.headlineLarge,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onBackground
            )
            
            ScoreDisplay(
                score = uiState.score,
                modifier = Modifier.fillMaxWidth(),
                tileTheme = uiState.selectedTileTheme
            )
            
            MovesCounter(
                moves = uiState.moves,
                modifier = Modifier.fillMaxWidth(),
                tileTheme = uiState.selectedTileTheme
            )
            
            // Fixed height container for swap mode status to prevent layout shifts
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(32.dp), // Fixed height to prevent jumping
                contentAlignment = Alignment.Center
            ) {
                uiState.swapModeStatus?.let { status ->
                    com.frageo.triswipe.ui.components.SwapModeStatusIndicator(
                        statusMessage = status,
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }
            
            GameButtons(
                onMenuClick = onMenuClick,
                onUndoClick = onUndoClick,
                canUndo = uiState.canUndo,
                isPremiumUser = isPremiumUser,
                modifier = Modifier.fillMaxWidth(),
                tileTheme = uiState.selectedTileTheme
            )
            
            if (uiState.availableMovesCount > 0) {
                Text(
                    text = "Available moves: ${uiState.availableMovesCount}",
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colorScheme.onBackground
                )
            }
        }
        
        Column(
            modifier = Modifier.weight(1f),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            GameBoardComponent(
                board = uiState.board,
                selectedPosition = uiState.selectedTile,
                validSwapPositions = uiState.validSwapPositions,
                isSwapping = uiState.isSwapping,
                isMerging = uiState.isMerging,
                tileAnimations = uiState.tileAnimations,
                onSwipeGesture = onSwipeGesture,
                onTileClick = onTileClick,
                onTileAnimationComplete = null,
                tileTheme = uiState.selectedTileTheme
            )
        }
    }
}

@Composable
private fun GameWinDialog(
    score: Int,
    moves: Int,
    onPlayAgain: () -> Unit,
    onMainMenu: () -> Unit,
    tileTheme: TileTheme
) {
    val (primaryBgColor, _) = tileTheme.getBoardBackgroundColors()
    val accentColor = tileTheme.getBoardAccentColor()
    
    AlertDialog(
        onDismissRequest = { },
        title = {
            Text(
                text = "🎉 You Win!",
                style = MaterialTheme.typography.headlineMedium.copy(fontWeight = FontWeight.Bold),
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth(),
                color = MaterialTheme.colorScheme.primary
            )
        },
        text = {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(Dimensions.getSpacingXs())
            ) {
                Text(
                    text = "You reached ${GameConfig.WIN_TILE_VALUE}!",
                    style = MaterialTheme.typography.bodyLarge,
                    textAlign = TextAlign.Center
                )
                Text(
                    text = "Final Score: $score",
                    style = MaterialTheme.typography.bodyMedium.copy(fontWeight = FontWeight.Medium)
                )
                Text(
                    text = "Moves: $moves",
                    style = MaterialTheme.typography.bodyMedium.copy(fontWeight = FontWeight.Medium)
                )
            }
        },
        confirmButton = {
            TextButton(
                onClick = onPlayAgain,
                colors = ButtonDefaults.textButtonColors(
                    contentColor = MaterialTheme.colorScheme.primary
                )
            ) {
                Text("Play Again")
            }
        },
        dismissButton = {
            TextButton(
                onClick = onMainMenu,
                colors = ButtonDefaults.textButtonColors(
                    contentColor = MaterialTheme.colorScheme.primary
                )
            ) {
                Text("Main Menu")
            }
        },
        containerColor = MaterialTheme.colorScheme.surface
    )
}

@Composable
private fun GameOverDialog(
    score: Int,
    moves: Int,
    onPlayAgain: () -> Unit,
    onMainMenu: () -> Unit,
    tileTheme: TileTheme
) {
    val (primaryBgColor, _) = tileTheme.getBoardBackgroundColors()
    val accentColor = tileTheme.getBoardAccentColor()
    
    AlertDialog(
        onDismissRequest = { },
        title = {
            Text(
                text = "Game Over",
                style = MaterialTheme.typography.headlineMedium.copy(fontWeight = FontWeight.Bold),
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth(),
                color = MaterialTheme.colorScheme.primary
            )
        },
        text = {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(Dimensions.getSpacingXs())
            ) {
                Text(
                    text = "No more moves available!",
                    style = MaterialTheme.typography.bodyLarge,
                    textAlign = TextAlign.Center
                )
                Text(
                    text = "Final Score: $score",
                    style = MaterialTheme.typography.bodyMedium.copy(fontWeight = FontWeight.Medium)
                )
                Text(
                    text = "Moves: $moves",
                    style = MaterialTheme.typography.bodyMedium.copy(fontWeight = FontWeight.Medium)
                )
            }
        },
        confirmButton = {
            TextButton(
                onClick = onPlayAgain,
                colors = ButtonDefaults.textButtonColors(
                    contentColor = MaterialTheme.colorScheme.primary
                )
            ) {
                Text("Play Again")
            }
        },
        dismissButton = {
            TextButton(
                onClick = onMainMenu,
                colors = ButtonDefaults.textButtonColors(
                    contentColor = MaterialTheme.colorScheme.primary
                )
            ) {
                Text("Main Menu")
            }
        },
        containerColor = MaterialTheme.colorScheme.surface
    )
}

@Preview(showBackground = true)
@Composable
fun GameScreenPreview() {
    val sampleBoard = Array(4) { row ->
        Array(4) { col ->
            when {
                row == 0 && col == 0 -> Tile(1, Position(0, 0))
                row == 0 && col == 1 -> Tile(3, Position(0, 1))
                row == 0 && col == 2 -> Tile(9, Position(0, 2))
                row == 1 && col == 0 -> Tile(27, Position(1, 0))
                row == 1 && col == 1 -> Tile(81, Position(1, 1))
                row == 2 && col == 0 -> Tile(243, Position(2, 0))
                else -> null
            }
        }
    }
    
    val sampleUiState = GameUiState(
        board = sampleBoard,
        score = 12540,
        moves = 147,
        selectedTile = Position(1, 1),
        availableMovesCount = 23
    )
    
    GameScreen(
        onNavigateToMenu = {}
    )
}