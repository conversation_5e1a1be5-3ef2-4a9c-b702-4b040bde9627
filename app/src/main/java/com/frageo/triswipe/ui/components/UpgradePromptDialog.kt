package com.frageo.triswipe.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Star
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import com.frageo.triswipe.data.models.TileTheme
import com.frageo.triswipe.ui.theme.Dimensions
import com.frageo.triswipe.ui.theme.ThemeAwareButtonStyles

/**
 * Calculate contrasting text color based on background luminance
 */
private fun getContrastingTextColor(backgroundColor: String): Color {
    val color = Color(android.graphics.Color.parseColor(backgroundColor))
    val luminance = (0.299 * color.red + 0.587 * color.green + 0.114 * color.blue)
    return if (luminance > 0.5f) Color.Black else Color.White
}

@Composable
fun UpgradePromptDialog(
    onDismiss: () -> Unit,
    onUpgrade: () -> Unit,
    premiumPrice: String = "$1.99",
    isProcessing: Boolean = false,
    isPending: Boolean = false,
    errorMessage: String? = null,
    tileTheme: TileTheme = TileTheme.CLASSIC,
    modifier: Modifier = Modifier
) {
    Dialog(
        onDismissRequest = onDismiss
    ) {
        ElevatedCard(
            modifier = modifier
                .fillMaxWidth()
                .wrapContentHeight()
                .padding(Dimensions.getSpacingM()),
            shape = RoundedCornerShape(Dimensions.getCornerRadiusM()),
            colors = CardDefaults.elevatedCardColors(
                containerColor = MaterialTheme.colorScheme.surface
            )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(Dimensions.getSpacingL()),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(Dimensions.getSpacingM())
            ) {
                // Premium icon
                Icon(
                    imageVector = Icons.Default.Star,
                    contentDescription = null,
                    modifier = Modifier.size(48.dp),
                    tint = MaterialTheme.colorScheme.primary
                )
                
                // Title
                Text(
                    text = "Upgrade to Premium",
                    style = MaterialTheme.typography.headlineSmall.copy(fontWeight = FontWeight.Bold),
                    color = MaterialTheme.colorScheme.onSurface,
                    textAlign = TextAlign.Center
                )
                
                // Description
                Text(
                    text = "You've reached the 30-game limit for the free version.",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurface,
                    textAlign = TextAlign.Center
                )
                
                // Premium features
                Column(
                    modifier = Modifier.fillMaxWidth(),
                    verticalArrangement = Arrangement.spacedBy(Dimensions.getSpacingXs())
                ) {
                    Text(
                        text = "Premium features:",
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                    
                    PremiumFeatureItem("Unlimited games")
                    PremiumFeatureItem("Undo moves")
                    PremiumFeatureItem("Additional tile themes")
                    PremiumFeatureItem("Detailed statistics")
                    PremiumFeatureItem("No ads")
                }
                
                // Price
                Text(
                    text = "Only $premiumPrice - One-time purchase",
                    style = MaterialTheme.typography.titleMedium.copy(fontWeight = FontWeight.Bold),
                    color = MaterialTheme.colorScheme.primary,
                    textAlign = TextAlign.Center
                )
                
                // Pending purchase notification
                if (isPending) {
                    ElevatedCard(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.elevatedCardColors(
                            containerColor = MaterialTheme.colorScheme.primaryContainer
                        )
                    ) {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(Dimensions.getSpacingS()),
                            horizontalArrangement = Arrangement.spacedBy(Dimensions.getSpacingXs()),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(16.dp),
                                color = MaterialTheme.colorScheme.primary,
                                strokeWidth = 2.dp
                            )
                            Text(
                                text = "Purchase pending - waiting for payment confirmation...",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onPrimaryContainer,
                                textAlign = TextAlign.Start
                            )
                        }
                    }
                }
                
                // Error message
                errorMessage?.let { error ->
                    ElevatedCard(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.elevatedCardColors(
                            containerColor = MaterialTheme.colorScheme.errorContainer
                        )
                    ) {
                        Text(
                            text = error,
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onErrorContainer,
                            textAlign = TextAlign.Center,
                            modifier = Modifier.padding(Dimensions.getSpacingS())
                        )
                    }
                }
                
                // Buttons
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    OutlinedButton(
                        onClick = onDismiss,
                        modifier = Modifier.weight(1f),
                        colors = ThemeAwareButtonStyles.getGameButtonColors(
                            tileTheme = tileTheme,
                            materialColorScheme = MaterialTheme.colorScheme,
                            isEnabled = true
                        )
                    ) {
                        Text("Not Now")
                    }
                    
                    Button(
                        onClick = onUpgrade,
                        enabled = !isProcessing && !isPending,
                        modifier = Modifier.weight(1f),
                        colors = ThemeAwareButtonStyles.getAccentButtonColors(
                            tileTheme = tileTheme,
                            materialColorScheme = MaterialTheme.colorScheme,
                            isEnabled = !isProcessing && !isPending
                        )
                    ) {
                        when {
                            isPending -> {
                                Row(
                                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    CircularProgressIndicator(
                                        modifier = Modifier.size(16.dp),
                                        color = Color.White,
                                        strokeWidth = 2.dp
                                    )
                                    Text("Pending...")
                                }
                            }
                            isProcessing -> {
                                Row(
                                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    CircularProgressIndicator(
                                        modifier = Modifier.size(16.dp),
                                        color = Color.White,
                                        strokeWidth = 2.dp
                                    )
                                    Text("Processing...")
                                }
                            }
                            else -> {
                                Text("Upgrade Now")
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun PremiumFeatureItem(
    text: String
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(Dimensions.getSpacingXs())
    ) {
        Box(
            modifier = Modifier
                .size(6.dp)
                .background(
                    MaterialTheme.colorScheme.primary, 
                    shape = androidx.compose.foundation.shape.CircleShape
                )
        )
        
        Text(
            text = text,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurface
        )
    }
}

@Preview
@Composable
fun UpgradePromptDialogPreview() {
    UpgradePromptDialog(
        onDismiss = {},
        onUpgrade = {}
    )
}