package com.frageo.triswipe.ui.theme

import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.min
import kotlin.math.max
import kotlin.math.min

/**
 * Responsive dimensions system for TriSwipe
 * Provides adaptive sizing based on screen size and orientation
 */
object Dimensions {
    
    // Screen size categories
    enum class ScreenSize {
        COMPACT,    // < 600dp width (phones)
        MEDIUM,     // 600dp - 840dp width (large phones, small tablets)
        EXPANDED    // > 840dp width (tablets, desktop)
    }
    
    @Composable
    fun getScreenSize(): ScreenSize {
        val configuration = LocalConfiguration.current
        val screenWidth = configuration.screenWidthDp.dp
        
        return when {
            screenWidth < 600.dp -> ScreenSize.COMPACT
            screenWidth < 840.dp -> ScreenSize.MEDIUM
            else -> ScreenSize.EXPANDED
        }
    }
    
    // Responsive tile sizing
    @Composable
    fun getTileSize(): Dp {
        val configuration = LocalConfiguration.current
        val screenWidth = configuration.screenWidthDp.dp
        val screenHeight = configuration.screenHeightDp.dp
        val screenSize = getScreenSize()
        val isLandscape = screenWidth > screenHeight
        
        // Calculate available space for the game board
        val availableWidth = if (isLandscape) {
            // In landscape, leave more space for score/buttons
            screenWidth * 0.55f
        } else {
            // In portrait, use more of the width
            screenWidth * 0.85f
        }
        
        val availableHeight = if (isLandscape) {
            // In landscape, use most of the height
            screenHeight * 0.75f
        } else {
            // In portrait, leave space for score/buttons
            screenHeight * 0.45f
        }
        
        // Calculate tile size based on available space
        val maxTileSize = min(availableWidth, availableHeight) / 4.5f // 4 tiles + spacing
        
        // Apply screen size constraints
        val constrainedTileSize = when (screenSize) {
            ScreenSize.COMPACT -> maxTileSize.coerceIn(35.dp, 65.dp)
            ScreenSize.MEDIUM -> maxTileSize.coerceIn(45.dp, 85.dp)
            ScreenSize.EXPANDED -> maxTileSize.coerceIn(55.dp, 110.dp)
        }
        
        return constrainedTileSize
    }
    
    // Responsive spacing
    @Composable
    fun getTileSpacing(): Dp {
        val screenSize = getScreenSize()
        return when (screenSize) {
            ScreenSize.COMPACT -> 3.dp
            ScreenSize.MEDIUM -> 4.dp
            ScreenSize.EXPANDED -> 6.dp
        }
    }
    
    @Composable
    fun getBoardPadding(): Dp {
        val screenSize = getScreenSize()
        return when (screenSize) {
            ScreenSize.COMPACT -> 6.dp
            ScreenSize.MEDIUM -> 8.dp
            ScreenSize.EXPANDED -> 12.dp
        }
    }
    
    @Composable
    fun getGridOffset(): Dp {
        val screenSize = getScreenSize()
        return when (screenSize) {
            ScreenSize.COMPACT -> 4.dp
            ScreenSize.MEDIUM -> 6.dp
            ScreenSize.EXPANDED -> 8.dp
        }
    }
    
    // Button sizing
    @Composable
    fun getButtonHeight(): Dp {
        val screenSize = getScreenSize()
        return when (screenSize) {
            ScreenSize.COMPACT -> 48.dp
            ScreenSize.MEDIUM -> 56.dp
            ScreenSize.EXPANDED -> 64.dp
        }
    }
    
    @Composable
    fun getButtonIconSize(): Dp {
        val screenSize = getScreenSize()
        return when (screenSize) {
            ScreenSize.COMPACT -> 18.dp
            ScreenSize.MEDIUM -> 20.dp
            ScreenSize.EXPANDED -> 24.dp
        }
    }
    
    // Common spacing values
    @Composable
    fun getSpacingXs(): Dp {
        val screenSize = getScreenSize()
        return when (screenSize) {
            ScreenSize.COMPACT -> 2.dp
            ScreenSize.MEDIUM -> 4.dp
            ScreenSize.EXPANDED -> 6.dp
        }
    }
    
    @Composable
    fun getSpacingS(): Dp {
        val screenSize = getScreenSize()
        return when (screenSize) {
            ScreenSize.COMPACT -> 6.dp
            ScreenSize.MEDIUM -> 8.dp
            ScreenSize.EXPANDED -> 12.dp
        }
    }
    
    @Composable
    fun getSpacingM(): Dp {
        val screenSize = getScreenSize()
        return when (screenSize) {
            ScreenSize.COMPACT -> 12.dp
            ScreenSize.MEDIUM -> 16.dp
            ScreenSize.EXPANDED -> 24.dp
        }
    }
    
    @Composable
    fun getSpacingL(): Dp {
        val screenSize = getScreenSize()
        return when (screenSize) {
            ScreenSize.COMPACT -> 20.dp
            ScreenSize.MEDIUM -> 24.dp
            ScreenSize.EXPANDED -> 32.dp
        }
    }
    
    @Composable
    fun getSpacingXl(): Dp {
        val screenSize = getScreenSize()
        return when (screenSize) {
            ScreenSize.COMPACT -> 24.dp
            ScreenSize.MEDIUM -> 32.dp
            ScreenSize.EXPANDED -> 48.dp
        }
    }
    
    @Composable
    fun getSpacingXxl(): Dp {
        val screenSize = getScreenSize()
        return when (screenSize) {
            ScreenSize.COMPACT -> 32.dp
            ScreenSize.MEDIUM -> 48.dp
            ScreenSize.EXPANDED -> 64.dp
        }
    }
    
    // Corner radius values
    @Composable
    fun getCornerRadiusS(): Dp {
        val screenSize = getScreenSize()
        return when (screenSize) {
            ScreenSize.COMPACT -> 6.dp
            ScreenSize.MEDIUM -> 8.dp
            ScreenSize.EXPANDED -> 10.dp
        }
    }
    
    @Composable
    fun getCornerRadiusM(): Dp {
        val screenSize = getScreenSize()
        return when (screenSize) {
            ScreenSize.COMPACT -> 10.dp
            ScreenSize.MEDIUM -> 12.dp
            ScreenSize.EXPANDED -> 16.dp
        }
    }
    
    @Composable
    fun getCornerRadiusL(): Dp {
        val screenSize = getScreenSize()
        return when (screenSize) {
            ScreenSize.COMPACT -> 14.dp
            ScreenSize.MEDIUM -> 16.dp
            ScreenSize.EXPANDED -> 20.dp
        }
    }
    
    // Border widths
    @Composable
    fun getBorderWidthThin(): Dp {
        val screenSize = getScreenSize()
        return when (screenSize) {
            ScreenSize.COMPACT -> 1.dp
            ScreenSize.MEDIUM -> 1.dp
            ScreenSize.EXPANDED -> 2.dp
        }
    }
    
    @Composable
    fun getBorderWidthThick(): Dp {
        val screenSize = getScreenSize()
        return when (screenSize) {
            ScreenSize.COMPACT -> 2.dp
            ScreenSize.MEDIUM -> 3.dp
            ScreenSize.EXPANDED -> 4.dp
        }
    }
    
    // Elevation values
    @Composable
    fun getElevationS(): Dp {
        val screenSize = getScreenSize()
        return when (screenSize) {
            ScreenSize.COMPACT -> 1.dp
            ScreenSize.MEDIUM -> 2.dp
            ScreenSize.EXPANDED -> 3.dp
        }
    }
    
    @Composable
    fun getElevationM(): Dp {
        val screenSize = getScreenSize()
        return when (screenSize) {
            ScreenSize.COMPACT -> 3.dp
            ScreenSize.MEDIUM -> 4.dp
            ScreenSize.EXPANDED -> 6.dp
        }
    }
    
    @Composable
    fun getElevationL(): Dp {
        val screenSize = getScreenSize()
        return when (screenSize) {
            ScreenSize.COMPACT -> 6.dp
            ScreenSize.MEDIUM -> 8.dp
            ScreenSize.EXPANDED -> 12.dp
        }
    }
}