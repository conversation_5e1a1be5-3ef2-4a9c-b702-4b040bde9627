package com.frageo.triswipe.ui.theme

import androidx.compose.material3.ButtonColors
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.ColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import com.frageo.triswipe.data.models.TileTheme

/**
 * Theme-aware button styling utilities that adapt to both Material3 color schemes
 * and selected tile themes for perfect visual integration.
 */
object ThemeAwareButtonStyles {
    
    /**
     * Get theme-aware colors for standard game buttons (Restart, Menu)
     */
    @Composable
    fun getGameButtonColors(
        tileTheme: TileTheme,
        materialColorScheme: ColorScheme,
        isEnabled: Boolean = true
    ): ButtonColors {
        val accentColor = Color(android.graphics.Color.parseColor(tileTheme.getBoardAccentColor()))
        
        return ButtonDefaults.outlinedButtonColors(
            contentColor = if (isEnabled) accentColor else materialColorScheme.onSurface.copy(alpha = 0.38f),
            containerColor = if (isEnabled) {
                materialColorScheme.surface
            } else {
                materialColorScheme.surface.copy(alpha = 0.12f)
            }
        )
    }
    
    /**
     * Get theme-aware colors for FilledTonalButton (primary actions)
     */
    @Composable
    fun getFilledTonalButtonColors(
        tileTheme: TileTheme,
        materialColorScheme: ColorScheme,
        isEnabled: Boolean = true
    ): ButtonColors {
        val accentColor = Color(android.graphics.Color.parseColor(tileTheme.getBoardAccentColor()))
        
        return ButtonDefaults.filledTonalButtonColors(
            containerColor = if (isEnabled) accentColor else materialColorScheme.surface.copy(alpha = 0.12f),
            contentColor = if (isEnabled) Color.White else materialColorScheme.onSurface.copy(alpha = 0.38f)
        )
    }
    
    /**
     * Get theme-aware colors for premium feature buttons (Undo)
     */
    @Composable
    fun getPremiumButtonColors(
        tileTheme: TileTheme,
        materialColorScheme: ColorScheme,
        isEnabled: Boolean = true,
        isPremiumUser: Boolean = true
    ): ButtonColors {
        val accentColor = Color(android.graphics.Color.parseColor(tileTheme.getBoardAccentColor()))
        
        return when {
            !isPremiumUser -> ButtonDefaults.outlinedButtonColors(
                contentColor = materialColorScheme.onSurface.copy(alpha = 0.38f),
                containerColor = materialColorScheme.surface.copy(alpha = 0.5f)
            )
            isEnabled && isPremiumUser -> ButtonDefaults.outlinedButtonColors(
                contentColor = accentColor,
                containerColor = accentColor.copy(alpha = 0.1f)
            )
            else -> ButtonDefaults.outlinedButtonColors(
                contentColor = materialColorScheme.onSurface.copy(alpha = 0.38f),
                containerColor = materialColorScheme.surface.copy(alpha = 0.12f)
            )
        }
    }
    
    /**
     * Get theme-aware icon tint color
     */
    @Composable
    fun getIconTintColor(
        tileTheme: TileTheme,
        materialColorScheme: ColorScheme,
        isEnabled: Boolean = true,
        isPremiumUser: Boolean = true
    ): Color {
        val accentColor = Color(android.graphics.Color.parseColor(tileTheme.getBoardAccentColor()))
        
        return when {
            !isPremiumUser -> materialColorScheme.onSurface.copy(alpha = 0.38f)
            isEnabled -> accentColor
            else -> materialColorScheme.onSurface.copy(alpha = 0.38f)
        }
    }
    
    /**
     * Get theme-aware text color for buttons
     */
    @Composable
    fun getTextColor(
        tileTheme: TileTheme,
        materialColorScheme: ColorScheme,
        isEnabled: Boolean = true,
        isPremiumUser: Boolean = true
    ): Color {
        val accentColor = Color(android.graphics.Color.parseColor(tileTheme.getBoardAccentColor()))
        
        return when {
            !isPremiumUser -> materialColorScheme.onSurface.copy(alpha = 0.38f)
            isEnabled -> accentColor
            else -> materialColorScheme.onSurface.copy(alpha = 0.38f)
        }
    }
    
    /**
     * Get enhanced button colors for special accent buttons
     */
    @Composable
    fun getAccentButtonColors(
        tileTheme: TileTheme,
        materialColorScheme: ColorScheme,
        isEnabled: Boolean = true
    ): ButtonColors {
        val accentColor = Color(android.graphics.Color.parseColor(tileTheme.getBoardAccentColor()))
        
        return ButtonDefaults.buttonColors(
            containerColor = if (isEnabled) accentColor else materialColorScheme.surface.copy(alpha = 0.12f),
            contentColor = if (isEnabled) {
                // Calculate contrasting text color based on accent color luminance
                val luminance = (0.299 * accentColor.red + 0.587 * accentColor.green + 0.114 * accentColor.blue)
                if (luminance > 0.5f) Color.Black else Color.White
            } else {
                materialColorScheme.onSurface.copy(alpha = 0.38f)
            }
        )
    }
}