package com.frageo.triswipe.ui.components

import androidx.compose.animation.core.animateIntAsState
import androidx.compose.animation.core.tween
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.foundation.background
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.ui.graphics.Brush
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.setValue
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowLeft
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.frageo.triswipe.data.models.TileTheme
import com.frageo.triswipe.ui.theme.Dimensions
import com.frageo.triswipe.ui.theme.AppTypography
import com.frageo.triswipe.ui.theme.ThemeAwareButtonStyles

/**
 * Calculate contrasting text color based on background luminance
 */
private fun getContrastingTextColor(backgroundColor: String): Color {
    val color = Color(android.graphics.Color.parseColor(backgroundColor))
    val luminance = (0.299 * color.red + 0.587 * color.green + 0.114 * color.blue)
    return if (luminance > 0.5f) Color.Black else Color.White
}

@Composable
fun ScoreDisplay(
    score: Int,
    modifier: Modifier = Modifier,
    tileTheme: TileTheme = TileTheme.CLASSIC
) {
    // Track animation start time when score changes
    var animationStartTime by remember { mutableStateOf(0L) }
    
    val animatedScore by animateIntAsState(
        targetValue = score,
        animationSpec = tween(
            durationMillis = 150,
            easing = FastOutSlowInEasing
        ),
        label = "score_animation",
        finishedListener = { 
            // Track animation completion time
            if (animationStartTime > 0) {
                val duration = System.currentTimeMillis() - animationStartTime
                if (duration > 200) { // Only log if significantly over target (150ms + buffer)
                }
                animationStartTime = 0L
            }
        }
    )
    
    // Monitor recomposition frequency and start timing
    LaunchedEffect(score) {
        animationStartTime = System.currentTimeMillis()
    }
    
    Card(
        modifier = modifier
            .semantics {
                contentDescription = "Current score: $score"
            },
        colors = CardDefaults.cardColors(
            containerColor = Color.Transparent
        ),
        shape = RoundedCornerShape(Dimensions.getCornerRadiusS())
    ) {
        Column(
            modifier = Modifier
                .background(
                    brush = Brush.linearGradient(
                        colors = tileTheme.getBoardBackgroundColors().let { (primary, secondary) ->
                            listOf(
                                Color(android.graphics.Color.parseColor(primary)),
                                Color(android.graphics.Color.parseColor(secondary))
                            )
                        }
                    ),
                    shape = RoundedCornerShape(Dimensions.getCornerRadiusS())
                )
                .padding(Dimensions.getSpacingM())
                .fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            val textColor = getContrastingTextColor(tileTheme.getBoardBackgroundColors().first)
            Text(
                text = "SCORE",
                color = textColor,
                fontSize = AppTypography.getBodyMedium(),
                fontWeight = FontWeight.Bold
            )
            Text(
                text = animatedScore.toString(),
                color = textColor,
                fontSize = AppTypography.getHeadlineLarge(),
                fontWeight = FontWeight.Bold,
                textAlign = TextAlign.Center
            )
        }
    }
}

@Composable
fun MovesCounter(
    moves: Int,
    modifier: Modifier = Modifier,
    tileTheme: TileTheme = TileTheme.CLASSIC
) {
    // Track animation start time when moves changes
    var animationStartTime by remember { mutableStateOf(0L) }
    
    val animatedMoves by animateIntAsState(
        targetValue = moves,
        animationSpec = tween(
            durationMillis = 150,
            easing = FastOutSlowInEasing
        ),
        label = "moves_animation",
        finishedListener = { 
            // Track animation completion time
            if (animationStartTime > 0) {
                val duration = System.currentTimeMillis() - animationStartTime
                if (duration > 200) { // Only log if significantly over target (150ms + buffer)
                }
                animationStartTime = 0L
            }
        }
    )
    
    // Monitor recomposition frequency and start timing
    LaunchedEffect(moves) {
        animationStartTime = System.currentTimeMillis()
    }
    
    Card(
        modifier = modifier
            .semantics {
                contentDescription = "Moves made: $moves"
            },
        colors = CardDefaults.cardColors(
            containerColor = Color.Transparent
        ),
        shape = RoundedCornerShape(Dimensions.getCornerRadiusS())
    ) {
        Column(
            modifier = Modifier
                .background(
                    brush = Brush.linearGradient(
                        colors = tileTheme.getBoardBackgroundColors().let { (primary, secondary) ->
                            listOf(
                                Color(android.graphics.Color.parseColor(primary)),
                                Color(android.graphics.Color.parseColor(secondary))
                            )
                        }
                    ),
                    shape = RoundedCornerShape(Dimensions.getCornerRadiusS())
                )
                .padding(Dimensions.getSpacingM())
                .fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            val textColor = getContrastingTextColor(tileTheme.getBoardBackgroundColors().first)
            Text(
                text = "MOVES",
                color = textColor,
                fontSize = AppTypography.getBodyMedium(),
                fontWeight = FontWeight.Bold
            )
            Text(
                text = animatedMoves.toString(),
                color = textColor,
                fontSize = AppTypography.getHeadlineLarge(),
                fontWeight = FontWeight.Bold,
                textAlign = TextAlign.Center
            )
        }
    }
}

@Composable
fun GameButtons(
    onMenuClick: () -> Unit,
    onUndoClick: () -> Unit = {},
    canUndo: Boolean = false,
    isPremiumUser: Boolean = false,
    modifier: Modifier = Modifier,
    tileTheme: TileTheme = TileTheme.CLASSIC
) {
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(Dimensions.getSpacingS())
    ) {
        
        // Undo button (Premium feature)
        val accentColor = Color(android.graphics.Color.parseColor(tileTheme.getBoardAccentColor()))
        OutlinedButton(
            onClick = onUndoClick,
            enabled = canUndo && isPremiumUser,
            modifier = Modifier
                .weight(1f)
                .semantics {
                    contentDescription = if (isPremiumUser) {
                        if (canUndo) "Undo last move" else "No moves to undo"
                    } else {
                        "Undo - Premium feature"
                    }
                },
            colors = ButtonDefaults.outlinedButtonColors(
                contentColor = if (isPremiumUser && canUndo) accentColor else MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
            ),
            border = BorderStroke(1.dp, if (isPremiumUser && canUndo) accentColor else MaterialTheme.colorScheme.onSurface.copy(alpha = 0.12f))
        ) {
            Icon(
                imageVector = Icons.AutoMirrored.Filled.KeyboardArrowLeft,
                contentDescription = null,
                modifier = Modifier.size(Dimensions.getButtonIconSize())
            )
            Spacer(modifier = Modifier.width(Dimensions.getSpacingXs()))
            Text(
                text = if (isPremiumUser) "UNDO" else "UNDO*",
                style = MaterialTheme.typography.labelLarge.copy(fontWeight = FontWeight.Medium),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
        }
        
        OutlinedButton(
            onClick = onMenuClick,
            modifier = Modifier
                .weight(1f)
                .semantics {
                    contentDescription = "Go to main menu"
                },
            colors = ButtonDefaults.outlinedButtonColors(
                contentColor = accentColor
            ),
            border = BorderStroke(1.dp, accentColor)
        ) {
            Icon(
                imageVector = Icons.Default.Home,
                contentDescription = null,
                modifier = Modifier.size(Dimensions.getButtonIconSize())
            )
            Spacer(modifier = Modifier.width(Dimensions.getSpacingXs()))
            Text(
                text = "MENU",
                style = MaterialTheme.typography.labelLarge.copy(fontWeight = FontWeight.Medium),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
fun GameInfoComponentsPreview() {
    Column(
        modifier = Modifier.padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            ScoreDisplay(
                score = 12540,
                modifier = Modifier.weight(1f),
                tileTheme = TileTheme.CLASSIC
            )
            MovesCounter(
                moves = 147,
                modifier = Modifier.weight(1f),
                tileTheme = TileTheme.CLASSIC
            )
        }
        
        GameButtons(
            onMenuClick = {},
            modifier = Modifier.fillMaxWidth()
        )
    }
}