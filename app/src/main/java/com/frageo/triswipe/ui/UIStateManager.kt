package com.frageo.triswipe.ui

import com.frageo.triswipe.data.models.Position
import com.frageo.triswipe.data.models.TileAnimation
import com.frageo.triswipe.data.models.TileTheme
import com.frageo.triswipe.data.models.GameEngineInterface
import com.frageo.triswipe.game.GameStateResult
import com.frageo.triswipe.viewmodel.GameUiState
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Manages UI state for the game, handling tile selection, loading states,
 * errors, and state updates from game operations.
 * 
 * This component is responsible for:
 * - Managing GameUiState and exposing it via StateFlow
 * - Handling tile selection logic
 * - Managing loading, error, and animation states
 * - Updating UI state from GameStateResult
 */
@Singleton
class UIStateManager @Inject constructor(
    private val gameEngine: GameEngineInterface
) {
    
    private val _uiState = MutableStateFlow(GameUiState.initial())
    val uiState: StateFlow<GameUiState> = _uiState.asStateFlow()
    
    private var selectedTilePosition: Position? = null
    
    /**
     * Updates UI state from a GameStateResult, typically after a game operation
     */
    fun updateFromGameStateResult(result: GameStateResult) {
        _uiState.value = GameUiState.fromGameState(result.gameState)
            .withGameStats(
                result.gameStats?.highestTile ?: 0,
                result.gameStats?.availableMoves ?: 0
            )
            .withSelectedTile(selectedTilePosition)
            .withTileAnimations(result.animations)
            .withError(result.errorMessage)
            .withLoading(false)
    }
    
    /**
     * Updates UI state with undo capability status
     */
    fun updateUndoStatus(canUndo: Boolean) {
        _uiState.value = _uiState.value.withCanUndo(canUndo)
    }
    
    /**
     * Handles tile selection logic - selects, deselects, or triggers swap
     * Returns the action that should be taken: null, 'select', 'deselect', or 'swap'
     */
    fun handleTileClick(position: Position): TileClickAction {
        val current = selectedTilePosition
        return when {
            current == null -> {
                selectedTilePosition = position
                val validSwapPositions = gameEngine.getValidSwapPositions(position)
                _uiState.value = _uiState.value
                    .withSelectedTile(position)
                    .withValidSwapPositions(validSwapPositions)
                    .withSwapModeStatus(gameEngine.getSwapModeStatus())
                TileClickAction.Select(position)
            }
            current == position -> {
                clearSelection()
                TileClickAction.Deselect
            }
            else -> {
                val action = TileClickAction.Swap(current, position)
                clearSelection()
                action
            }
        }
    }
    
    /**
     * Selects a tile at the given position
     */
    fun selectTile(position: Position) {
        selectedTilePosition = position
        _uiState.value = _uiState.value.withSelectedTile(position)
    }
    
    /**
     * Clears the current tile selection
     */
    fun clearSelection() {
        selectedTilePosition = null
        _uiState.value = _uiState.value
            .withSelectedTile(null)
            .withValidSwapPositions(emptyList())
            .withSwapModeStatus(gameEngine.getSwapModeStatus())
    }
    
    /**
     * Sets the loading state
     */
    fun setLoading(isLoading: Boolean) {
        _uiState.value = _uiState.value.withLoading(isLoading)
        if (isLoading) {
            // Clear animations when starting a new operation
            _uiState.value = _uiState.value.withTileAnimations(emptyList())
        }
    }
    
    /**
     * Sets an error message
     */
    fun setError(message: String?) {
        _uiState.value = _uiState.value
            .withError(message)
            .withLoading(false)
    }
    
    /**
     * Clears the current error message
     */
    fun clearError() {
        _uiState.value = _uiState.value.withError(null)
    }
    
    /**
     * Updates the selected tile theme
     */
    fun updateTheme(theme: TileTheme) {
        _uiState.value = _uiState.value.withSelectedTileTheme(theme)
    }
    
    /**
     * Sets tile animations
     */
    fun setAnimations(animations: List<TileAnimation>) {
        _uiState.value = _uiState.value.withTileAnimations(animations)
    }
    
    /**
     * Clears all tile animations
     */
    fun clearAnimations() {
        _uiState.value = _uiState.value.withTileAnimations(emptyList())
    }
    
    /**
     * Sets swapping state
     */
    fun setSwapping(isSwapping: Boolean) {
        _uiState.value = _uiState.value.withSwapping(isSwapping)
    }
    
    /**
     * Sets merging state
     */
    fun setMerging(isMerging: Boolean) {
        _uiState.value = _uiState.value.withMerging(isMerging)
    }
    
    /**
     * Gets the current UI state value
     */
    fun getCurrentUiState(): GameUiState = _uiState.value
    
    /**
     * Gets the currently selected tile position
     */
    fun getSelectedTilePosition(): Position? = selectedTilePosition
}

/**
 * Represents the action to take after a tile click
 */
sealed class TileClickAction {
    data class Select(val position: Position) : TileClickAction()
    object Deselect : TileClickAction()
    data class Swap(val position1: Position, val position2: Position) : TileClickAction()
}