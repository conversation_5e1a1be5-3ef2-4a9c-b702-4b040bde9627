package com.frageo.triswipe.ui.components

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.tween
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.VectorConverter
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.layout.*
import androidx.compose.ui.graphics.Brush
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.frageo.triswipe.data.models.GameConfig
import com.frageo.triswipe.data.models.Position
import com.frageo.triswipe.data.models.Tile
import com.frageo.triswipe.data.models.Direction
import com.frageo.triswipe.data.models.TileOrigin
import com.frageo.triswipe.data.models.TileAnimation
import com.frageo.triswipe.data.models.TileAnimationType
import com.frageo.triswipe.data.models.TileTheme
import com.frageo.triswipe.ui.theme.Dimensions
import kotlin.math.abs

@Composable
fun AnimatedTileComponent(
    tile: Tile?,
    animation: TileAnimation? = null,
    isSelected: Boolean = false,
    isValidSwapTarget: Boolean = false,
    isSwapping: Boolean = false,
    onTileClick: (Position) -> Unit,
    modifier: Modifier = Modifier,
    onTileAnimationComplete: ((Position, TileOrigin) -> Unit)? = null,
    tileSize: androidx.compose.ui.unit.Dp = Dimensions.getTileSize(),
    spacing: androidx.compose.ui.unit.Dp = Dimensions.getTileSpacing(),
    gridOffset: androidx.compose.ui.unit.Dp = Dimensions.getGridOffset(),
    theme: TileTheme = TileTheme.CLASSIC
) {
    if (tile == null) return
    
    // Use provided responsive measurements
    val totalTileSize = tileSize + spacing
    
    // Determine positions based on animation type
    val (fromPosition, toPosition) = if (animation != null) {
        animation.fromPosition to animation.toPosition
    } else {
        tile.position to tile.position
    }
    
    // Create initial and target offsets with grid centering
    val initialOffset = Offset(
        x = (gridOffset.value + fromPosition.col * totalTileSize.value),
        y = (gridOffset.value + fromPosition.row * totalTileSize.value)
    )

    val targetOffset = Offset(
        x = (gridOffset.value + toPosition.col * totalTileSize.value),
        y = (gridOffset.value + toPosition.row * totalTileSize.value)
    )

    // Use Animatable for precise control over tile position
    // Key with both tile.id and animation to ensure fresh state for new animations
    val animatableOffset = remember(tile.id, animation?.animationType, animation?.fromPosition) { 
        Animatable(initialOffset, Offset.VectorConverter) 
    }
    
    // Reset position when tile gets new animation
    LaunchedEffect(tile.id, animation?.fromPosition, animation?.toPosition) {
        // Always reset to initial position when animation changes
        animatableOffset.snapTo(initialOffset)
    }
    
    // Handle animation when animation data changes
    LaunchedEffect(animation, tile.id, animation?.fromPosition) {
        if (animation != null) {
            // Apply delay before starting animation
            if (animation.delay > 0) {
                kotlinx.coroutines.delay(animation.delay.toLong())
            }
            
            // Animate position from start to target
            animatableOffset.animateTo(
                targetValue = targetOffset,
                animationSpec = when (animation.animationType) {
                    TileAnimationType.MOVE -> when {
                        isSwapping -> spring<Offset>(
                            dampingRatio = Spring.DampingRatioMediumBouncy,
                            stiffness = Spring.StiffnessMediumLow
                        )
                        else -> tween<Offset>(
                            durationMillis = animation.duration,
                            easing = FastOutSlowInEasing
                        )
                    }
                    TileAnimationType.MERGE_TO_CENTER -> tween<Offset>(
                        durationMillis = animation.duration,
                        easing = FastOutSlowInEasing
                    )
                }
            )
            
            // Handle animation completion
            if (animation.animationType == TileAnimationType.MERGE_TO_CENTER) {
                onTileAnimationComplete?.invoke(animation.toPosition, TileOrigin.MERGE)
            }
        } else {
            // No animation - snap to tile position immediately
            animatableOffset.snapTo(initialOffset)
        }
    }
    
    // Animation progress for merge animations (alpha and scale effects)
    // Key with detailed animation info to ensure fresh state
    var animationProgress by remember(tile.id, animation?.animationType, animation?.fromPosition, animation?.toPosition) { 
        mutableFloatStateOf(0f) 
    }
    
    // Trigger progress animation when merge animation starts
    LaunchedEffect(animation?.animationType, animation?.fromPosition, animation?.toPosition) {
        if (animation?.animationType == TileAnimationType.MERGE_TO_CENTER) {
            // Apply delay before starting progress animation
            if (animation.delay > 0) {
                kotlinx.coroutines.delay(animation.delay.toLong())
            }
            // Add small delay to ensure position animation has started
            kotlinx.coroutines.delay(50L)
            animationProgress = 1f
        } else {
            animationProgress = 0f
        }
    }
    
    val animatedProgress by animateFloatAsState(
        targetValue = animationProgress,
        animationSpec = when (animation?.animationType) {
            TileAnimationType.MERGE_TO_CENTER -> tween<Float>(
                durationMillis = animation.duration,
                easing = FastOutSlowInEasing
            )
            else -> tween<Float>(durationMillis = 0)
        },
        label = "animation_progress"
    )
    
    // Calculate alpha and scale for merge animations
    val alpha = if (animation?.animationType == TileAnimationType.MERGE_TO_CENTER) {
        animation.getAlpha(animatedProgress)
    } else {
        1f
    }
    
    val scale = if (animation?.animationType == TileAnimationType.MERGE_TO_CENTER) {
        animation.getScale(animatedProgress)
    } else {
        1f
    }
    
    Box(
        modifier = modifier
            .offset(
                x = animatableOffset.value.x.dp,
                y = animatableOffset.value.y.dp
            )
            .graphicsLayer {
                this.alpha = alpha
                scaleX = scale
                scaleY = scale
            }
    ) {
        TileComponent(
            tile = tile,
            position = toPosition, // Use target position for click handling
            isSelected = isSelected,
            isValidSwapTarget = isValidSwapTarget,
            isMerging = animation?.animationType == TileAnimationType.MERGE_TO_CENTER,
            onTileClick = onTileClick,
            onAnimationComplete = onTileAnimationComplete,
            isMoving = animation != null,
            theme = theme
        )
    }
}


@Composable
fun GameBoardComponent(
    board: Array<Array<Tile?>>,
    selectedPosition: Position? = null,
    validSwapPositions: List<Position> = emptyList(),
    isSwapping: Boolean = false,
    isMerging: Boolean = false,
    tileAnimations: List<TileAnimation> = emptyList(),
    onSwipeGesture: (Direction) -> Unit,
    onTileClick: (Position) -> Unit,
    modifier: Modifier = Modifier,
    onTileAnimationComplete: ((Position, TileOrigin) -> Unit)? = null,
    tileTheme: TileTheme = TileTheme.CLASSIC
) {
    val density = LocalDensity.current
    val configuration = LocalConfiguration.current
    val swipeThreshold = with(density) { GameConfig.SWIPE_THRESHOLD.toDp() }
    
    // Use responsive sizing system
    val tileSize = Dimensions.getTileSize()
    val spacing = Dimensions.getTileSpacing()
    val boardPadding = Dimensions.getBoardPadding()
    val gridOffset = Dimensions.getGridOffset()
    
    // Calculate board size based on responsive tile size
    val totalTileSize = tileSize + spacing
    val gridWidth = (tileSize * 4) + (spacing * 3)
    val boardSize = gridWidth + (boardPadding * 2)
    
    // Get theme-aware board background colors
    val (primaryColor, secondaryColor) = tileTheme.getBoardBackgroundColors()
    
    Box(
        modifier = modifier
            .size(boardSize)
            .background(
                brush = Brush.linearGradient(
                    colors = listOf(
                        Color(android.graphics.Color.parseColor(primaryColor)),
                        Color(android.graphics.Color.parseColor(secondaryColor))
                    )
                ),
                shape = RoundedCornerShape(Dimensions.getCornerRadiusM())
            )
            .padding(boardPadding)
            .pointerInput(Unit) {
                var startPosition: Offset? = null
                var swipeTriggered = false
                val swipeThresholdPx = swipeThreshold.toPx()
                
                detectDragGestures(
                    onDragStart = { offset ->
                        startPosition = offset
                        swipeTriggered = false
                    },
                    onDragEnd = { 
                        startPosition = null
                        swipeTriggered = false
                    }
                ) { change, _ ->
                    if (swipeTriggered) return@detectDragGestures
                    
                    val start = startPosition ?: return@detectDragGestures
                    val totalDragX = change.position.x - start.x
                    val totalDragY = change.position.y - start.y
                    
                    val absX = abs(totalDragX)
                    val absY = abs(totalDragY)
                    
                    // Provide immediate visual feedback with reduced threshold
                    if (absX > swipeThresholdPx || absY > swipeThresholdPx) {
                        val direction = when {
                            absX > absY -> {
                                if (totalDragX > 0) Direction.RIGHT else Direction.LEFT
                            }
                            totalDragY > 0 -> Direction.DOWN
                            else -> Direction.UP
                        }
                        onSwipeGesture(direction)
                        swipeTriggered = true
                    }
                }
            }
            .semantics {
                contentDescription = "Game board with 4x4 grid. Swipe to move tiles, tap adjacent tiles to swap them."
            },
        contentAlignment = Alignment.TopStart
    ) {
        Box(
            modifier = Modifier.fillMaxSize()
        ) {
            // Pre-compute animation lookup for performance
            val animationLookup = tileAnimations.associateBy { it.tile.id }
            
            // First pass: Always render empty tile backgrounds for all positions
            for (row in 0 until GameConfig.BOARD_SIZE) {
                for (col in 0 until GameConfig.BOARD_SIZE) {
                    val position = Position(row, col)
                    TileComponent(
                        tile = null,
                        position = position,
                        isSelected = selectedPosition == position,
                        isValidSwapTarget = position in validSwapPositions,
                        isMerging = false,
                        onTileClick = onTileClick,
                        onAnimationComplete = onTileAnimationComplete,
                        tileSize = tileSize,
                        isMoving = false,
                        theme = tileTheme,
                        modifier = Modifier
                            .offset(
                                x = gridOffset + (col * totalTileSize.value).dp,
                                y = gridOffset + (row * totalTileSize.value).dp
                            )
                    )
                }
            }
            
            // Second pass: Render tiles with unified animations (excluding merge animations)
            for (row in 0 until GameConfig.BOARD_SIZE) {
                for (col in 0 until GameConfig.BOARD_SIZE) {
                    val position = Position(row, col)
                    val tile = board[row][col]
                    
                    if (tile != null) {
                        val animation = animationLookup[tile.id]
                        val targetPos = animation?.toPosition ?: position
                        
                        // Skip tiles that are part of merge animations - they'll be rendered in third pass
                        if (animation?.animationType != TileAnimationType.MERGE_TO_CENTER) {
                            AnimatedTileComponent(
                                tile = tile,
                                animation = animation,
                                isSelected = selectedPosition == targetPos,
                                isValidSwapTarget = targetPos in validSwapPositions,
                                isSwapping = isSwapping,
                                onTileClick = onTileClick,
                                onTileAnimationComplete = onTileAnimationComplete,
                                tileSize = tileSize,
                                spacing = spacing,
                                gridOffset = gridOffset,
                                theme = tileTheme
                            )
                        }
                    }
                }
            }
            
            // Third pass: Render ALL merge animations (both on-board and off-board tiles)
            tileAnimations
                .filter { it.animationType == TileAnimationType.MERGE_TO_CENTER }
                .forEach { animation ->
                    AnimatedTileComponent(
                        tile = animation.tile,
                        animation = animation,
                        isSelected = selectedPosition == animation.toPosition,
                        isValidSwapTarget = animation.toPosition in validSwapPositions,
                        isSwapping = false,
                        onTileClick = onTileClick,
                        onTileAnimationComplete = onTileAnimationComplete,
                        tileSize = tileSize,
                        spacing = spacing,
                        gridOffset = gridOffset,
                        theme = tileTheme
                    )
                }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun GameBoardComponentPreview() {
    val sampleBoard = Array(4) { row ->
        Array(4) { col ->
            when {
                row == 0 && col == 0 -> Tile(1, Position(0, 0))
                row == 0 && col == 1 -> Tile(3, Position(0, 1))
                row == 0 && col == 2 -> Tile(9, Position(0, 2))
                row == 1 && col == 0 -> Tile(27, Position(1, 0))
                row == 1 && col == 1 -> Tile(81, Position(1, 1))
                row == 1 && col == 2 -> Tile(243, Position(1, 2))
                row == 2 && col == 0 -> Tile(729, Position(2, 0))
                row == 2 && col == 1 -> Tile(2187, Position(2, 1))
                else -> null
            }
        }
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        GameBoardComponent(
            board = sampleBoard,
            selectedPosition = Position(1, 1),
            isSwapping = false,
            isMerging = false,
            tileAnimations = emptyList(),
            onSwipeGesture = {},
            onTileClick = {}
        )
    }
}