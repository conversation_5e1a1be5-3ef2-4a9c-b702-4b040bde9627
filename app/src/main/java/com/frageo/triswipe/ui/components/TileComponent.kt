package com.frageo.triswipe.ui.components

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.keyframes
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.tween
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.animateFloat
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.role
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.frageo.triswipe.data.models.GameConfig
import com.frageo.triswipe.data.models.Position
import com.frageo.triswipe.data.models.Tile
import com.frageo.triswipe.data.models.TileOrigin
import com.frageo.triswipe.ui.theme.Dimensions
import com.frageo.triswipe.ui.theme.AppTypography
import androidx.compose.foundation.BorderStroke
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.sp

/**
 * Helper functions for enhanced tile visual distinction
 */

/**
 * Get corner radius based on tile value for visual distinction
 */
@Composable
private fun getTileCornerRadius(value: Int?): Dp {
    val baseRadius = Dimensions.getCornerRadiusS()
    return when (value) {
        null -> baseRadius
        1, 3 -> baseRadius // Small corners for low values
        9, 27 -> (baseRadius.value * 1.2f).dp // Medium corners
        81, 243 -> (baseRadius.value * 1.4f).dp // Larger corners
        else -> (baseRadius.value * 1.6f).dp // Largest corners for high values
    }
}

/**
 * Get enhanced border for tile based on value and theme
 */
private fun getTileBorder(value: Int?, theme: com.frageo.triswipe.data.models.TileTheme): BorderStroke? {
    if (value == null) return null
    
    val borderColor = when (theme) {
        com.frageo.triswipe.data.models.TileTheme.CLASSIC -> Color(0xFF8F7A66)
        com.frageo.triswipe.data.models.TileTheme.MIDNIGHT -> Color(0xFF7070A0)
        com.frageo.triswipe.data.models.TileTheme.OCEAN -> Color(0xFF4A90B8)
        com.frageo.triswipe.data.models.TileTheme.FOREST -> Color(0xFF5A8C58)
        com.frageo.triswipe.data.models.TileTheme.SUNSET -> Color(0xFF8C3A80)
        com.frageo.triswipe.data.models.TileTheme.NEON -> Color(0xFFFF00FF)
        com.frageo.triswipe.data.models.TileTheme.MONOCHROME -> Color(0xFF666666)
    }
    
    val borderWidth = when (value) {
        1, 3 -> 1.dp // Thin border for low values
        9, 27 -> 1.5.dp // Medium border
        81, 243 -> 2.dp // Thick border
        else -> 2.5.dp // Thickest border for high values
    }
    
    return BorderStroke(borderWidth, borderColor)
}

/**
 * Get enhanced font weight based on tile value
 */
private fun getTileFontWeight(value: Int?): FontWeight {
    return when (value) {
        null -> FontWeight.Bold
        1, 3 -> FontWeight.Medium // Lighter weight for small values
        9, 27 -> FontWeight.SemiBold // Medium weight
        81, 243 -> FontWeight.Bold // Bold for medium-high values
        else -> FontWeight.ExtraBold // Heaviest weight for high values
    }
}

/**
 * Get font style based on tile value for additional distinction
 */
private fun getTileFontStyle(value: Int?): FontStyle {
    return when (value) {
        null -> FontStyle.Normal
        729, 2187 -> FontStyle.Italic // Italic for high values to make them special
        else -> FontStyle.Normal
    }
}

/**
 * Get letter spacing for better readability of larger numbers
 */
private fun getTileLetterSpacing(value: Int?): androidx.compose.ui.unit.TextUnit {
    return when (value) {
        null -> 0.sp
        1, 3, 9 -> 0.sp // Normal spacing for single digits
        27, 81 -> 0.5.sp // Slight spacing for double digits
        243, 729 -> 1.sp // More spacing for triple digits
        else -> 1.5.sp // Maximum spacing for 4+ digits
    }
}

@Composable
fun TileComponent(
    tile: Tile?,
    position: Position,
    isSelected: Boolean = false,
    isValidSwapTarget: Boolean = false,
    isMerging: Boolean = false,
    onTileClick: (Position) -> Unit,
    modifier: Modifier = Modifier,
    theme: com.frageo.triswipe.data.models.TileTheme = com.frageo.triswipe.data.models.TileTheme.CLASSIC,
    onAnimationComplete: ((Position, TileOrigin) -> Unit)? = null,
    tileSize: androidx.compose.ui.unit.Dp = Dimensions.getTileSize(),
    isMoving: Boolean = false
) {
    // Initialize hasAppeared based on whether this is a genuine spawn tile (not moving)
    var hasAppeared by remember(tile?.id) { 
        mutableStateOf(tile?.origin != TileOrigin.SPAWN || isMoving) 
    }
    var hasMerged by remember { mutableStateOf(false) }
    
    // Different animations based on tile origin
    // Only show spawn animation for genuine new tiles (SPAWN origin) that are NOT moving via animation
    // This ensures tiles that moved via swipe don't show spawn animation at their destination
    val isSpawnTile = tile?.origin == TileOrigin.SPAWN && !isMoving
    val isMergeTile = tile?.origin == TileOrigin.MERGE || isMerging
    
    // Enhanced spawn animation with anticipation and overshoot
    val spawnScale by animateFloatAsState(
        targetValue = if (hasAppeared && isSpawnTile) 1f else if (isSpawnTile) 0f else 1f,
        animationSpec = keyframes {
            durationMillis = 350
            if (isSpawnTile) {
                0f at 0 using FastOutSlowInEasing
                1.15f at 175 using FastOutSlowInEasing
                1f at 350
            }
        },
        label = "tile_spawn_scale"
    )
    
    val spawnAlpha by animateFloatAsState(
        targetValue = if (hasAppeared && isSpawnTile) 1f else if (isSpawnTile) 0f else 1f,
        animationSpec = keyframes {
            durationMillis = 300
            if (isSpawnTile) {
                0f at 0
                0.7f at 120 using FastOutSlowInEasing
                1f at 300
            }
        },
        label = "tile_spawn_alpha"
    )
    
    // Enhanced merge animation with dramatic emphasis
    val mergeScale by animateFloatAsState(
        targetValue = if (hasMerged) 1f else 1.3f,
        animationSpec = keyframes {
            durationMillis = 250
            1.3f at 0 // Start big
            1.15f at 125 using FastOutSlowInEasing
            1f at 250 // End normal
        },
        label = "tile_merge_scale"
    )
    
    // Enhanced merge rotation for extra emphasis
    val mergeRotation by animateFloatAsState(
        targetValue = if (hasMerged) 0f else 5f,
        animationSpec = keyframes {
            durationMillis = 200
            5f at 0 // Start tilted
            -2f at 100 using FastOutSlowInEasing
            0f at 200 // End normal
        },
        label = "tile_merge_rotation"
    )
    
    // Subtle pulse animation for valid swap targets
    val infiniteTransition = rememberInfiniteTransition(label = "swap_target_pulse")
    val swapTargetScale by infiniteTransition.animateFloat(
        initialValue = 1f,
        targetValue = 1.05f,
        animationSpec = infiniteRepeatable(
            animation = tween(1000, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "swap_target_scale"
    )
    
    LaunchedEffect(tile?.id, tile?.origin, isMoving) {
        if (tile != null) {
            if (tile.origin == TileOrigin.SPAWN && !hasAppeared && !isMoving) {
                // Only trigger spawn animation for genuine new tiles (not moving via swipe)
                hasAppeared = true
                // Reset spawn animation after delay for spawn tiles
                kotlinx.coroutines.delay(320) // Wait for enhanced animation to complete (30ms earlier to sync with movement)
                onAnimationComplete?.invoke(position, TileOrigin.SPAWN)
            } else if (tile.origin != TileOrigin.SPAWN || isMoving) {
                // For tiles that are not spawning OR are moving via animation, just mark as appeared
                hasAppeared = true
            }
        } else {
            hasAppeared = false
        }
    }
    
    LaunchedEffect(isMerging) {
        if (isMerging) {
            hasMerged = false // Start animation
            kotlinx.coroutines.delay(30)
            hasMerged = true // Trigger animation to normal state
            // Reset merge animation after completion
            kotlinx.coroutines.delay(250) // Wait for enhanced animation to complete
            onAnimationComplete?.invoke(position, TileOrigin.MERGE)
        }
    }
    
    // Initialize merge animation for tiles with MERGE origin
    LaunchedEffect(tile?.origin) {
        if (tile?.origin == TileOrigin.MERGE) {
            hasMerged = false // Start in animated state
            kotlinx.coroutines.delay(30)
            hasMerged = true // Animate to normal state
            // Reset merge animation after completion
            kotlinx.coroutines.delay(250)
            onAnimationComplete?.invoke(position, TileOrigin.MERGE)
        }
    }
    
    val backgroundColor = when {
        tile == null -> {
            val (primaryColor, _) = theme.getBoardBackgroundColors()
            val baseColor = Color(android.graphics.Color.parseColor(primaryColor))
            
            // Add subtle tint for valid swap targets
            if (isValidSwapTarget) {
                val surfaceColor = MaterialTheme.colorScheme.surface
                val isLightTheme = (surfaceColor.red + surfaceColor.green + surfaceColor.blue) / 3f > 0.5f
                
                val tintColor = if (isLightTheme) {
                    Color(0xFF4CAF50).copy(alpha = 0.15f) // Light green tint for light theme
                } else {
                    Color(0xFF4CAF50).copy(alpha = 0.25f) // Slightly stronger tint for dark theme
                }
                baseColor.copy(
                    red = (baseColor.red + tintColor.red * tintColor.alpha).coerceAtMost(1f),
                    green = (baseColor.green + tintColor.green * tintColor.alpha).coerceAtMost(1f),
                    blue = (baseColor.blue + tintColor.blue * tintColor.alpha).coerceAtMost(1f)
                )
            } else {
                baseColor
            }
        }
        else -> {
            val baseColor = Color(android.graphics.Color.parseColor(GameConfig.getTileColor(tile.value, theme)))
            
            // Add subtle tint for valid swap targets on filled tiles too
            if (isValidSwapTarget) {
                val surfaceColor = MaterialTheme.colorScheme.surface
                val isLightTheme = (surfaceColor.red + surfaceColor.green + surfaceColor.blue) / 3f > 0.5f
                
                val tintColor = if (isLightTheme) {
                    Color(0xFF4CAF50).copy(alpha = 0.1f) // Subtle tint for light theme
                } else {
                    Color(0xFF4CAF50).copy(alpha = 0.15f) // Slightly stronger tint for dark theme
                }
                baseColor.copy(
                    red = (baseColor.red + tintColor.red * tintColor.alpha).coerceAtMost(1f),
                    green = (baseColor.green + tintColor.green * tintColor.alpha).coerceAtMost(1f),
                    blue = (baseColor.blue + tintColor.blue * tintColor.alpha).coerceAtMost(1f)
                )
            } else {
                baseColor
            }
        }
    }
    
    val textColor = when {
        tile == null -> Color.Transparent
        else -> Color(android.graphics.Color.parseColor(GameConfig.getTileTextColor(tile.value, theme)))
    }
    
    val borderColor = when {
        isSelected -> MaterialTheme.colorScheme.primary
        isValidSwapTarget -> {
            // Use a more visible color that works in both light and dark themes
            val surfaceColor = MaterialTheme.colorScheme.surface
            val isLightTheme = (surfaceColor.red + surfaceColor.green + surfaceColor.blue) / 3f > 0.5f
            
            if (isLightTheme) {
                // Light theme - use a darker, more visible color
                Color(0xFF2E7D32) // Dark green
            } else {
                // Dark theme - use a brighter color
                Color(0xFF4CAF50) // Bright green
            }
        }
        else -> {
            val (_, secondaryColor) = theme.getBoardBackgroundColors()
            Color(android.graphics.Color.parseColor(secondaryColor))
        }
    }
    
    val borderWidth = when {
        isSelected -> Dimensions.getBorderWidthThick()
        isValidSwapTarget -> Dimensions.getBorderWidthThick() // Make it as thick as selected
        else -> Dimensions.getBorderWidthThin()
    }
    
    Card(
        modifier = modifier
            .size(tileSize)
            .graphicsLayer {
                val pulseScale = if (isValidSwapTarget) swapTargetScale else 1f
                val finalScale = when {
                    tile == null -> pulseScale // Apply pulse to empty tiles
                    isSpawnTile -> spawnScale * pulseScale // Combine animations
                    isMergeTile -> mergeScale * pulseScale // Combine animations
                    else -> pulseScale // Apply pulse to filled tiles
                }
                val finalAlpha = when {
                    tile == null -> 1f
                    isSpawnTile -> spawnAlpha
                    else -> 1f
                }
                val finalRotation = when {
                    tile == null -> 0f
                    isMergeTile -> mergeRotation
                    else -> 0f
                }
                scaleX = finalScale
                scaleY = finalScale
                alpha = finalAlpha
                rotationZ = finalRotation
            }
            .border(
                width = borderWidth,
                color = borderColor,
                shape = RoundedCornerShape(Dimensions.getCornerRadiusS())
            )
            .clickable(
                role = Role.Button,
                onClick = { onTileClick(position) }
            )
            .semantics {
                contentDescription = if (tile != null) {
                    "Tile with value ${tile.value} at position ${position.row + 1}, ${position.col + 1}"
                } else {
                    "Empty tile at position ${position.row + 1}, ${position.col + 1}"
                }
                role = Role.Button
            },
        colors = CardDefaults.cardColors(
            containerColor = backgroundColor
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = if (tile != null) Dimensions.getElevationM() else Dimensions.getElevationS(),
            pressedElevation = if (tile != null) Dimensions.getElevationL() else Dimensions.getElevationM()
        ),
        shape = RoundedCornerShape(getTileCornerRadius(tile?.value)),
        border = getTileBorder(tile?.value, theme)
    ) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            if (tile != null) {
                Text(
                    text = tile.value.toString(),
                    color = textColor,
                    fontSize = AppTypography.getTileTextSize(tile.value),
                    fontWeight = getTileFontWeight(tile.value),
                    fontStyle = getTileFontStyle(tile.value),
                    letterSpacing = getTileLetterSpacing(tile.value),
                    textAlign = TextAlign.Center
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun TileComponentPreview() {
    Column(
        modifier = Modifier.padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Row(horizontalArrangement = Arrangement.spacedBy(8.dp)) {
            TileComponent(
                tile = null,
                position = Position(0, 0),
                onTileClick = {}
            )
            TileComponent(
                tile = Tile(1, Position(0, 1)),
                position = Position(0, 1),
                onTileClick = {}
            )
            TileComponent(
                tile = Tile(3, Position(0, 2)),
                position = Position(0, 2),
                onTileClick = {}
            )
        }
        Row(horizontalArrangement = Arrangement.spacedBy(8.dp)) {
            TileComponent(
                tile = Tile(9, Position(1, 0)),
                position = Position(1, 0),
                onTileClick = {}
            )
            TileComponent(
                tile = Tile(27, Position(1, 1)),
                position = Position(1, 1),
                isSelected = true,
                onTileClick = {}
            )
            TileComponent(
                tile = Tile(81, Position(1, 2)),
                position = Position(1, 2),
                onTileClick = {}
            )
        }
        Row(horizontalArrangement = Arrangement.spacedBy(8.dp)) {
            TileComponent(
                tile = Tile(243, Position(2, 0)),
                position = Position(2, 0),
                onTileClick = {}
            )
            TileComponent(
                tile = Tile(729, Position(2, 1)),
                position = Position(2, 1),
                onTileClick = {}
            )
            TileComponent(
                tile = Tile(2187, Position(2, 2)),
                position = Position(2, 2),
                onTileClick = {}
            )
        }
    }
}