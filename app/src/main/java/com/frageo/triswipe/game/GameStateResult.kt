package com.frageo.triswipe.game

import com.frageo.triswipe.data.models.GameState
import com.frageo.triswipe.data.models.GameStatistics
import com.frageo.triswipe.data.models.Position
import com.frageo.triswipe.data.models.TileAnimation

/**
 * Result class for game state operations, providing structured feedback
 * from GameStateManager operations to the UI layer.
 */
data class GameStateResult(
    val success: Boolean,
    val gameState: GameState,
    val animations: List<TileAnimation> = emptyList(),
    val visualMovements: Map<Position, Position> = emptyMap(),
    val errorMessage: String? = null,
    val gameStats: GameStatistics? = null
) {
    companion object {
        /**
         * Creates a successful GameStateResult
         */
        fun success(
            gameState: GameState,
            animations: List<TileAnimation> = emptyList(),
            visualMovements: Map<Position, Position> = emptyMap(),
            gameStats: GameStatistics? = null
        ): GameStateResult {
            return GameStateResult(
                success = true,
                gameState = gameState,
                animations = animations,
                visualMovements = visualMovements,
                errorMessage = null,
                gameStats = gameStats
            )
        }
        
        /**
         * Creates a failed GameStateResult
         */
        fun failure(
            gameState: GameState,
            errorMessage: String,
            gameStats: GameStatistics? = null
        ): GameStateResult {
            return GameStateResult(
                success = false,
                gameState = gameState,
                animations = emptyList(),
                visualMovements = emptyMap(),
                errorMessage = errorMessage,
                gameStats = gameStats
            )
        }
    }
    
    /**
     * Checks if the operation was successful
     */
    fun isSuccess(): Boolean = success
    
    /**
     * Checks if the operation failed
     */
    fun isFailure(): Boolean = !success
    
    /**
     * Returns error message if failed, null if successful
     */
    fun getErrorOrNull(): String? = if (isFailure()) errorMessage else null
    
    /**
     * Returns the result if successful, throws exception if failed
     */
    fun getOrThrow(): GameStateResult {
        if (isFailure()) {
            throw GameStateException(errorMessage ?: "Game state operation failed")
        }
        return this
    }
}

/**
 * Exception thrown when game state operations fail
 */
class GameStateException(message: String) : Exception(message)