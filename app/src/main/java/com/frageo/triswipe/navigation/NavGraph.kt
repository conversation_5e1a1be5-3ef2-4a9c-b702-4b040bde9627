package com.frageo.triswipe.navigation

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.frageo.triswipe.ui.screens.GameScreen
import com.frageo.triswipe.ui.screens.MainMenuScreen
import com.frageo.triswipe.ui.screens.SettingsScreen
import com.frageo.triswipe.viewmodel.GameViewModel

object NavDestinations {
    const val MAIN_MENU = "main_menu"
    const val GAME = "game"
    const val SETTINGS = "settings"
}

@Composable
fun TriSwipeNavGraph(
    modifier: Modifier = Modifier,
    navController: NavHostController = rememberNavController(),
    startDestination: String = NavDestinations.MAIN_MENU
) {
    // Single shared ViewModel instance across navigation
    val gameViewModel: GameViewModel = viewModel()
    
    NavHost(
        navController = navController,
        startDestination = startDestination,
        modifier = modifier
    ) {
        composable(NavDestinations.MAIN_MENU) {
            MainMenuScreen(
                onNavigateToGame = {
                    navController.navigate(NavDestinations.GAME) {
                        // Don't create multiple instances of game screen
                        launchSingleTop = true
                    }
                },
                onNavigateToSettings = {
                    navController.navigate(NavDestinations.SETTINGS)
                },
                gameViewModel = gameViewModel
            )
        }
        
        composable(NavDestinations.GAME) {
            GameScreen(
                onNavigateToMenu = {
                    navController.navigate(NavDestinations.MAIN_MENU) {
                        // Clear back stack when going to main menu
                        popUpTo(NavDestinations.MAIN_MENU) {
                            inclusive = true
                        }
                        launchSingleTop = true
                    }
                },
                gameViewModel = gameViewModel
            )
        }
        
        composable(NavDestinations.SETTINGS) {
            SettingsScreen(
                onNavigateBack = {
                    navController.popBackStack()
                },
                gameViewModel = gameViewModel
            )
        }
    }
}