package com.frageo.triswipe.viewmodel

import com.frageo.triswipe.data.models.GameState
import com.frageo.triswipe.data.models.Tile
import com.frageo.triswipe.data.models.Position
import com.frageo.triswipe.data.models.TileAnimation
import com.frageo.triswipe.data.models.TileTheme

data class GameUiState(
    val board: Array<Array<Tile?>>,
    val score: Int = 0,
    val moves: Int = 0,
    val isGameOver: Boolean = false,
    val hasWon: Boolean = false,
    val isLoading: Boolean = false,
    val errorMessage: String? = null,
    val lastMoveType: String? = null,
    val selectedTile: Position? = null,
    val canUndo: Boolean = false,
    val highestTile: Int = 0,
    val availableMovesCount: Int = 0,
    val isSwapping: Boolean = false,
    val isMerging: Boolean = false,
    val tileAnimations: List<TileAnimation> = emptyList(),
    val selectedTileTheme: TileTheme = TileTheme.CLASSIC,
    val validSwapPositions: List<Position> = emptyList(),
    val swapModeStatus: String? = null
) {
    companion object {
        fun fromGameState(gameState: GameState): GameUiState {
            return GameUiState(
                board = gameState.board,
                score = gameState.score,
                moves = gameState.moves,
                isGameOver = gameState.isGameOver,
                hasWon = gameState.hasWon,
                lastMoveType = gameState.lastMoveType
            )
        }
        
        fun initial(): GameUiState {
            val emptyBoard = Array(4) { Array<Tile?>(4) { null } }
            return GameUiState(
                board = emptyBoard,
                isLoading = true
            )
        }
    }
    
    fun withLoading(loading: Boolean): GameUiState {
        return copy(isLoading = loading)
    }
    
    fun withError(error: String?): GameUiState {
        return copy(errorMessage = error, isLoading = false)
    }
    
    fun withSelectedTile(position: Position?): GameUiState {
        return copy(selectedTile = position)
    }
    
    fun withGameStats(highestTile: Int, availableMovesCount: Int): GameUiState {
        return copy(
            highestTile = highestTile,
            availableMovesCount = availableMovesCount
        )
    }
    

    
    fun withCanUndo(canUndo: Boolean): GameUiState {
        return copy(canUndo = canUndo)
    }
    
    fun withTileAnimations(animations: List<TileAnimation>): GameUiState {
        return copy(tileAnimations = animations)
    }
    
    fun withMerging(merging: Boolean): GameUiState {
        return copy(isMerging = merging)
    }
    
    fun withSwapping(swapping: Boolean): GameUiState {
        return copy(isSwapping = swapping)
    }
    
    fun withSelectedTileTheme(theme: TileTheme): GameUiState {
        return copy(selectedTileTheme = theme)
    }
    
    fun withValidSwapPositions(positions: List<Position>): GameUiState {
        return copy(validSwapPositions = positions)
    }
    
    fun withSwapModeStatus(status: String?): GameUiState {
        return copy(swapModeStatus = status)
    }
    
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        
        other as GameUiState
        
        if (!board.contentDeepEquals(other.board)) return false
        if (score != other.score) return false
        if (moves != other.moves) return false
        if (isGameOver != other.isGameOver) return false
        if (hasWon != other.hasWon) return false
        if (isLoading != other.isLoading) return false
        if (errorMessage != other.errorMessage) return false
        if (lastMoveType != other.lastMoveType) return false
        if (selectedTile != other.selectedTile) return false
        if (canUndo != other.canUndo) return false
        if (highestTile != other.highestTile) return false
        if (availableMovesCount != other.availableMovesCount) return false
        if (isSwapping != other.isSwapping) return false
        if (isMerging != other.isMerging) return false
        if (tileAnimations != other.tileAnimations) return false
        if (selectedTileTheme != other.selectedTileTheme) return false
        if (validSwapPositions != other.validSwapPositions) return false
        if (swapModeStatus != other.swapModeStatus) return false
        
        return true
    }
    
    override fun hashCode(): Int {
        var result = board.contentDeepHashCode()
        result = 31 * result + score
        result = 31 * result + moves
        result = 31 * result + isGameOver.hashCode()
        result = 31 * result + hasWon.hashCode()
        result = 31 * result + isLoading.hashCode()
        result = 31 * result + (errorMessage?.hashCode() ?: 0)
        result = 31 * result + (lastMoveType?.hashCode() ?: 0)
        result = 31 * result + (selectedTile?.hashCode() ?: 0)
        result = 31 * result + canUndo.hashCode()
        result = 31 * result + highestTile
        result = 31 * result + availableMovesCount
        result = 31 * result + isSwapping.hashCode()
        result = 31 * result + isMerging.hashCode()
        result = 31 * result + tileAnimations.hashCode()
        result = 31 * result + selectedTileTheme.hashCode()
        result = 31 * result + validSwapPositions.hashCode()
        result = 31 * result + (swapModeStatus?.hashCode() ?: 0)
        return result
    }
}