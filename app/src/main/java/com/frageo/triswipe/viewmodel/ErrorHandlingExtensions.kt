package com.frageo.triswipe.viewmodel

import androidx.lifecycle.viewModelScope
import androidx.lifecycle.ViewModel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch

/**
 * Extension functions for consistent error handling across ViewModels
 */
inline fun <T> ViewModel.safeExecute(
    uiState: MutableStateFlow<T>,
    crossinline loadingUpdater: T.(<PERSON><PERSON><PERSON>) -> T,
    crossinline errorUpdater: T.(String?) -> T,
    crossinline action: suspend () -> Unit
) {
    viewModelScope.launch {
        try {
            uiState.value = uiState.value.loadingUpdater(true)
            action()
        } catch (e: Exception) {
            uiState.value = uiState.value.errorUpdater("Operation failed: ${e.message}")
        } finally {
            uiState.value = uiState.value.loadingUpdater(false)
        }
    }
}

inline fun <T> ViewModel.safeExecuteWithResult(
    uiState: MutableStateFlow<T>,
    crossinline loadingUpdater: T.(<PERSON><PERSON><PERSON>) -> T,
    crossinline errorUpdater: T.(String?) -> T,
    crossinline action: suspend () -> Unit
) {
    viewModelScope.launch {
        try {
            uiState.value = uiState.value.loadingUpdater(true)
            action()
            uiState.value = uiState.value.errorUpdater(null)
        } catch (e: Exception) {
            uiState.value = uiState.value.errorUpdater("Operation failed: ${e.message}")
        } finally {
            uiState.value = uiState.value.loadingUpdater(false)
        }
    }
}

inline fun CoroutineScope.safeExecuteAsync(
    crossinline action: suspend () -> Unit,
    crossinline onError: (Exception) -> Unit = {}
) {
    launch {
        try {
            action()
        } catch (e: Exception) {
            onError(e)
        }
    }
}