package com.frageo.triswipe.viewmodel

import kotlinx.coroutines.CoroutineScope
import com.frageo.triswipe.data.repository.GameStateRepository
import com.frageo.triswipe.data.repository.GameStatisticsManager
import com.frageo.triswipe.data.repository.UserPreferencesRepository
import com.frageo.triswipe.data.models.TileTheme
import com.frageo.triswipe.data.models.ThemeMode
import com.frageo.triswipe.billing.BillingManager
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel responsible for game persistence, statistics, and settings
 */
class GamePersistenceViewModel @Inject constructor(
    private val gameStateRepository: GameStateRepository,
    private val userPreferencesRepository: UserPreferencesRepository,
    private val statisticsManager: GameStatisticsManager,
    private val billingManager: BillingManager,
    private val coroutineScope: CoroutineScope
) {
    
    // Theme mode state flow
    private val _themeMode = MutableStateFlow(ThemeMode.AUTO)
    val themeMode: StateFlow<ThemeMode> = _themeMode.asStateFlow()
    
    init {
        // Load initial theme state
        coroutineScope.launch {
            _themeMode.value = userPreferencesRepository.getThemeMode()
        }
    }
    
    suspend fun saveGameState(gameState: com.frageo.triswipe.data.models.GameState) {
        gameStateRepository.saveGameState(gameState)
    }
    
    suspend fun loadGameState(): com.frageo.triswipe.data.models.GameState? {
        return gameStateRepository.loadGameState()
    }
    
    suspend fun clearGameState() {
        gameStateRepository.clearGameState()
    }
    
    fun getStatisticsFlow() = statisticsManager.getStatisticsFlow()
    

    
    // Real billing methods
    fun launchPurchaseFlow(activity: android.app.Activity) {
        billingManager.launchPurchaseFlow(activity)
    }
    
    fun getBillingState() = billingManager.billingState
    
    fun getPurchaseState() = billingManager.purchaseState
    
    fun getPremiumPrice() = billingManager.getPremiumPrice()
    

    
    // Theme methods
    fun setSelectedTheme(theme: TileTheme) {
        coroutineScope.launch {
            userPreferencesRepository.setSelectedTheme(theme)
        }
    }
    
    suspend fun getSelectedTheme(): TileTheme {
        return userPreferencesRepository.getSelectedTheme()
    }
    
    fun getAvailableThemes(): List<TileTheme> {
        return TileTheme.values().toList()
    }
    
    // Theme mode methods
    fun setThemeMode(themeMode: ThemeMode) {
        coroutineScope.launch {
            userPreferencesRepository.setThemeMode(themeMode)
            _themeMode.value = themeMode
        }
    }
    
    suspend fun getThemeModeOnce(): ThemeMode {
        return userPreferencesRepository.getThemeMode()
    }

}