package com.frageo.triswipe.premium

import com.frageo.triswipe.data.repository.MonetizationRepository
import com.frageo.triswipe.data.repository.GameStatisticsManager
import kotlinx.coroutines.runBlocking
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever

class PremiumStatusManagerTest {

    @Mock
    private lateinit var monetizationRepository: MonetizationRepository
    
    @Mock
    private lateinit var statisticsManager: GameStatisticsManager
    
    private lateinit var premiumStatusManager: PremiumStatusManager
    
    @Before
    fun setup() {
        MockitoAnnotations.openMocks(this)
        premiumStatusManager = PremiumStatusManager(monetizationRepository, statisticsManager)
    }
    
    @Test
    fun `initialize loads status from repository`() = runBlocking {
        // Given
        whenever(monetizationRepository.isPremiumUser()).thenReturn(true)
        whenever(monetizationRepository.getFreeGamesRemaining()).thenReturn(25)
        
        // When
        premiumStatusManager.initialize()
        
        // Then
        assertTrue(premiumStatusManager.isPremiumUserValue())
        assertEquals(25, premiumStatusManager.freeGamesRemainingValue())
    }
    
    @Test
    fun `setPremiumStatus updates repository and flows`() = runBlocking {
        // When
        premiumStatusManager.setPremiumStatus(true)
        
        // Then
        verify(monetizationRepository).setPremiumUser(true)
        verify(monetizationRepository).setFreeGamesRemaining(Int.MAX_VALUE)
        assertTrue(premiumStatusManager.isPremiumUserValue())
        assertEquals(Int.MAX_VALUE, premiumStatusManager.freeGamesRemainingValue())
    }
    
    @Test
    fun `decrementFreeGames only affects non-premium users`() = runBlocking {
        // Given - non-premium user
        whenever(monetizationRepository.isPremiumUser()).thenReturn(false)
        whenever(monetizationRepository.getFreeGamesRemaining()).thenReturn(29)
        premiumStatusManager.initialize()

        // When
        premiumStatusManager.decrementFreeGames()

        // Then
        verify(monetizationRepository).decrementFreeGames()
    }
    
    @Test
    fun `decrementFreeGames does not affect premium users`() = runBlocking {
        // Given - premium user
        whenever(monetizationRepository.isPremiumUser()).thenReturn(true)
        premiumStatusManager.initialize()
        
        // When
        premiumStatusManager.decrementFreeGames()
        
        // Then - should not call repository decrement
        verify(monetizationRepository, org.mockito.kotlin.never()).decrementFreeGames()
    }
    
    @Test
    fun `canStartNewGame returns true for premium users`() = runBlocking {
        // Given
        whenever(monetizationRepository.isPremiumUser()).thenReturn(true)
        premiumStatusManager.initialize()
        
        // Then
        assertTrue(premiumStatusManager.canStartNewGame())
    }
    
    @Test
    fun `canStartNewGame returns true for free users with games remaining`() = runBlocking {
        // Given
        whenever(monetizationRepository.isPremiumUser()).thenReturn(false)
        whenever(monetizationRepository.getFreeGamesRemaining()).thenReturn(5)
        premiumStatusManager.initialize()
        
        // Then
        assertTrue(premiumStatusManager.canStartNewGame())
    }
    
    @Test
    fun `canStartNewGame returns false for free users with no games remaining`() = runBlocking {
        // Given
        whenever(monetizationRepository.isPremiumUser()).thenReturn(false)
        whenever(monetizationRepository.getFreeGamesRemaining()).thenReturn(0)
        premiumStatusManager.initialize()
        
        // Then
        assertFalse(premiumStatusManager.canStartNewGame())
    }
    
    @Test
    fun `resetToFreeUser resets status correctly`() = runBlocking {
        // When
        premiumStatusManager.resetToFreeUser()
        
        // Then
        verify(monetizationRepository).setPremiumUser(false)
        verify(monetizationRepository).setFreeGamesRemaining(30)
        assertFalse(premiumStatusManager.isPremiumUserValue())
        assertEquals(30, premiumStatusManager.freeGamesRemainingValue())
    }
}
