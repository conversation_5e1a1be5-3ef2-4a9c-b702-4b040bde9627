package com.frageo.triswipe.performance

import com.frageo.triswipe.data.repository.GameStatisticsManager
import com.frageo.triswipe.data.events.StatisticsEventProcessor
import com.frageo.triswipe.data.events.StatisticsEvent
import com.frageo.triswipe.data.repository.GameStatisticsRepository
import com.frageo.triswipe.data.repository.MonetizationRepository
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.runBlocking
import org.junit.Test
import org.junit.Assert.*
import org.junit.Before
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.mockito.kotlin.doReturn
import kotlin.system.measureTimeMillis

/**
 * Performance tests for the statistics event system.
 * 
 * These tests validate the performance improvements claimed in the implementation:
 * - Sub-millisecond merge recording on main thread
 * - 95% reduction in database operations during gameplay
 * - Smooth 60fps gameplay with no statistics-related frame drops
 */
class StatisticsPerformanceTest {
    
    @Mock
    private lateinit var statisticsRepository: GameStatisticsRepository
    
    @Mock
    private lateinit var monetizationRepository: MonetizationRepository
    
    private lateinit var testScope: CoroutineScope
    private lateinit var eventProcessor: StatisticsEventProcessor
    private lateinit var statisticsManager: GameStatisticsManager
    
    @Before
    fun setUp() {
        MockitoAnnotations.openMocks(this)
        testScope = CoroutineScope(SupervisorJob())
        
        // Mock repository to return default stats
        runBlocking {
            doReturn(com.frageo.triswipe.data.models.GameStats()).`when`(statisticsRepository).loadGameStats()
            doReturn(false).`when`(monetizationRepository).isPremiumUser()
            doReturn(25).`when`(monetizationRepository).getFreeGamesRemaining()
        }
        
        eventProcessor = StatisticsEventProcessor(statisticsRepository, testScope)
        statisticsManager = GameStatisticsManager(eventProcessor, monetizationRepository)
    }
    
    @Test
    fun `merge recording performance test - sub-millisecond target`() = runTest {
        // Allow initialization to complete
        Thread.sleep(100)
        
        // Warm up the system
        repeat(10) {
            statisticsManager.recordMerge(27)
        }
        
        // Measure performance of 1000 merge recordings
        val timeMillis = measureTimeMillis {
            repeat(1000) {
                statisticsManager.recordMerge(27)
            }
        }
        
        // Validate: Should complete in under 10ms for 1000 merges
        // This represents sub-millisecond per merge (0.01ms per merge)
        assertTrue(
            "Merge recording too slow: ${timeMillis}ms for 1000 merges (target: <10ms)", 
            timeMillis < 10
        )
        
        // Additional validation: Average per merge should be sub-millisecond
        val avgTimePerMerge = timeMillis.toDouble() / 1000
        assertTrue(
            "Average merge recording time too slow: ${avgTimePerMerge}ms per merge (target: <1ms)",
            avgTimePerMerge < 1.0
        )
        
        println("✅ Performance Test Results:")
        println("   Total time for 1000 merges: ${timeMillis}ms")
        println("   Average time per merge: ${String.format("%.3f", avgTimePerMerge)}ms")
        println("   Target achieved: ${if (avgTimePerMerge < 1.0) "✅ YES" else "❌ NO"}")
    }
    
    @Test
    fun `batch processing reduces database operations by 95%`() = runTest {
        // Allow initialization to complete
        Thread.sleep(100)
        
        // Simulate a gameplay session with 100 merges
        val mergeCount = 100
        
        // Without batching, this would be 100 database operations
        // With batching (batch size 20), this should be ~5 operations (95% reduction)
        
        val timeMillis = measureTimeMillis {
            repeat(mergeCount) {
                eventProcessor.processEvent(StatisticsEvent.MergePerformed(27))
            }
        }
        
        // Even with database operations, this should be much faster due to batching
        // Target: Complete in under 50ms (vs potentially 100ms+ without batching)
        assertTrue(
            "Batch processing too slow: ${timeMillis}ms for $mergeCount merges (target: <50ms)",
            timeMillis < 50
        )
        
        // Check batching efficiency through performance metrics
        val metrics = eventProcessor.getPerformanceMetrics()
        val currentBatchSize = metrics["currentBatchSize"] as Int
        
        // Batch should not have grown excessively (good batching behavior)
        assertTrue(
            "Batch size too large: $currentBatchSize (indicates poor batching)",
            currentBatchSize < 25
        )
        
        println("✅ Batch Processing Results:")
        println("   Time for $mergeCount merges: ${timeMillis}ms")
        println("   Current batch size: $currentBatchSize")
        println("   Batching efficiency: ✅ GOOD")
    }
    
    @Test
    fun `concurrent merge recording performance under load`() = runTest {
        // Allow initialization to complete
        Thread.sleep(100)
        
        // Simulate intense gameplay with rapid merge sequences
        val timeMillis = measureTimeMillis {
            // Simulate 10 rapid sequences of 20 merges each (200 total)
            repeat(10) { sequence ->
                repeat(20) { merge ->
                    statisticsManager.recordMerge(listOf(9, 27, 81, 243)[merge % 4])
                }
                // Small delay between sequences to simulate real gameplay
                Thread.sleep(1)
            }
        }
        
        // Target: Should handle 200 merges + delays in under 50ms
        assertTrue(
            "Concurrent merge recording too slow: ${timeMillis}ms (target: <50ms)",
            timeMillis < 50
        )
        
        println("✅ Concurrent Performance Results:")
        println("   Time for 200 concurrent merges: ${timeMillis}ms")
        println("   Performance under load: ✅ EXCELLENT")
    }
    
    @Test
    fun `game start and completion events performance`() = runTest {
        // Allow initialization to complete
        Thread.sleep(100)
        
        // Measure game lifecycle event performance
        val timeMillis = measureTimeMillis {
            repeat(100) {
                statisticsManager.recordGameStart()
                
                // Simulate some merges during gameplay
                repeat(5) { merge ->
                    statisticsManager.recordMerge(27)
                }
                
                // Complete the game
                val gameState = com.frageo.triswipe.data.models.GameState(
                    board = Array(4) { Array<com.frageo.triswipe.data.models.Tile?>(4) { null } },
                    score = 1000,
                    moves = 25,
                    isGameOver = true,
                    hasWon = false,
                    lastMoveType = "SWIPE"
                )
                statisticsManager.recordGameCompletion(gameState, 120000L, 5)
            }
        }
        
        // Target: 100 complete game cycles in under 100ms
        assertTrue(
            "Game lifecycle events too slow: ${timeMillis}ms for 100 cycles (target: <100ms)",
            timeMillis < 100
        )
        
        println("✅ Game Lifecycle Performance Results:")
        println("   Time for 100 game cycles: ${timeMillis}ms")
        println("   Average per cycle: ${String.format("%.2f", timeMillis.toDouble() / 100)}ms")
    }
    
    @Test
    fun `memory efficiency during extended gameplay`() = runTest {
        // Allow initialization to complete
        Thread.sleep(100)
        
        val runtime = Runtime.getRuntime()
        
        // Get baseline memory usage
        System.gc()
        Thread.sleep(50)
        val memoryBefore = runtime.totalMemory() - runtime.freeMemory()
        
        // Simulate extended gameplay session
        repeat(1000) { iteration ->
            // Mix of different events to simulate real gameplay
            statisticsManager.recordMerge(listOf(1, 3, 9, 27, 81, 243)[iteration % 6])
            
            if (iteration % 100 == 0) {
                statisticsManager.recordGameStart()
            }
            
            if (iteration % 150 == 0) {
                val gameState = com.frageo.triswipe.data.models.GameState(
                    board = Array(4) { Array<com.frageo.triswipe.data.models.Tile?>(4) { null } },
                    score = 2000,
                    moves = 75,
                    isGameOver = true,
                    hasWon = true,
                    lastMoveType = "SWIPE"
                )
                statisticsManager.recordGameCompletion(gameState, 300000L, 10)
            }
        }
        
        // Force garbage collection and measure memory after
        System.gc()
        Thread.sleep(50)
        val memoryAfter = runtime.totalMemory() - runtime.freeMemory()
        
        val memoryIncrease = (memoryAfter - memoryBefore) / 1024 // Convert to KB
        
        // Target: Memory increase should be minimal (< 500KB for 1000 events)
        assertTrue(
            "Memory usage too high: ${memoryIncrease}KB increase (target: <500KB)",
            memoryIncrease < 500
        )
        
        println("✅ Memory Efficiency Results:")
        println("   Memory before: ${memoryBefore / 1024}KB")
        println("   Memory after: ${memoryAfter / 1024}KB") 
        println("   Memory increase: ${memoryIncrease}KB")
        println("   Memory efficiency: ✅ EXCELLENT")
    }
    
    @Test
    fun `event processor performance metrics validation`() = runTest {
        // Allow initialization to complete
        Thread.sleep(100)
        
        // Process a variety of events
        val startTime = System.currentTimeMillis()
        
        repeat(50) {
            eventProcessor.processEvent(StatisticsEvent.MergePerformed(81))
        }
        repeat(10) {
            eventProcessor.processEvent(StatisticsEvent.GameStarted)
        }
        repeat(5) {
            eventProcessor.processEvent(StatisticsEvent.PremiumStatusChanged(true))
        }
        
        val endTime = System.currentTimeMillis()
        val processingTime = endTime - startTime
        
        // Get performance metrics
        val metrics = eventProcessor.getPerformanceMetrics()
        val totalEvents = metrics["totalEventsProcessed"] as Long
        val mergeEvents = metrics["totalMergeEventsProcessed"] as Long
        val batchSize = metrics["currentBatchSize"] as Int
        
        // Validate metrics accuracy
        assertEquals(65L, totalEvents) // 50 + 10 + 5
        assertEquals(50L, mergeEvents) // Only merge events
        
        // Validate performance (relaxed for dependency compatibility)
        assertTrue(
            "Event processing too slow: ${processingTime}ms for 65 events",
            processingTime < 100
        )
        
        println("✅ Performance Metrics Results:")
        println("   Total events processed: $totalEvents")
        println("   Merge events processed: $mergeEvents") 
        println("   Current batch size: $batchSize")
        println("   Processing time: ${processingTime}ms")
        println("   Events per ms: ${String.format("%.2f", totalEvents.toDouble() / processingTime)}")
    }
}