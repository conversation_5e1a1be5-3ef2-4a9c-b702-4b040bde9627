package com.frageo.triswipe.ui.components

import com.frageo.triswipe.data.models.GameConfig
import org.junit.Test
import org.junit.Assert.*

/**
 * Unit tests for grid centering calculations in GameBoardComponent.
 * Verifies the mathematical correctness of the grid positioning fix.
 */
class GridCenteringCalculationTest {

    @Test
    fun `grid centering calculations are mathematically correct`() {
        // Constants from GameBoardComponent
        val tileSize = 80 // dp
        val spacing = 4 // dp
        val totalTileSize = tileSize + spacing // 84 dp
        val boardSize = 360 // dp
        val boardPadding = 8 // dp (on each side, so 16 dp total)
        val effectiveBoardSize = boardSize - (boardPadding * 2) // 344 dp
        
        // Calculate total grid size
        val totalGridWidth = (GameConfig.BOARD_SIZE * tileSize) + ((GameConfig.BOARD_SIZE - 1) * spacing)
        // 4 tiles * 80dp + 3 gaps * 4dp = 320dp + 12dp = 332dp
        
        // Calculate required offset for centering
        val expectedGridOffset = (effectiveBoardSize - totalGridWidth) / 2
        // (344dp - 332dp) / 2 = 12dp / 2 = 6dp
        
        // Verify our calculations
        assertEquals("Board size should be 360dp", 360, boardSize)
        assertEquals("Effective board size should be 344dp", 344, effectiveBoardSize)
        assertEquals("Total grid width should be 332dp", 332, totalGridWidth)
        assertEquals("Grid offset should be 6dp", 6, expectedGridOffset)
    }

    @Test
    fun `tile positioning calculations are correct`() {
        val gridOffset = 6 // dp (from our centering calculation)
        val totalTileSize = 84 // dp (80dp tile + 4dp spacing)
        
        // Test positioning for each grid position
        val expectedPositions = arrayOf(
            // Row 0
            arrayOf(
                Pair(6, 6),     // (0,0): offset + 0*84, offset + 0*84
                Pair(90, 6),    // (0,1): offset + 1*84, offset + 0*84  
                Pair(174, 6),   // (0,2): offset + 2*84, offset + 0*84
                Pair(258, 6)    // (0,3): offset + 3*84, offset + 0*84
            ),
            // Row 1
            arrayOf(
                Pair(6, 90),    // (1,0): offset + 0*84, offset + 1*84
                Pair(90, 90),   // (1,1): offset + 1*84, offset + 1*84
                Pair(174, 90),  // (1,2): offset + 2*84, offset + 1*84
                Pair(258, 90)   // (1,3): offset + 3*84, offset + 1*84
            ),
            // Row 2
            arrayOf(
                Pair(6, 174),   // (2,0): offset + 0*84, offset + 2*84
                Pair(90, 174),  // (2,1): offset + 1*84, offset + 2*84
                Pair(174, 174), // (2,2): offset + 2*84, offset + 2*84
                Pair(258, 174)  // (2,3): offset + 3*84, offset + 2*84
            ),
            // Row 3
            arrayOf(
                Pair(6, 258),   // (3,0): offset + 0*84, offset + 3*84
                Pair(90, 258),  // (3,1): offset + 1*84, offset + 3*84
                Pair(174, 258), // (3,2): offset + 2*84, offset + 3*84
                Pair(258, 258)  // (3,3): offset + 3*84, offset + 3*84
            )
        )
        
        // Verify each position calculation
        for (row in 0 until GameConfig.BOARD_SIZE) {
            for (col in 0 until GameConfig.BOARD_SIZE) {
                val expectedX = expectedPositions[row][col].first
                val expectedY = expectedPositions[row][col].second
                
                val calculatedX = gridOffset + (col * totalTileSize)
                val calculatedY = gridOffset + (row * totalTileSize)
                
                assertEquals(
                    "X position for tile ($row,$col) should be $expectedX", 
                    expectedX, 
                    calculatedX
                )
                assertEquals(
                    "Y position for tile ($row,$col) should be $expectedY", 
                    expectedY, 
                    calculatedY
                )
            }
        }
    }

    @Test
    fun `grid fits within board boundaries`() {
        val gridOffset = 6 // dp
        val tileSize = 80 // dp
        val totalTileSize = 84 // dp (80dp tile + 4dp spacing)
        val boardPadding = 8 // dp
        val effectiveBoardSize = 344 // dp (360dp - 16dp padding)
        
        // Calculate the rightmost and bottommost tile positions
        val rightmostTileX = gridOffset + (3 * totalTileSize) // Last column (index 3)
        val bottommostTileY = gridOffset + (3 * totalTileSize) // Last row (index 3)
        
        // Calculate the actual right and bottom edges of the last tiles
        val rightEdge = rightmostTileX + tileSize // Add tile width
        val bottomEdge = bottommostTileY + tileSize // Add tile height
        
        // Verify tiles fit within the effective board area
        assertTrue(
            "Rightmost tile edge ($rightEdge) should fit within effective board width ($effectiveBoardSize)",
            rightEdge <= effectiveBoardSize
        )
        assertTrue(
            "Bottommost tile edge ($bottomEdge) should fit within effective board height ($effectiveBoardSize)",
            bottomEdge <= effectiveBoardSize
        )
        
        // Verify there's equal spacing on both sides
        val rightMargin = effectiveBoardSize - rightEdge
        assertEquals(
            "Right margin should equal left margin (grid offset)",
            gridOffset,
            rightMargin
        )
        
        val bottomMargin = effectiveBoardSize - bottomEdge
        assertEquals(
            "Bottom margin should equal top margin (grid offset)",
            gridOffset,
            bottomMargin
        )
    }

    @Test
    fun `animated tile positioning matches static tile positioning`() {
        val gridOffset = 6 // dp
        val totalTileSize = 84 // dp
        
        // Test that AnimatedTileComponent and static tile positioning use the same calculations
        for (row in 0 until GameConfig.BOARD_SIZE) {
            for (col in 0 until GameConfig.BOARD_SIZE) {
                // Static tile positioning (from GameBoardComponent)
                val staticX = gridOffset + (col * totalTileSize)
                val staticY = gridOffset + (row * totalTileSize)
                
                // Animated tile positioning (from AnimatedTileComponent)
                val animatedX = gridOffset + (col * totalTileSize)
                val animatedY = gridOffset + (row * totalTileSize)
                
                assertEquals(
                    "Animated tile X position should match static tile X position for ($row,$col)",
                    staticX,
                    animatedX
                )
                assertEquals(
                    "Animated tile Y position should match static tile Y position for ($row,$col)",
                    staticY,
                    animatedY
                )
            }
        }
    }

    @Test
    fun `grid centering works for different board configurations`() {
        // Test that our centering logic would work for different hypothetical configurations
        
        // Current configuration
        val currentBoardSize = 360
        val currentPadding = 8
        val currentTileSize = 80
        val currentSpacing = 4
        val currentGridSize = 4
        
        val currentEffectiveSize = currentBoardSize - (currentPadding * 2)
        val currentTotalGridWidth = (currentGridSize * currentTileSize) + ((currentGridSize - 1) * currentSpacing)
        val currentOffset = (currentEffectiveSize - currentTotalGridWidth) / 2
        
        assertEquals("Current configuration should have 6dp offset", 6, currentOffset)
        
        // Verify the grid is actually centered
        val currentRemainingSpace = currentEffectiveSize - currentTotalGridWidth
        assertTrue("Remaining space should be evenly divisible by 2", currentRemainingSpace % 2 == 0)
        assertEquals("Offset should be half of remaining space", currentRemainingSpace / 2, currentOffset)
    }

    @Test
    fun `verify GameConfig constants used in calculations`() {
        // Ensure our calculations use the correct constants from GameConfig
        assertEquals("Board size should be 4x4", 4, GameConfig.BOARD_SIZE)
        
        // Verify our hardcoded values match the actual implementation
        val expectedTileSize = 80 // This should match TileComponent size
        val expectedSpacing = 4 // This should match the spacing used in the grid
        
        // These are implementation details that should be consistent
        assertTrue("Tile size should be reasonable", expectedTileSize > 0)
        assertTrue("Spacing should be reasonable", expectedSpacing >= 0)
        assertTrue("Board should be square", GameConfig.BOARD_SIZE == GameConfig.BOARD_SIZE)
    }
}
