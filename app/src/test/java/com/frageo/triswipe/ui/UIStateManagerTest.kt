package com.frageo.triswipe.ui

import com.frageo.triswipe.data.models.GameState
import com.frageo.triswipe.data.models.GameStatistics
import com.frageo.triswipe.data.models.Position
import com.frageo.triswipe.data.models.Tile
import com.frageo.triswipe.data.models.TileAnimation
import com.frageo.triswipe.data.models.TileAnimationType
import com.frageo.triswipe.data.models.TileTheme
import com.frageo.triswipe.game.GameStateResult
import com.frageo.triswipe.viewmodel.GameUiState
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test

class UIStateManagerTest {
    
    private lateinit var uiStateManager: UIStateManager
    
    @Before
    fun setup() {
        val mockGameEngine = org.mockito.kotlin.mock<com.frageo.triswipe.data.models.GameEngineInterface>()
        uiStateManager = UIStateManager(mockGameEngine)
    }
    
    @Test
    fun `initial state should be loading`() = runTest {
        val initialState = uiStateManager.uiState.first()
        
        assertTrue(initialState.isLoading)
        assertNull(initialState.selectedTile)
        assertNull(initialState.errorMessage)
        assertTrue(initialState.tileAnimations.isEmpty())
        assertEquals(TileTheme.CLASSIC, initialState.selectedTileTheme)
    }
    
    @Test
    fun `tile click selection should work correctly`() = runTest {
        val position = Position(1, 1)
        
        // First click should select
        val action1 = uiStateManager.handleTileClick(position)
        assertTrue(action1 is TileClickAction.Select)
        assertEquals(position, (action1 as TileClickAction.Select).position)
        
        val state1 = uiStateManager.uiState.first()
        assertEquals(position, state1.selectedTile)
        assertEquals(position, uiStateManager.getSelectedTilePosition())
        
        // Second click on same position should deselect
        val action2 = uiStateManager.handleTileClick(position)
        assertTrue(action2 is TileClickAction.Deselect)
        
        val state2 = uiStateManager.uiState.first()
        assertNull(state2.selectedTile)
        assertNull(uiStateManager.getSelectedTilePosition())
    }
    
    @Test
    fun `tile click swap should work correctly`() = runTest {
        val position1 = Position(1, 1)
        val position2 = Position(1, 2)
        
        // First click selects
        val action1 = uiStateManager.handleTileClick(position1)
        assertTrue(action1 is TileClickAction.Select)
        
        // Second click on different position should trigger swap
        val action2 = uiStateManager.handleTileClick(position2)
        assertTrue(action2 is TileClickAction.Swap)
        val swapAction = action2 as TileClickAction.Swap
        assertEquals(position1, swapAction.position1)
        assertEquals(position2, swapAction.position2)
        
        // Selection should be cleared after swap
        val state = uiStateManager.uiState.first()
        assertNull(state.selectedTile)
    }
    
    @Test
    fun `manual tile selection should work`() = runTest {
        val position = Position(2, 3)
        
        uiStateManager.selectTile(position)
        
        val state = uiStateManager.uiState.first()
        assertEquals(position, state.selectedTile)
        assertEquals(position, uiStateManager.getSelectedTilePosition())
    }
    
    @Test
    fun `clear selection should work`() = runTest {
        val position = Position(1, 1)
        
        uiStateManager.selectTile(position)
        assertEquals(position, uiStateManager.uiState.first().selectedTile)
        
        uiStateManager.clearSelection()
        
        val state = uiStateManager.uiState.first()
        assertNull(state.selectedTile)
        assertNull(uiStateManager.getSelectedTilePosition())
    }
    
    @Test
    fun `loading state should be managed correctly`() = runTest {
        // Initially loading
        assertTrue(uiStateManager.uiState.first().isLoading)
        
        // Set not loading
        uiStateManager.setLoading(false)
        assertFalse(uiStateManager.uiState.first().isLoading)
        
        // Set loading again and verify animations are cleared
        val tile = Tile(3, Position(0, 0))
        val animations = listOf(
            TileAnimation(tile, Position(0, 0), Position(1, 0), TileAnimationType.MOVE, 250, 0)
        )
        uiStateManager.setAnimations(animations)
        assertEquals(1, uiStateManager.uiState.first().tileAnimations.size)
        
        uiStateManager.setLoading(true)
        val state = uiStateManager.uiState.first()
        assertTrue(state.isLoading)
        assertTrue(state.tileAnimations.isEmpty())
    }
    
    @Test
    fun `error state should be managed correctly`() = runTest {
        val errorMessage = "Test error"
        
        uiStateManager.setError(errorMessage)
        
        val state = uiStateManager.uiState.first()
        assertEquals(errorMessage, state.errorMessage)
        assertFalse(state.isLoading) // Should clear loading when error is set
        
        uiStateManager.clearError()
        assertNull(uiStateManager.uiState.first().errorMessage)
    }
    
    @Test
    fun `theme updates should work correctly`() = runTest {
        val theme = TileTheme.OCEAN
        
        uiStateManager.updateTheme(theme)
        
        val state = uiStateManager.uiState.first()
        assertEquals(theme, state.selectedTileTheme)
    }
    
    @Test
    fun `animations should be managed correctly`() = runTest {
        val tile1 = Tile(3, Position(0, 0))
        val tile2 = Tile(9, Position(0, 1))
        val animations = listOf(
            TileAnimation(tile1, Position(0, 0), Position(1, 0), TileAnimationType.MOVE, 250, 0),
            TileAnimation(tile2, Position(0, 1), Position(1, 1), TileAnimationType.MOVE, 250, 15)
        )
        
        uiStateManager.setAnimations(animations)
        assertEquals(animations, uiStateManager.uiState.first().tileAnimations)
        
        uiStateManager.clearAnimations()
        assertTrue(uiStateManager.uiState.first().tileAnimations.isEmpty())
    }
    
    @Test
    fun `swapping and merging states should work`() = runTest {
        uiStateManager.setSwapping(true)
        assertTrue(uiStateManager.uiState.first().isSwapping)
        
        uiStateManager.setSwapping(false)
        assertFalse(uiStateManager.uiState.first().isSwapping)
        
        uiStateManager.setMerging(true)
        assertTrue(uiStateManager.uiState.first().isMerging)
        
        uiStateManager.setMerging(false)
        assertFalse(uiStateManager.uiState.first().isMerging)
    }
    
    @Test
    fun `undo status should be updated correctly`() = runTest {
        uiStateManager.updateUndoStatus(true)
        assertTrue(uiStateManager.uiState.first().canUndo)
        
        uiStateManager.updateUndoStatus(false)
        assertFalse(uiStateManager.uiState.first().canUndo)
    }
    
    @Test
    fun `updateFromGameStateResult should work correctly`() = runTest {
        // Create test data
        val board = Array(4) { Array<Tile?>(4) { null } }
        board[0][0] = Tile(3, Position(0, 0))
        board[1][1] = Tile(9, Position(1, 1))
        
        val gameState = GameState(
            board = board,
            score = 100,
            moves = 5,
            isGameOver = false,
            hasWon = false,
            lastMoveType = "swipe"
        )
        
        val gameStats = GameStatistics(
            score = 100,
            moves = 5,
            highestTile = 81,
            emptySpaces = 10,
            availableMoves = 15,
            canWin = true
        )
        
        val animationTile = Tile(3, Position(0, 0))
        val animations = listOf(
            TileAnimation(animationTile, Position(0, 0), Position(1, 0), TileAnimationType.MOVE, 250, 0)
        )
        
        val result = GameStateResult.success(
            gameState = gameState,
            animations = animations,
            gameStats = gameStats
        )
        
        // Select a tile first to test that selection is preserved
        val selectedPosition = Position(2, 2)
        uiStateManager.selectTile(selectedPosition)
        
        // Update from game state result
        uiStateManager.updateFromGameStateResult(result)
        
        val state = uiStateManager.uiState.first()
        
        // Verify game state was applied
        assertEquals(100, state.score)
        assertEquals(5, state.moves)
        assertEquals("swipe", state.lastMoveType)
        assertFalse(state.isGameOver)
        assertFalse(state.hasWon)
        
        // Verify game stats were applied
        assertEquals(81, state.highestTile)
        assertEquals(15, state.availableMovesCount)
        
        // Verify animations were applied
        assertEquals(animations, state.tileAnimations)
        
        // Verify selection was preserved
        assertEquals(selectedPosition, state.selectedTile)
        
        // Verify loading was cleared
        assertFalse(state.isLoading)
        
        // Verify no error
        assertNull(state.errorMessage)
    }
    
    @Test
    fun `updateFromGameStateResult with error should handle correctly`() = runTest {
        val board = Array(4) { Array<Tile?>(4) { null } }
        val gameState = GameState(board = board)
        val errorMessage = "Game operation failed"
        
        val result = GameStateResult.failure(
            gameState = gameState,
            errorMessage = errorMessage
        )
        
        uiStateManager.updateFromGameStateResult(result)
        
        val state = uiStateManager.uiState.first()
        assertEquals(errorMessage, state.errorMessage)
        assertFalse(state.isLoading)
    }
    
    @Test
    fun `getCurrentUiState should return current state`() = runTest {
        val position = Position(1, 1)
        uiStateManager.selectTile(position)
        
        val currentState = uiStateManager.getCurrentUiState()
        val flowState = uiStateManager.uiState.first()
        
        assertEquals(currentState, flowState)
        assertEquals(position, currentState.selectedTile)
    }
}