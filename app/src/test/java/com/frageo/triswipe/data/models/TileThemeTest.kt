package com.frageo.triswipe.data.models

import org.junit.Test
import org.junit.Assert.*

class TileThemeTest {
    
    @Test
    fun `classic theme is not premium`() {
        assertFalse(TileTheme.CLASSIC.isPremium)
        assertEquals("Classic", TileTheme.CLASSIC.displayName)
        assertEquals("Original warm colors", TileTheme.CLASSIC.description)
    }
    
    @Test
    fun `midnight theme is not premium`() {
        assertFalse(TileTheme.MIDNIGHT.isPremium)
        assertEquals("Midnight", TileTheme.MIDNIGHT.displayName)
        assertEquals("Sleek dark blue-purple theme", TileTheme.MIDNIGHT.description)
    }
    
    @Test
    fun `all themes have display names and descriptions`() {
        TileTheme.values().forEach { theme ->
            assertTrue(theme.displayName.isNotBlank())
            assertTrue(theme.description.isNotBlank())
        }
    }
    
    @Test
    fun `classic theme returns correct colors for standard tiles`() {
        assertEquals("#F5F4F0", TileTheme.CLASSIC.getTileColor(1))
        assertEquals("#EFE4D3", TileTheme.CLASSIC.getTileColor(3))
        assertEquals("#FFB366", TileTheme.CLASSIC.getTileColor(9))
        assertEquals("#FF9A56", TileTheme.CLASSIC.getTileColor(27))
        assertEquals("#FF7B5A", TileTheme.CLASSIC.getTileColor(81))
        assertEquals("#FF5E5B", TileTheme.CLASSIC.getTileColor(243))
        assertEquals("#FFD93D", TileTheme.CLASSIC.getTileColor(729))
        assertEquals("#6BCF7F", TileTheme.CLASSIC.getTileColor(2187))
    }
    
    @Test
    fun `midnight theme returns correct colors for standard tiles`() {
        assertEquals("#1E1E2E", TileTheme.MIDNIGHT.getTileColor(1))
        assertEquals("#2A2A4A", TileTheme.MIDNIGHT.getTileColor(3))
        assertEquals("#3B3B6B", TileTheme.MIDNIGHT.getTileColor(9))
        assertEquals("#4A4A7C", TileTheme.MIDNIGHT.getTileColor(27))
        assertEquals("#5D5D8D", TileTheme.MIDNIGHT.getTileColor(81))
        assertEquals("#7070A0", TileTheme.MIDNIGHT.getTileColor(243))
        assertEquals("#8585B3", TileTheme.MIDNIGHT.getTileColor(729))
        assertEquals("#9A9AC6", TileTheme.MIDNIGHT.getTileColor(2187))
    }
    
    @Test
    fun `getAvailableThemes returns classic and midnight for free users`() {
        val freeThemes = TileTheme.getAvailableThemes(false)
        
        assertEquals(2, freeThemes.size)
        assertTrue(freeThemes.contains(TileTheme.CLASSIC))
        assertTrue(freeThemes.contains(TileTheme.MIDNIGHT))
    }
    
    @Test
    fun `getAvailableThemes returns all themes for premium users`() {
        val premiumThemes = TileTheme.getAvailableThemes(true)
        
        assertEquals(7, premiumThemes.size)
        assertTrue(premiumThemes.contains(TileTheme.CLASSIC))
        assertTrue(premiumThemes.contains(TileTheme.MIDNIGHT))
        assertTrue(premiumThemes.contains(TileTheme.OCEAN))
        assertTrue(premiumThemes.contains(TileTheme.FOREST))
        assertTrue(premiumThemes.contains(TileTheme.SUNSET))
        assertTrue(premiumThemes.contains(TileTheme.NEON))
        assertTrue(premiumThemes.contains(TileTheme.MONOCHROME))
    }
    
    @Test
    fun `theme enum has correct count`() {
        assertEquals(7, TileTheme.values().size)
        
        val themeNames = TileTheme.values().map { it.name }
        assertTrue(themeNames.contains("CLASSIC"))
        assertTrue(themeNames.contains("MIDNIGHT"))
        assertTrue(themeNames.contains("OCEAN"))
        assertTrue(themeNames.contains("FOREST"))
        assertTrue(themeNames.contains("SUNSET"))
        assertTrue(themeNames.contains("NEON"))
        assertTrue(themeNames.contains("MONOCHROME"))
    }
    
    @Test
    fun `theme colors are valid hex colors`() {
        val testValues = listOf(1, 3, 9, 27, 81, 243, 729, 2187)
        
        TileTheme.values().forEach { theme ->
            testValues.forEach { value ->
                val color = theme.getTileColor(value)
                val textColor = theme.getTileTextColor(value)
                
                assertTrue("Color $color is not valid hex", color.matches(Regex("^#[0-9A-Fa-f]{6}$")))
                assertTrue("Text color $textColor is not valid hex", textColor.matches(Regex("^#[0-9A-Fa-f]{6}$")))
            }
        }
    }
}