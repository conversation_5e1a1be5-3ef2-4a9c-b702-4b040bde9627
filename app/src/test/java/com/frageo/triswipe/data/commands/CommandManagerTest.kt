package com.frageo.triswipe.data.commands

import com.frageo.triswipe.data.models.Direction
import com.frageo.triswipe.data.models.GameEngine
import com.frageo.triswipe.data.models.GameState
import com.frageo.triswipe.data.models.Position
import com.frageo.triswipe.data.models.Tile
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.mockito.kotlin.whenever

class CommandManagerTest {

    @Mock
    private lateinit var mockGameEngine: GameEngine
    
    @Mock
    private lateinit var mockCommand1: GameCommand
    
    @Mock
    private lateinit var mockCommand2: GameCommand
    
    private lateinit var commandManager: CommandManager
    
    @Before
    fun setUp() {
        MockitoAnnotations.openMocks(this)
        commandManager = CommandManager()
        
        // Setup mock commands
        whenever(mockCommand1.id).thenReturn("command1")
        whenever(mockCommand1.description).thenReturn("Test Command 1")
        whenever(mockCommand1.canUndo()).thenReturn(true)
        
        whenever(mockCommand2.id).thenReturn("command2")
        whenever(mockCommand2.description).thenReturn("Test Command 2")
        whenever(mockCommand2.canUndo()).thenReturn(true)
    }
    
    @Test
    fun `initial state has empty history and cannot undo`() = runTest {
        assertFalse(commandManager.canUndoCommand())
        assertEquals(0, commandManager.getUndoableCommandCount())
        assertFalse(commandManager.canUndo.first())
        assertEquals(0, commandManager.undoCount.first())
    }
    
    @Test
    fun `executeCommand adds successful command to history`() {
        val gameState = createGameState()
        val successResult = CommandResult.success("Command executed", gameState)
        
        whenever(mockCommand1.execute(mockGameEngine)).thenReturn(successResult)
        
        val result = commandManager.executeCommand(mockCommand1, mockGameEngine)
        
        assertTrue(result.success)
        assertTrue(commandManager.canUndoCommand())
        assertEquals(1, commandManager.getUndoableCommandCount())
    }
    
    @Test
    fun `executeCommand does not add failed command to history`() {
        val gameState = createGameState()
        val failureResult = CommandResult.failure("Command failed", gameState)
        
        whenever(mockCommand1.execute(mockGameEngine)).thenReturn(failureResult)
        
        val result = commandManager.executeCommand(mockCommand1, mockGameEngine)
        
        assertFalse(result.success)
        assertFalse(commandManager.canUndoCommand())
        assertEquals(0, commandManager.getUndoableCommandCount())
    }
    
    @Test
    fun `undoLastCommand removes command from history`() {
        val gameState = createGameState()
        val successResult = CommandResult.success("Command executed", gameState)
        val undoResult = CommandResult.success("Command undone", gameState)
        
        whenever(mockCommand1.execute(mockGameEngine)).thenReturn(successResult)
        whenever(mockCommand1.undo(mockGameEngine)).thenReturn(undoResult)
        
        // Execute command
        commandManager.executeCommand(mockCommand1, mockGameEngine)
        assertTrue(commandManager.canUndoCommand())
        
        // Undo command
        val result = commandManager.undoLastCommand(mockGameEngine)
        
        assertTrue(result.success)
        assertFalse(commandManager.canUndoCommand())
    }
    
    @Test
    fun `undoLastCommand fails when no commands in history`() {
        val gameState = createGameState()
        whenever(mockGameEngine.getCurrentGameState()).thenReturn(gameState)
        
        val result = commandManager.undoLastCommand(mockGameEngine)
        
        assertFalse(result.success)
        assertEquals("No commands to undo", result.message)
    }
    
    
    
    @Test
    fun `clearHistory removes all commands`() {
        val gameState = createGameState()
        val successResult = CommandResult.success("Command executed", gameState)
        
        whenever(mockCommand1.execute(mockGameEngine)).thenReturn(successResult)
        whenever(mockCommand2.execute(mockGameEngine)).thenReturn(successResult)
        
        // Execute commands
        commandManager.executeCommand(mockCommand1, mockGameEngine)
        commandManager.executeCommand(mockCommand2, mockGameEngine)
        assertTrue(commandManager.canUndoCommand())
        
        // Clear history
        commandManager.clearHistory()
        
        assertFalse(commandManager.canUndoCommand())
        assertEquals(0, commandManager.getUndoableCommandCount())
    }
    
    
    
    @Test
    fun `history respects maximum size limit`() {
        val gameState = createGameState()
        val successResult = CommandResult.success("Command executed", gameState)
        
        whenever(mockCommand1.execute(mockGameEngine)).thenReturn(successResult)
        
        // Execute more than 50 commands (max history size)
        repeat(55) {
            commandManager.executeCommand(mockCommand1, mockGameEngine)
        }
        
        // Should still be able to undo (history managed internally)
        assertTrue(commandManager.canUndoCommand())
        assertEquals(50, commandManager.getUndoableCommandCount())
    }
    
    
    private fun createGameState(
        score: Int = 0,
        moves: Int = 0,
        isGameOver: Boolean = false,
        hasWon: Boolean = false
    ): GameState {
        val emptyBoard = Array(4) { Array<Tile?>(4) { null } }
        return GameState(
            board = emptyBoard,
            score = score,
            moves = moves,
            isGameOver = isGameOver,
            hasWon = hasWon
        )
    }
}