package com.frageo.triswipe.data.repository

import com.frageo.triswipe.data.events.StatisticsEvent
import com.frageo.triswipe.data.events.StatisticsEventProcessor
import com.frageo.triswipe.data.models.GameState
import com.frageo.triswipe.data.models.GameStats
import com.frageo.triswipe.data.models.Tile
import com.frageo.triswipe.data.models.Position
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import org.junit.Test
import org.junit.Assert.*
import org.junit.Before
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import org.mockito.kotlin.any
import org.mockito.kotlin.eq

class GameStatisticsManagerTest {
    
    @Mock
    private lateinit var eventProcessor: StatisticsEventProcessor
    
    @Mock
    private lateinit var monetizationRepository: MonetizationRepository
    
    private lateinit var statisticsManager: GameStatisticsManager
    
    private val testStatsFlow = MutableStateFlow(GameStats())
    
    @Before
    fun setUp() {
        MockitoAnnotations.openMocks(this)
        whenever(eventProcessor.currentStats).thenReturn(testStatsFlow)
        statisticsManager = GameStatisticsManager(eventProcessor, monetizationRepository)
    }
    
    @Test
    fun `recordGameStart emits GameStarted event`() = runTest {
        statisticsManager.recordGameStart()
        
        verify(eventProcessor).processEvent(StatisticsEvent.GameStarted)
    }
    
    @Test
    fun `recordGameCompletion emits GameCompleted event with correct data`() = runTest {
        val gameState = GameState(
            board = createEmptyBoard(),
            score = 200,
            moves = 10,
            isGameOver = true,
            hasWon = false,
            lastMoveType = "SWIPE"
        )
        
        statisticsManager.recordGameCompletion(gameState, 60000L, 5)
        
        verify(eventProcessor).processEvent(
            StatisticsEvent.GameCompleted(gameState, 60000L, 5)
        )
    }
    
    @Test
    fun `recordGameCompletion emits event regardless of score`() = runTest {
        val gameState = GameState(
            board = createEmptyBoard(),
            score = 150,
            moves = 8,
            isGameOver = true,
            hasWon = false,
            lastMoveType = "SWIPE"
        )
        
        statisticsManager.recordGameCompletion(gameState, 45000L, 3)
        
        verify(eventProcessor).processEvent(
            StatisticsEvent.GameCompleted(gameState, 45000L, 3)
        )
    }
    
    @Test
    fun `recordGameCompletion emits event for winning game`() = runTest {
        val gameState = GameState(
            board = createEmptyBoard(),
            score = 500,
            moves = 20,
            isGameOver = true,
            hasWon = true,
            lastMoveType = "SWIPE"
        )
        
        statisticsManager.recordGameCompletion(gameState, 120000L, 10)
        
        verify(eventProcessor).processEvent(
            StatisticsEvent.GameCompleted(gameState, 120000L, 10)
        )
    }
    
    @Test
    fun `recordGameCompletion emits event for losing game`() = runTest {
        val gameState = GameState(
            board = createEmptyBoard(),
            score = 300,
            moves = 15,
            isGameOver = true,
            hasWon = false,
            lastMoveType = "SWIPE"
        )
        
        statisticsManager.recordGameCompletion(gameState, 90000L, 8)
        
        verify(eventProcessor).processEvent(
            StatisticsEvent.GameCompleted(gameState, 90000L, 8)
        )
    }
    
    @Test
    fun `recordGameCompletion emits comprehensive event data`() = runTest {
        val gameState = GameState(
            board = createBoardWithTiles(arrayOf(243, 81, 27)),
            score = 400,
            moves = 25,
            isGameOver = true,
            hasWon = true,
            lastMoveType = "SWIPE"
        )
        
        statisticsManager.recordGameCompletion(gameState, 180000L, 12)
        
        verify(eventProcessor).processEvent(
            StatisticsEvent.GameCompleted(gameState, 180000L, 12)
        )
    }
    
    @Test
    fun `recordMerge emits MergePerformed event with tile value`() = runTest {
        statisticsManager.recordMerge(243)
        
        verify(eventProcessor).processEvent(
            StatisticsEvent.MergePerformed(243)
        )
    }
    
    @Test
    fun `getStatisticsFlow returns event processor flow`() = runTest {
        val stats = GameStats(highScore = 500)
        testStatsFlow.value = stats
        
        val flow = statisticsManager.getStatisticsFlow()
        val result = flow.first()
        
        assertEquals(500, result.highScore)
    }
    
    @Test
    fun `getCurrentStatistics returns current stats from event processor`() = runTest {
        val stats = GameStats(gamesPlayed = 15)
        testStatsFlow.value = stats
        
        val result = statisticsManager.getCurrentStatistics()
        
        assertEquals(15, result.gamesPlayed)
    }
    
    @Test
    fun `canStartNewGame returns true when stats allow new game`() = runTest {
        val stats = GameStats() // Default stats should allow new game
        testStatsFlow.value = stats
        
        val result = statisticsManager.canStartNewGame()
        
        assertTrue(result)
    }
    
    @Test
    fun `consumeFreeGame decrements free games when user is not premium`() = runTest {
        whenever(monetizationRepository.isPremiumUser()).thenReturn(false)
        
        statisticsManager.consumeFreeGame()
        
        verify(monetizationRepository).decrementFreeGames()
    }
    
    @Test
    fun `consumeFreeGame does nothing when user is premium`() = runTest {
        whenever(monetizationRepository.isPremiumUser()).thenReturn(true)
        
        statisticsManager.consumeFreeGame()
        
        verify(monetizationRepository, org.mockito.kotlin.never()).decrementFreeGames()
    }
    
    @Test
    fun `getFreeGamesRemaining returns count from repository`() = runTest {
        whenever(monetizationRepository.getFreeGamesRemaining()).thenReturn(22)
        
        val result = statisticsManager.getFreeGamesRemaining()
        
        assertEquals(22, result)
    }
    
    @Test
    fun `recordGameCompletion includes board with highest tile values`() = runTest {
        val board = createBoardWithTiles(arrayOf(1, 3, 9, 27, 81, 243))
        val gameState = GameState(
            board = board,
            score = 100,
            moves = 5,
            isGameOver = false,
            hasWon = false,
            lastMoveType = "SWIPE"
        )
        
        statisticsManager.recordGameCompletion(gameState, 60000L, 5)
        
        // Verify the event includes the game state with the board
        verify(eventProcessor).processEvent(
            StatisticsEvent.GameCompleted(gameState, 60000L, 5)
        )
    }
    
    @Test
    fun `recordGameCompletion works with empty board`() = runTest {
        val board = createEmptyBoard()
        val gameState = GameState(
            board = board,
            score = 0,
            moves = 0,
            isGameOver = false,
            hasWon = false,
            lastMoveType = "SWIPE"
        )
        
        statisticsManager.recordGameCompletion(gameState, 30000L, 0)
        
        verify(eventProcessor).processEvent(
            StatisticsEvent.GameCompleted(gameState, 30000L, 0)
        )
    }
    
    // Helper methods
    private fun createBoardWithTiles(values: Array<Int>): Array<Array<Tile?>> {
        val board = Array(4) { Array<Tile?>(4) { null } }
        
        var index = 0
        for (row in 0 until 4) {
            for (col in 0 until 4) {
                if (index < values.size) {
                    board[row][col] = Tile(values[index], Position(row, col))
                    index++
                }
            }
        }
        
        return board
    }
    
    private fun createEmptyBoard(): Array<Array<Tile?>> {
        return Array(4) { Array<Tile?>(4) { null } }
    }
    
    @Test
    fun `recordGameCompletion passes session merge count correctly`() = runTest {
        val board = createBoardWithTiles(arrayOf(1, 3, 9, 27))
        val gameState = GameState(
            board = board,
            score = 100,
            moves = 5,
            isGameOver = false,
            hasWon = false,
            lastMoveType = "SWIPE"
        )
        
        // Game completed with 3 merges performed during the game
        statisticsManager.recordGameCompletion(gameState, 60000L, 3)
        
        verify(eventProcessor).processEvent(
            StatisticsEvent.GameCompleted(gameState, 60000L, 3)
        )
    }
    
    @Test
    fun `recordMerge passes correct tile value to event`() = runTest {
        statisticsManager.recordMerge(27) // New merge creates 27-tile
        
        verify(eventProcessor).processEvent(
            StatisticsEvent.MergePerformed(27)
        )
    }
}