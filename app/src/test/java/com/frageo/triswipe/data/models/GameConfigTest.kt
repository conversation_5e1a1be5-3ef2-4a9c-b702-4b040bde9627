package com.frageo.triswipe.data.models

import org.junit.Test
import org.junit.Assert.*

class GameConfigTest {

    @Test
    fun `getNextTileValue returns correct next value`() {
        assertEquals(3, GameConfig.getNextTileValue(1))
        assertEquals(9, GameConfig.getNextTileValue(3))
        assertEquals(27, GameConfig.getNextTileValue(9))
        assertEquals(81, GameConfig.getNextTileValue(27))
        assertEquals(243, GameConfig.getNextTileValue(81))
        assertEquals(729, GameConfig.getNextTileValue(243))
        assertEquals(2187, GameConfig.getNextTileValue(729))
        assertEquals(6561, GameConfig.getNextTileValue(2187))
        assertEquals(19683, GameConfig.getNextTileValue(6561))
    }

    @Test
    fun `getNextTileValue returns same value for highest tile`() {
        assertEquals(19683, GameConfig.getNextTileValue(19683))
    }

    @Test
    fun `getNextTileValue returns same value for invalid tile`() {
        assertEquals(999, GameConfig.getNextTileValue(999))
    }

    @Test
    fun `getTileColor returns correct colors`() {
        assertEquals("#F5F4F0", GameConfig.getTileColor(1))
        assertEquals("#EFE4D3", GameConfig.getTileColor(3))
        assertEquals("#FFB366", GameConfig.getTileColor(9))
        assertEquals("#FF9A56", GameConfig.getTileColor(27))
        assertEquals("#FF7B5A", GameConfig.getTileColor(81))
        assertEquals("#FF5E5B", GameConfig.getTileColor(243))
        assertEquals("#FFD93D", GameConfig.getTileColor(729))
        assertEquals("#6BCF7F", GameConfig.getTileColor(2187))
        assertEquals("#4DABF7", GameConfig.getTileColor(6561))
        assertEquals("#9775FA", GameConfig.getTileColor(19683))
    }

    @Test
    fun `getTileColor returns default color for unknown value`() {
        assertEquals("#495057", GameConfig.getTileColor(999))
    }

    @Test
    fun `getTileTextColor returns correct colors`() {
        assertEquals("#495057", GameConfig.getTileTextColor(1))
        assertEquals("#495057", GameConfig.getTileTextColor(3))
        assertEquals("#FFFFFF", GameConfig.getTileTextColor(9))
        assertEquals("#FFFFFF", GameConfig.getTileTextColor(27))
        assertEquals("#FFFFFF", GameConfig.getTileTextColor(999))
    }

    @Test
    fun `calculateScore returns correct values`() {
        assertEquals(20, GameConfig.calculateScore(1))
        assertEquals(60, GameConfig.calculateScore(3))
        assertEquals(180, GameConfig.calculateScore(9))
        assertEquals(540, GameConfig.calculateScore(27))
    }

    @Test
    fun `calculateScore with combo multiplier`() {
        assertEquals(40, GameConfig.calculateScore(1, 2))
        assertEquals(180, GameConfig.calculateScore(3, 3))
        assertEquals(720, GameConfig.calculateScore(9, 4))
    }

    @Test
    fun `isWinningTile returns correct values`() {
        assertFalse(GameConfig.isWinningTile(1))
        assertFalse(GameConfig.isWinningTile(3))
        assertFalse(GameConfig.isWinningTile(9))
        assertFalse(GameConfig.isWinningTile(27))
        assertFalse(GameConfig.isWinningTile(81))
        assertFalse(GameConfig.isWinningTile(243))
        assertFalse(GameConfig.isWinningTile(729))
        assertTrue(GameConfig.isWinningTile(2187))
        assertTrue(GameConfig.isWinningTile(6561))
        assertTrue(GameConfig.isWinningTile(19683))
    }

    @Test
    fun `constants have expected values`() {
        assertEquals(4, GameConfig.BOARD_SIZE)
        assertEquals(3, GameConfig.MIN_TILES_FOR_MERGE)
        assertEquals(2187, GameConfig.WIN_TILE_VALUE)
        assertEquals(2, GameConfig.INITIAL_TILES_COUNT)
        assertEquals(30, GameConfig.FREE_GAMES_LIMIT)
        assertEquals(199, GameConfig.PREMIUM_PRICE_CENTS)
    }

    @Test
    fun `spawn probabilities sum to 1`() {
        val totalProbability = GameConfig.SPAWN_PROBABILITIES.values.sum()
        assertEquals(1.0f, totalProbability, 0.01f)
    }
}