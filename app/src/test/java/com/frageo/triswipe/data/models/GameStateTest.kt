package com.frageo.triswipe.data.models

import org.junit.Test
import org.junit.Assert.*
import org.junit.Before

class GameStateTest {
    
    private lateinit var gameState: GameState
    
    @Before
    fun setUp() {
        val board = Array(4) { Array<Tile?>(4) { null } }
        board[0][0] = Tile(1, Position(0, 0), "tile1")
        board[0][1] = Tile(3, Position(0, 1), "tile2")
        board[1][0] = Tile(9, Position(1, 0), "tile3")
        
        gameState = GameState(
            board = board,
            score = 100,
            moves = 5,
            isGameOver = false,
            hasWon = false,
            lastMoveType = "SWIPE"
        )
    }
    
    @Test
    fun `withScore returns new state with updated score`() {
        val newState = gameState.withScore(200)
        
        assertEquals(200, newState.score)
        assertEquals(5, newState.moves) // Other properties unchanged
        assertFalse(newState.isGameOver)
    }
    
    @Test
    fun `withMoves returns new state with updated moves`() {
        val newState = gameState.withMoves(10)
        
        assertEquals(10, newState.moves)
        assertEquals(100, newState.score) // Other properties unchanged
        assertFalse(newState.isGameOver)
    }
    
    @Test
    fun `withGameOver returns new state with updated game over status`() {
        val newState = gameState.withGameOver(true)
        
        assertTrue(newState.isGameOver)
        assertEquals(100, newState.score) // Other properties unchanged
        assertEquals(5, newState.moves)
    }
    
    @Test
    fun `withWon returns new state with updated won status`() {
        val newState = gameState.withWon(true)
        
        assertTrue(newState.hasWon)
        assertEquals(100, newState.score) // Other properties unchanged
        assertEquals(5, newState.moves)
    }
    
    @Test
    fun `getTileAt returns correct tile for valid position`() {
        val tile = gameState.getTileAt(Position(0, 0))
        
        assertNotNull(tile)
        assertEquals(1, tile!!.value)
        assertEquals(Position(0, 0), tile.position)
    }
    
    @Test
    fun `getTileAt returns null for empty position`() {
        val tile = gameState.getTileAt(Position(2, 2))
        
        assertNull(tile)
    }
    
    @Test
    fun `getTileAt returns null for invalid position`() {
        val tile = gameState.getTileAt(Position(5, 5))
        
        assertNull(tile)
    }
    
    @Test
    fun `isEmpty returns true for empty position`() {
        assertTrue(gameState.isEmpty(Position(2, 2)))
    }
    
    @Test
    fun `isEmpty returns false for occupied position`() {
        assertFalse(gameState.isEmpty(Position(0, 0)))
    }
    
    @Test
    fun `isEmpty returns true for invalid position`() {
        assertTrue(gameState.isEmpty(Position(5, 5)))
    }
    
    @Test
    fun `getEmptyPositions returns correct count`() {
        val emptyPositions = gameState.getEmptyPositions()
        
        assertEquals(13, emptyPositions.size) // 16 total - 3 occupied
        assertFalse(emptyPositions.contains(Position(0, 0)))
        assertFalse(emptyPositions.contains(Position(0, 1)))
        assertFalse(emptyPositions.contains(Position(1, 0)))
        assertTrue(emptyPositions.contains(Position(2, 2)))
    }
    
    @Test
    fun `getEmptyPositions returns all positions for empty board`() {
        val emptyBoard = Array(4) { Array<Tile?>(4) { null } }
        val emptyGameState = GameState(
            board = emptyBoard,
            score = 0,
            moves = 0,
            isGameOver = false,
            hasWon = false
        )
        
        val emptyPositions = emptyGameState.getEmptyPositions()
        
        assertEquals(16, emptyPositions.size)
    }
    
    @Test
    fun `hasEmptySpaces returns true when board has empty spaces`() {
        assertTrue(gameState.hasEmptySpaces())
    }
    
    @Test
    fun `hasEmptySpaces returns false when board is full`() {
        val fullBoard = Array(4) { row ->
            Array<Tile?>(4) { col ->
                Tile(1, Position(row, col))
            }
        }
        val fullGameState = GameState(
            board = fullBoard,
            score = 0,
            moves = 0,
            isGameOver = false,
            hasWon = false
        )
        
        assertFalse(fullGameState.hasEmptySpaces())
    }
    
    @Test
    fun `equals returns true for identical states`() {
        val board2 = Array(4) { Array<Tile?>(4) { null } }
        board2[0][0] = Tile(1, Position(0, 0), "tile1")
        board2[0][1] = Tile(3, Position(0, 1), "tile2")
        board2[1][0] = Tile(9, Position(1, 0), "tile3")
        
        val gameState2 = GameState(
            board = board2,
            score = 100,
            moves = 5,
            isGameOver = false,
            hasWon = false,
            lastMoveType = "SWIPE"
        )
        
        assertEquals(gameState, gameState2)
    }
    
    @Test
    fun `equals returns false for different scores`() {
        val board2 = Array(4) { Array<Tile?>(4) { null } }
        board2[0][0] = Tile(1, Position(0, 0), "tile1")
        board2[0][1] = Tile(3, Position(0, 1), "tile2")
        board2[1][0] = Tile(9, Position(1, 0), "tile3")
        
        val gameState2 = GameState(
            board = board2,
            score = 200, // Different score
            moves = 5,
            isGameOver = false,
            hasWon = false,
            lastMoveType = "SWIPE"
        )
        
        assertNotEquals(gameState, gameState2)
    }
    
    @Test
    fun `equals returns false for different board states`() {
        val board2 = Array(4) { Array<Tile?>(4) { null } }
        board2[0][0] = Tile(1, Position(0, 0), "tile1")
        board2[0][1] = Tile(3, Position(0, 1), "tile2")
        // Missing tile at [1][0]
        
        val gameState2 = GameState(
            board = board2,
            score = 100,
            moves = 5,
            isGameOver = false,
            hasWon = false,
            lastMoveType = "SWIPE"
        )
        
        assertNotEquals(gameState, gameState2)
    }
    
    @Test
    fun `hashCode is consistent for identical states`() {
        val board2 = Array(4) { Array<Tile?>(4) { null } }
        board2[0][0] = Tile(1, Position(0, 0), "tile1")
        board2[0][1] = Tile(3, Position(0, 1), "tile2")
        board2[1][0] = Tile(9, Position(1, 0), "tile3")
        
        val gameState2 = GameState(
            board = board2,
            score = 100,
            moves = 5,
            isGameOver = false,
            hasWon = false,
            lastMoveType = "SWIPE"
        )
        
        assertEquals(gameState.hashCode(), gameState2.hashCode())
    }
    
    @Test
    fun `initialization validates board size`() {
        try {
            val invalidBoard = Array(3) { Array<Tile?>(3) { null } }
            GameState(
                board = invalidBoard,
                score = 0,
                moves = 0,
                isGameOver = false,
                hasWon = false
            )
            fail("Should throw IllegalArgumentException for invalid board size")
        } catch (e: IllegalArgumentException) {
            assertTrue(e.message!!.contains("Board must be 4x4"))
        }
    }
}