package com.frageo.triswipe.data.commands

import com.frageo.triswipe.data.models.Direction
import com.frageo.triswipe.data.models.GameEngine
import com.frageo.triswipe.data.models.GameState
import com.frageo.triswipe.data.models.Tile
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.mockito.kotlin.any
import org.mockito.kotlin.whenever

class SwipeCommandTest {

    @Mock
    private lateinit var mockGameEngine: GameEngine
    
    private lateinit var swipeCommand: SwipeCommand
    
    @Before
    fun setUp() {
        MockitoAnnotations.openMocks(this)
        swipeCommand = SwipeCommand(Direction.LEFT)
    }
    
    @Test
    fun `command has correct description`() {
        val leftCommand = SwipeCommand(Direction.LEFT)
        val rightCommand = SwipeCommand(Direction.RIGHT)
        val upCommand = SwipeCommand(Direction.UP)
        val downCommand = SwipeCommand(Direction.DOWN)
        
        assertEquals("Swipe left", leftCommand.description)
        assertEquals("Swipe right", rightCommand.description)
        assertEquals("Swipe up", upCommand.description)
        assertEquals("Swipe down", downCommand.description)
    }
    
    @Test
    fun `command has unique id`() {
        val command1 = SwipeCommand(Direction.LEFT)
        val command2 = SwipeCommand(Direction.LEFT)
        
        assertNotEquals(command1.id, command2.id)
    }
    
    @Test
    fun `canExecute returns false when game is over`() {
        val gameOverState = createGameState(isGameOver = true)
        whenever(mockGameEngine.getCurrentGameState()).thenReturn(gameOverState)
        
        assertFalse(swipeCommand.canExecute(mockGameEngine))
    }
    
    @Test
    fun `canExecute returns true when game is active`() {
        val activeGameState = createGameState(isGameOver = false)
        whenever(mockGameEngine.getCurrentGameState()).thenReturn(activeGameState)
        
        assertTrue(swipeCommand.canExecute(mockGameEngine))
    }
    
    @Test
    fun `canUndo returns false before execution`() {
        assertFalse(swipeCommand.canUndo())
    }
    
    @Test
    fun `execute captures game state and delegates to engine`() {
        val initialState = createGameState(score = 100, moves = 5)
        val resultState = createGameState(score = 150, moves = 6)
        val mockSwipeResult = com.frageo.triswipe.data.models.SwipeResult(
            gameState = resultState,
            success = true,
            message = "Swipe completed",
            tileMovements = emptyList(),
            mergeActions = emptyList(),
            scoreGained = 50
        )
        
        whenever(mockGameEngine.getCurrentGameState()).thenReturn(initialState)
        whenever(mockGameEngine.performSwipe(Direction.LEFT)).thenReturn(mockSwipeResult)
        
        val result = swipeCommand.execute(mockGameEngine)
        
        assertTrue(result.success)
        assertEquals("Swipe left completed", result.message)
        assertEquals(resultState, result.gameState)
        assertEquals(50, result.scoreGained)
        assertTrue(swipeCommand.canUndo())
    }
    
    @Test
    fun `execute handles engine failure`() {
        val initialState = createGameState()
        val failedSwipeResult = com.frageo.triswipe.data.models.SwipeResult(
            gameState = initialState,
            success = false,
            message = "No valid moves",
            tileMovements = emptyList(),
            mergeActions = emptyList(),
            scoreGained = 0
        )
        
        whenever(mockGameEngine.getCurrentGameState()).thenReturn(initialState)
        whenever(mockGameEngine.performSwipe(Direction.LEFT)).thenReturn(failedSwipeResult)
        
        val result = swipeCommand.execute(mockGameEngine)
        
        assertFalse(result.success)
        assertEquals("No valid moves", result.message)
        assertFalse(swipeCommand.canUndo())
    }
    
    @Test
    fun `undo restores previous state`() {
        val initialState = createGameState(score = 100, moves = 5)
        val resultState = createGameState(score = 150, moves = 6)
        val mockSwipeResult = com.frageo.triswipe.data.models.SwipeResult(
            gameState = resultState,
            success = true,
            message = "Swipe completed",
            tileMovements = emptyList(),
            mergeActions = emptyList(),
            scoreGained = 50
        )
        
        whenever(mockGameEngine.getCurrentGameState())
            .thenReturn(initialState)
            .thenReturn(resultState)
            .thenReturn(initialState) // After undo
        whenever(mockGameEngine.performSwipe(Direction.LEFT)).thenReturn(mockSwipeResult)
        
        // Execute the command
        swipeCommand.execute(mockGameEngine)
        assertTrue(swipeCommand.canUndo())
        
        // Undo the command
        val undoResult = swipeCommand.undo(mockGameEngine)
        
        assertTrue(undoResult.success)
        assertEquals("Swipe left undone", undoResult.message)
        assertEquals(initialState, undoResult.gameState)
    }
    
    @Test
    fun `undo fails when command not executed`() {
        val currentState = createGameState()
        whenever(mockGameEngine.getCurrentGameState()).thenReturn(currentState)
        
        val undoResult = swipeCommand.undo(mockGameEngine)
        
        assertFalse(undoResult.success)
        assertEquals("Cannot undo swipe command - not executed or no previous state", undoResult.message)
    }
    
    @Test
    fun `toString returns meaningful description`() {
        val command = SwipeCommand(Direction.RIGHT)
        val string = command.toString()
        
        assertTrue(string.contains("SwipeCommand"))
        assertTrue(string.contains("RIGHT"))
        assertTrue(string.contains("executed=false"))
        assertTrue(string.contains("canUndo=false"))
    }
    
    private fun createGameState(
        score: Int = 0,
        moves: Int = 0,
        isGameOver: Boolean = false,
        hasWon: Boolean = false
    ): GameState {
        val emptyBoard = Array(4) { Array<Tile?>(4) { null } }
        return GameState(
            board = emptyBoard,
            score = score,
            moves = moves,
            isGameOver = isGameOver,
            hasWon = hasWon
        )
    }
}