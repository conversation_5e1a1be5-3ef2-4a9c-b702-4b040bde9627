package com.frageo.triswipe.data.models

import com.frageo.triswipe.data.models.managers.UndoManagerImpl
import com.frageo.triswipe.data.models.managers.MoveAnalyzerImpl
import com.frageo.triswipe.data.models.managers.GameValidatorImpl
import com.frageo.triswipe.data.models.managers.MoveExecutorImpl
import com.frageo.triswipe.data.repository.GameStatisticsManager
import org.junit.Test
import org.junit.Assert.*
import org.junit.Before
import org.mockito.Mock
import org.mockito.MockitoAnnotations

class GameEngineSimpleTest {
    
    @Mock
    private lateinit var statisticsManager: GameStatisticsManager
    
    private lateinit var gameEngine: GameEngine
    
    @Before
    fun setUp() {
        MockitoAnnotations.openMocks(this)
        
        // Create dependencies for GameEngine
        val mergeDetector = MergeDetector()
        val mergeExecutor = MergeExecutor()
        val undoManager = UndoManagerImpl()
        val gameValidator = GameValidatorImpl(mergeDetector)
        val moveAnalyzer = MoveAnalyzerImpl(mergeDetector, mergeExecutor)
        val moveExecutor = MoveExecutorImpl(mergeDetector, mergeExecutor, gameValidator)
        
        val mockSwapModeManager = org.mockito.kotlin.mock<com.frageo.triswipe.data.models.managers.SwapModeManager>()
        // Set up mock to return validation failure for non-adjacent tiles
        org.mockito.kotlin.whenever(mockSwapModeManager.isSwapAllowed(org.mockito.kotlin.any(), org.mockito.kotlin.any(), org.mockito.kotlin.any(), org.mockito.kotlin.any()))
            .thenReturn(com.frageo.triswipe.data.models.managers.SwapValidationResult(false, "Positions must be adjacent and valid"))
        
        gameEngine = GameEngine(undoManager, moveAnalyzer, gameValidator, moveExecutor, mergeDetector, mergeExecutor, statisticsManager, mockSwapModeManager)
    }
    
    @Test
    fun `initializeGame creates valid initial state`() {
        val gameState = gameEngine.initializeGame()
        
        assertEquals(0, gameState.score)
        assertEquals(0, gameState.moves)
        assertFalse(gameState.isGameOver)
        assertFalse(gameState.hasWon)
        
        // Should have some tiles on board
        val emptyPositions = gameState.getEmptyPositions()
        assertTrue(emptyPositions.size < 16)
    }
    
    @Test
    fun `performSwipe returns failure when game is over`() {
        gameEngine.initializeGame()
        
        // Use reflection to set game over state
        val gameOverField = gameEngine.javaClass.getDeclaredField("isGameOver")
        gameOverField.isAccessible = true
        gameOverField.setBoolean(gameEngine, true)
        
        val result = gameEngine.performSwipe(Direction.UP)
        
        assertFalse(result.success)
        assertEquals("Game is over", result.message)
    }
    
    @Test
    fun `performSwipe increments move counter on valid move`() {
        val initialState = gameEngine.initializeGame()
        
        val result = gameEngine.performSwipe(Direction.LEFT)
        
        if (result.success) {
            assertTrue(result.gameState.moves > initialState.moves)
        }
    }
    
    @Test
    fun `performTileSwap fails for non-adjacent tiles`() {
        gameEngine.initializeGame()
        
        val result = gameEngine.performTileSwap(Position(0, 0), Position(2, 2))
        
        assertFalse(result.success)
        assertTrue(result.message.contains("adjacent") || result.message.contains("valid"))
    }
    
    @Test
    fun `canUndo returns false initially`() {
        gameEngine.initializeGame()
        
        assertFalse(gameEngine.canUndo())
    }
    
    @Test
    fun `canUndo returns true after successful move`() {
        gameEngine.initializeGame()
        
        val result = gameEngine.performSwipe(Direction.LEFT)
        if (result.success) {
            assertTrue(gameEngine.canUndo())
        }
    }
    
    @Test
    fun `getUndoCount returns zero initially`() {
        gameEngine.initializeGame()
        
        assertEquals(0, gameEngine.getUndoCount())
    }
    
    @Test
    fun `performUndo fails when no history available`() {
        gameEngine.initializeGame()
        
        val result = gameEngine.performUndo()
        
        assertFalse(result.success)
        assertTrue(result.message.contains("No moves to undo"))
    }
    
    @Test
    fun `getCurrentGameState returns valid state`() {
        gameEngine.initializeGame()
        
        val gameState = gameEngine.getCurrentGameState()
        
        assertNotNull(gameState)
        assertTrue(gameState.score >= 0)
        assertTrue(gameState.moves >= 0)
        assertNotNull(gameState.board)
    }
}