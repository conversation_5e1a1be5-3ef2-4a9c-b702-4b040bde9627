package com.frageo.triswipe.data.models

import org.junit.Test
import org.junit.Assert.*
import org.junit.Before

class MergeExecutorSimpleTest {
    
    private lateinit var mergeExecutor: MergeExecutor
    private lateinit var mergeDetector: MergeDetector
    private lateinit var gameBoard: GameBoard
    
    @Before
    fun setUp() {
        mergeExecutor = MergeExecutor()
        mergeDetector = MergeDetector()
        gameBoard = GameBoard()
    }
    
    @Test
    fun `executeMerges returns empty result for empty board`() {
        val result = mergeExecutor.executeMerges(gameBoard, mergeDetector)
        
        assertTrue(result.mergeActions.isEmpty())
        assertEquals(0, result.totalScore)
    }
    
    @Test
    fun `executeMerges returns empty result when no merges available`() {
        gameBoard.setTile(Position(0, 0), Tile(1, Position(0, 0)))
        gameBoard.setTile(Position(0, 1), Tile(3, Position(0, 1)))
        gameBoard.setTile(Position(0, 2), Tile(9, Position(0, 2)))
        
        val result = mergeExecutor.executeMerges(gameBoard, mergeDetector)
        
        assertTrue(result.mergeActions.isEmpty())
        assertEquals(0, result.totalScore)
    }
    
    @Test
    fun `executeMerges processes single horizontal merge`() {
        gameBoard.setTile(Position(0, 0), Tile(1, Position(0, 0)))
        gameBoard.setTile(Position(0, 1), Tile(1, Position(0, 1)))
        gameBoard.setTile(Position(0, 2), Tile(1, Position(0, 2)))
        
        val result = mergeExecutor.executeMerges(gameBoard, mergeDetector)
        
        assertEquals(1, result.mergeActions.size)
        assertTrue(result.totalScore > 0)
        
        val mergeAction = result.mergeActions[0]
        assertEquals(3, mergeAction.removedTiles.size)
        assertEquals(GameConfig.getNextTileValue(1), mergeAction.createdTile.value)
    }
    
    @Test
    fun `executeMerges processes single vertical merge`() {
        gameBoard.setTile(Position(0, 0), Tile(3, Position(0, 0)))
        gameBoard.setTile(Position(1, 0), Tile(3, Position(1, 0)))
        gameBoard.setTile(Position(2, 0), Tile(3, Position(2, 0)))
        
        val result = mergeExecutor.executeMerges(gameBoard, mergeDetector)
        
        assertEquals(1, result.mergeActions.size)
        assertTrue(result.totalScore > 0)
        
        val mergeAction = result.mergeActions[0]
        assertEquals(3, mergeAction.removedTiles.size)
        assertEquals(GameConfig.getNextTileValue(3), mergeAction.createdTile.value)
    }
    
    @Test
    fun `executeMerges processes multiple separate merges`() {
        // Set up two separate merge groups
        gameBoard.setTile(Position(0, 0), Tile(1, Position(0, 0)))
        gameBoard.setTile(Position(0, 1), Tile(1, Position(0, 1)))
        gameBoard.setTile(Position(0, 2), Tile(1, Position(0, 2)))
        
        gameBoard.setTile(Position(2, 0), Tile(3, Position(2, 0)))
        gameBoard.setTile(Position(2, 1), Tile(3, Position(2, 1)))
        gameBoard.setTile(Position(2, 2), Tile(3, Position(2, 2)))
        
        val result = mergeExecutor.executeMerges(gameBoard, mergeDetector)
        
        assertEquals(2, result.mergeActions.size)
        assertTrue(result.totalScore > 0)
    }
    
    @Test
    fun `executeMerges correctly removes old tiles and places new tile`() {
        gameBoard.setTile(Position(0, 0), Tile(1, Position(0, 0)))
        gameBoard.setTile(Position(0, 1), Tile(1, Position(0, 1)))
        gameBoard.setTile(Position(0, 2), Tile(1, Position(0, 2)))
        
        val result = mergeExecutor.executeMerges(gameBoard, mergeDetector)
        
        // New tile should be at center position
        val newTile = gameBoard.getTile(Position(0, 1))
        assertNotNull(newTile)
        assertEquals(GameConfig.getNextTileValue(1), newTile!!.value)
    }
    
    @Test
    fun `executeAutoMerges returns empty result when no merges available`() {
        gameBoard.setTile(Position(0, 0), Tile(1, Position(0, 0)))
        gameBoard.setTile(Position(0, 1), Tile(3, Position(0, 1)))
        gameBoard.setTile(Position(0, 2), Tile(9, Position(0, 2)))
        
        val result = mergeExecutor.executeAutoMerges(gameBoard, mergeDetector)
        
        assertEquals(0, result.totalScore)
        assertTrue(result.mergeActions.isEmpty())
    }
    
    @Test
    fun `executeAutoMerges processes available merges`() {
        gameBoard.setTile(Position(0, 0), Tile(1, Position(0, 0)))
        gameBoard.setTile(Position(0, 1), Tile(1, Position(0, 1)))
        gameBoard.setTile(Position(0, 2), Tile(1, Position(0, 2)))
        
        val result = mergeExecutor.executeAutoMerges(gameBoard, mergeDetector)
        
        assertTrue(result.totalScore > 0)
        assertEquals(1, result.mergeActions.size)
    }
}