package com.frageo.triswipe.data.models

import org.junit.Test
import org.junit.Assert.*
import org.junit.Before

class GameStatsTest {
    
    private lateinit var gameStats: GameStats
    
    @Before
    fun setUp() {
        gameStats = GameStats(
            highScore = 1000,
            gamesPlayed = 10,
            gamesWon = 6,
            totalMoves = 500,
            totalMerges = 100,
            bestTile = 243,
            averageScore = 800.0,
            longestWinStreak = 4,
            currentWinStreak = 2,
            totalPlayTime = 3600000L,
            freeGamesRemaining = 25,
            hasPurchasedPremium = false
        )
    }
    
    @Test
    fun `withHighScore updates high score when new score is higher`() {
        val newStats = gameStats.withHighScore(1200)
        
        assertEquals(1200, newStats.highScore)
        assertEquals(10, newStats.gamesPlayed) // Other properties unchanged
    }
    
    @Test
    fun `withHighScore keeps existing high score when new score is lower`() {
        val newStats = gameStats.withHighScore(800)
        
        assertEquals(1000, newStats.highScore) // Unchanged
        assertEquals(10, newStats.gamesPlayed) // Other properties unchanged
    }
    
    @Test
    fun `withGamePlayed updates statistics for won game`() {
        val newStats = gameStats.withGamePlayed(
            score = 1200,
            moves = 50,
            won = true,
            playTime = 600000L
        )
        
        assertEquals(1200, newStats.highScore) // Updated high score
        assertEquals(11, newStats.gamesPlayed) // Incremented
        assertEquals(7, newStats.gamesWon) // Incremented
        assertEquals(550, newStats.totalMoves) // Added moves
        assertEquals(4200000L, newStats.totalPlayTime) // Added play time
        assertEquals(3, newStats.currentWinStreak) // Incremented
        assertEquals(4, newStats.longestWinStreak) // Unchanged (not surpassed)
        assertEquals(25, newStats.freeGamesRemaining) // Unchanged (now managed by PremiumStatusManager)
        
        // Check average score calculation
        val expectedAverage = (800.0 * 10 + 1200) / 11
        assertEquals(expectedAverage, newStats.averageScore, 0.01)
    }
    
    @Test
    fun `withGamePlayed updates statistics for lost game`() {
        val newStats = gameStats.withGamePlayed(
            score = 600,
            moves = 30,
            won = false,
            playTime = 300000L
        )
        
        assertEquals(1000, newStats.highScore) // Unchanged (lower score)
        assertEquals(11, newStats.gamesPlayed) // Incremented
        assertEquals(6, newStats.gamesWon) // Unchanged (lost)
        assertEquals(530, newStats.totalMoves) // Added moves
        assertEquals(3900000L, newStats.totalPlayTime) // Added play time
        assertEquals(0, newStats.currentWinStreak) // Reset to 0
        assertEquals(4, newStats.longestWinStreak) // Unchanged
        assertEquals(25, newStats.freeGamesRemaining) // Unchanged (now managed by PremiumStatusManager)
        
        // Check average score calculation
        val expectedAverage = (800.0 * 10 + 600) / 11
        assertEquals(expectedAverage, newStats.averageScore, 0.01)
    }
    
    @Test
    fun `withGamePlayed updates longest win streak when surpassed`() {
        val statsWithLowStreak = gameStats.copy(
            longestWinStreak = 2,
            currentWinStreak = 2
        )
        
        val newStats = statsWithLowStreak.withGamePlayed(
            score = 800,
            moves = 40,
            won = true,
            playTime = 400000L
        )
        
        assertEquals(3, newStats.currentWinStreak) // Incremented
        assertEquals(3, newStats.longestWinStreak) // Updated to new max
    }
    
    @Test
    fun `withGamePlayed doesn't decrement free games for premium users`() {
        val premiumStats = gameStats.copy(hasPurchasedPremium = true)
        
        val newStats = premiumStats.withGamePlayed(
            score = 800,
            moves = 40,
            won = true,
            playTime = 400000L
        )
        
        assertEquals(25, newStats.freeGamesRemaining) // Unchanged for premium
    }
    
    @Test
    fun `withGamePlayed doesn't go below zero free games`() {
        val almostEmptyStats = gameStats.copy(freeGamesRemaining = 0)
        
        val newStats = almostEmptyStats.withGamePlayed(
            score = 800,
            moves = 40,
            won = true,
            playTime = 400000L
        )
        
        assertEquals(0, newStats.freeGamesRemaining) // Stays at 0
    }
    
    @Test
    fun `withMerge updates merge count and best tile`() {
        val newStats = gameStats.withMerge(729)
        
        assertEquals(101, newStats.totalMerges) // Incremented
        assertEquals(729, newStats.bestTile) // Updated to higher value
        assertEquals(10, newStats.gamesPlayed) // Other properties unchanged
    }
    
    @Test
    fun `withMerge doesn't update best tile for lower values`() {
        val newStats = gameStats.withMerge(81)
        
        assertEquals(101, newStats.totalMerges) // Incremented
        assertEquals(243, newStats.bestTile) // Unchanged (lower value)
    }
    
    @Test
    fun `withPremiumPurchased updates premium status and free games`() {
        val newStats = gameStats.withPremiumPurchased()
        
        assertTrue(newStats.hasPurchasedPremium)
        assertEquals(Int.MAX_VALUE, newStats.freeGamesRemaining)
        assertEquals(10, newStats.gamesPlayed) // Other properties unchanged
    }
    
    @Test
    fun `canPlayGame returns true when user has premium`() {
        val premiumStats = gameStats.copy(hasPurchasedPremium = true)
        
        assertTrue(premiumStats.canPlayGame())
    }
    
    @Test
    fun `canPlayGame returns true when user has free games remaining`() {
        val freeStats = gameStats.copy(
            hasPurchasedPremium = false,
            freeGamesRemaining = 5
        )
        
        assertTrue(freeStats.canPlayGame())
    }
    
    @Test
    fun `canPlayGame returns false when user has no premium and no free games`() {
        val emptyStats = gameStats.copy(
            hasPurchasedPremium = false,
            freeGamesRemaining = 0
        )
        
        assertFalse(emptyStats.canPlayGame())
    }
    
    @Test
    fun `averageMovesPerGame calculates correctly`() {
        val expectedAverage = 500.0 / 10.0
        assertEquals(expectedAverage, gameStats.averageMovesPerGame, 0.01)
    }
    
    @Test
    fun `averageMovesPerGame returns zero for no games played`() {
        val noGamesStats = gameStats.copy(gamesPlayed = 0, totalMoves = 0)
        
        assertEquals(0.0, noGamesStats.averageMovesPerGame, 0.01)
    }
    
    @Test
    fun `averagePlayTimePerGame calculates correctly`() {
        val expectedAverage = 3600000.0 / 10.0
        assertEquals(expectedAverage, gameStats.averagePlayTimePerGame, 0.01)
    }
    
    @Test
    fun `averagePlayTimePerGame returns zero for no games played`() {
        val noGamesStats = gameStats.copy(gamesPlayed = 0, totalPlayTime = 0L)
        
        assertEquals(0.0, noGamesStats.averagePlayTimePerGame, 0.01)
    }
    
    @Test
    fun `default constructor creates valid empty stats`() {
        val defaultStats = GameStats()
        
        assertEquals(0, defaultStats.highScore)
        assertEquals(0, defaultStats.gamesPlayed)
        assertEquals(0, defaultStats.gamesWon)
        assertEquals(0, defaultStats.totalMoves)
        assertEquals(0, defaultStats.totalMerges)
        assertEquals(0, defaultStats.bestTile)
        assertEquals(0.0, defaultStats.averageScore, 0.01)
        assertEquals(0, defaultStats.longestWinStreak)
        assertEquals(0, defaultStats.currentWinStreak)
        assertEquals(0L, defaultStats.totalPlayTime)
        assertEquals(30, defaultStats.freeGamesRemaining)
        assertFalse(defaultStats.hasPurchasedPremium)
        assertTrue(defaultStats.canPlayGame()) // Should be able to play with 30 free games
    }
    
    @Test
    fun `complex scenario with multiple game results`() {
        var stats = GameStats()
        
        // Play and win first game
        stats = stats.withGamePlayed(500, 25, true, 300000L)
        assertEquals(1, stats.gamesWon)
        assertEquals(1, stats.currentWinStreak)
        assertEquals(30, stats.freeGamesRemaining) // Unchanged (now managed by PremiumStatusManager)
        
        // Play and lose second game
        stats = stats.withGamePlayed(300, 30, false, 250000L)
        assertEquals(1, stats.gamesWon)
        assertEquals(0, stats.currentWinStreak)
        assertEquals(30, stats.freeGamesRemaining) // Unchanged (now managed by PremiumStatusManager)
        
        // Play and win third game
        stats = stats.withGamePlayed(800, 20, true, 400000L)
        assertEquals(2, stats.gamesWon)
        assertEquals(1, stats.currentWinStreak)
        assertEquals(30, stats.freeGamesRemaining) // Unchanged (now managed by PremiumStatusManager)
        
        // Verify final statistics
        assertEquals(800, stats.highScore)
        assertEquals(3, stats.gamesPlayed)
        assertEquals(2, stats.gamesWon)
        assertEquals(75, stats.totalMoves)
        assertEquals(950000L, stats.totalPlayTime)
        assertEquals(25.0, stats.averageMovesPerGame, 0.01)
    }
}