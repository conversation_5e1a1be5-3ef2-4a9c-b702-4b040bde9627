package com.frageo.triswipe.data.models

import org.junit.Test
import org.junit.Assert.*

class PositionTest {

    @Test
    fun `isValid returns true for valid positions`() {
        val position = Position(1, 2)
        assertTrue(position.isValid())
    }

    @Test
    fun `isValid returns false for negative row`() {
        val position = Position(-1, 2)
        assertFalse(position.isValid())
    }

    @Test
    fun `isValid returns false for negative col`() {
        val position = Position(1, -1)
        assertFalse(position.isValid())
    }

    @Test
    fun `isValid returns false for row out of bounds`() {
        val position = Position(4, 2)
        assertFalse(position.isValid())
    }

    @Test
    fun `isValid returns false for col out of bounds`() {
        val position = Position(1, 4)
        assertFalse(position.isValid())
    }

    @Test
    fun `isAdjacent returns true for horizontally adjacent positions`() {
        val pos1 = Position(1, 1)
        val pos2 = Position(1, 2)
        assertTrue(pos1.isAdjacent(pos2))
        assertTrue(pos2.isAdjacent(pos1))
    }

    @Test
    fun `isAdjacent returns true for vertically adjacent positions`() {
        val pos1 = Position(1, 1)
        val pos2 = Position(2, 1)
        assertTrue(pos1.isAdjacent(pos2))
        assertTrue(pos2.isAdjacent(pos1))
    }

    @Test
    fun `isAdjacent returns false for diagonal positions`() {
        val pos1 = Position(1, 1)
        val pos2 = Position(2, 2)
        assertFalse(pos1.isAdjacent(pos2))
    }

    @Test
    fun `isAdjacent returns false for same position`() {
        val pos1 = Position(1, 1)
        val pos2 = Position(1, 1)
        assertFalse(pos1.isAdjacent(pos2))
    }

    @Test
    fun `isAdjacent returns false for distant positions`() {
        val pos1 = Position(0, 0)
        val pos2 = Position(3, 3)
        assertFalse(pos1.isAdjacent(pos2))
    }
}