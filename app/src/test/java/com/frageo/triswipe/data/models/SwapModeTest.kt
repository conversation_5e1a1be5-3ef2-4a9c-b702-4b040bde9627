package com.frageo.triswipe.data.models

import org.junit.Test
import org.junit.Assert.*

class SwapModeTest {
    
    @Test
    fun `fromString returns correct swap mode`() {
        assertEquals(SwapMode.FREE_SWAPPING, SwapMode.fromString("FREE_SWAPPING"))
        assertEquals(SwapMode.MERGE_ONLY, SwapMode.fromString("MERGE_ONLY"))
        assertEquals(SwapMode.ONE_SWAP_PER_TURN, SwapMode.fromString("ONE_SWAP_PER_TURN"))
        assertEquals(SwapMode.SWAP_ON_UPGRADE, SwapMode.fromString("SWAP_ON_UPGRADE"))
        assertEquals(SwapMode.NO_SWAPPING, SwapMode.fromString("NO_SWAPPING"))
    }
    
    @Test
    fun `fromString returns default for invalid input`() {
        assertEquals(SwapMode.FREE_SWAPPING, SwapMode.fromString("INVALID"))
        assertEquals(SwapMode.FREE_SWAPPING, SwapMode.fromString(null))
        assertEquals(SwapMode.FREE_SWAPPING, SwapMode.fromString(""))
    }
    
    @Test
    fun `getAvailableModes returns correct modes for free user`() {
        val modes = SwapMode.getAvailableModes(isPremiumUser = false)
        assertEquals(1, modes.size)
        assertEquals(SwapMode.FREE_SWAPPING, modes[0])
    }
    
    @Test
    fun `getAvailableModes returns all modes for premium user`() {
        val modes = SwapMode.getAvailableModes(isPremiumUser = true)
        assertEquals(5, modes.size)
        assertTrue(modes.contains(SwapMode.FREE_SWAPPING))
        assertTrue(modes.contains(SwapMode.MERGE_ONLY))
        assertTrue(modes.contains(SwapMode.ONE_SWAP_PER_TURN))
        assertTrue(modes.contains(SwapMode.SWAP_ON_UPGRADE))
        assertTrue(modes.contains(SwapMode.NO_SWAPPING))
    }
    
    @Test
    fun `premium feature flags are correct`() {
        assertFalse(SwapMode.FREE_SWAPPING.isPremiumFeature)
        assertTrue(SwapMode.MERGE_ONLY.isPremiumFeature)
        assertTrue(SwapMode.ONE_SWAP_PER_TURN.isPremiumFeature)
        assertTrue(SwapMode.SWAP_ON_UPGRADE.isPremiumFeature)
        assertTrue(SwapMode.NO_SWAPPING.isPremiumFeature)
    }
}

class SwapModeStateTest {
    
    @Test
    fun `free swapping allows swapping always`() {
        val state = SwapModeState(currentMode = SwapMode.FREE_SWAPPING)
        assertTrue(state.isSwappingAllowed(currentMoves = 5))
        assertTrue(state.isSwappingAllowed(currentMoves = 10))
    }
    
    @Test
    fun `merge only allows swapping always but validation happens elsewhere`() {
        val state = SwapModeState(currentMode = SwapMode.MERGE_ONLY)
        assertTrue(state.isSwappingAllowed(currentMoves = 5))
    }
    
    @Test
    fun `one swap per turn tracks usage correctly`() {
        val state = SwapModeState(currentMode = SwapMode.ONE_SWAP_PER_TURN)
        
        // Initially allows swapping
        assertTrue(state.isSwappingAllowed(currentMoves = 1))
        
        // After swap, doesn't allow more swaps
        val afterSwap = state.afterSwap()
        assertFalse(afterSwap.isSwappingAllowed(currentMoves = 1))
        
        // After swipe, resets and allows swapping again
        val afterSwipe = afterSwap.afterSwipe()
        assertTrue(afterSwipe.isSwappingAllowed(currentMoves = 2))
    }
    
    @Test
    fun `swap on upgrade tracks unlock status correctly`() {
        val state = SwapModeState(
            currentMode = SwapMode.SWAP_ON_UPGRADE,
            swapUnlockedForTurn = false
        )
        
        // Initially doesn't allow swapping
        assertFalse(state.isSwappingAllowed(currentMoves = 1))
        
        // After upgrade, allows swapping
        val afterUpgrade = state.onTileUpgrade(newTileValue = 9, currentMoves = 1)
        assertTrue(afterUpgrade.isSwappingAllowed(currentMoves = 1))
        
        // After swap, locks again
        val afterSwap = afterUpgrade.afterSwap()
        assertFalse(afterSwap.isSwappingAllowed(currentMoves = 1))
    }
    
    @Test
    fun `no swapping never allows swapping`() {
        val state = SwapModeState(currentMode = SwapMode.NO_SWAPPING)
        assertFalse(state.isSwappingAllowed(currentMoves = 1))
        assertFalse(state.isSwappingAllowed(currentMoves = 10))
    }
    
    @Test
    fun `status messages are appropriate for each mode`() {
        val freeState = SwapModeState(currentMode = SwapMode.FREE_SWAPPING)
        assertNull(freeState.getStatusMessage())
        
        val mergeOnlyState = SwapModeState(currentMode = SwapMode.MERGE_ONLY)
        assertEquals("Swaps must create merges", mergeOnlyState.getStatusMessage())
        
        val oneSwapState = SwapModeState(currentMode = SwapMode.ONE_SWAP_PER_TURN)
        assertEquals("One swap available this turn", oneSwapState.getStatusMessage())
        
        val oneSwapUsedState = SwapModeState(
            currentMode = SwapMode.ONE_SWAP_PER_TURN,
            swapsUsedThisTurn = 1
        )
        assertEquals("Swap used this turn - swipe to reset", oneSwapUsedState.getStatusMessage())
        
        val upgradeUnlockedState = SwapModeState(
            currentMode = SwapMode.SWAP_ON_UPGRADE,
            swapUnlockedForTurn = true
        )
        assertEquals("Swap unlocked! Use it wisely", upgradeUnlockedState.getStatusMessage())
        
        val upgradeLockedState = SwapModeState(
            currentMode = SwapMode.SWAP_ON_UPGRADE,
            swapUnlockedForTurn = false
        )
        assertEquals("Reach a new tile value to unlock swap", upgradeLockedState.getStatusMessage())
        
        val noSwapState = SwapModeState(currentMode = SwapMode.NO_SWAPPING)
        assertEquals("Swapping disabled - swipe only", noSwapState.getStatusMessage())
    }
    
    @Test
    fun `tile upgrade only unlocks for higher values`() {
        val state = SwapModeState(
            currentMode = SwapMode.SWAP_ON_UPGRADE,
            highestTileValueAchieved = 9,
            swapUnlockedForTurn = false
        )
        
        // Same value doesn't unlock
        val sameValue = state.onTileUpgrade(newTileValue = 9, currentMoves = 5)
        assertFalse(sameValue.swapUnlockedForTurn)
        assertEquals(9, sameValue.highestTileValueAchieved)
        
        // Lower value doesn't unlock
        val lowerValue = state.onTileUpgrade(newTileValue = 3, currentMoves = 5)
        assertFalse(lowerValue.swapUnlockedForTurn)
        assertEquals(9, lowerValue.highestTileValueAchieved)
        
        // Higher value unlocks
        val higherValue = state.onTileUpgrade(newTileValue = 27, currentMoves = 5)
        assertTrue(higherValue.swapUnlockedForTurn)
        assertEquals(27, higherValue.highestTileValueAchieved)
        assertEquals(5, higherValue.lastUpgradeMove)
    }
}