package com.frageo.triswipe.data.models

import org.junit.Test
import org.junit.Assert.*

class TileTest {

    @Test
    fun `tile creation with default id`() {
        val position = Position(1, 1)
        val tile = Tile(3, position)
        
        assertEquals(3, tile.value)
        assertEquals(position, tile.position)
        assertNotNull(tile.id)
        assertTrue(tile.id.isNotEmpty())
    }

    @Test
    fun `tile creation with custom id`() {
        val position = Position(1, 1)
        val customId = "custom-id"
        val tile = Tile(9, position, customId)
        
        assertEquals(9, tile.value)
        assertEquals(position, tile.position)
        assertEquals(customId, tile.id)
    }

    @Test
    fun `withPosition creates new tile with updated position`() {
        val originalPosition = Position(1, 1)
        val newPosition = Position(2, 2)
        val tile = Tile(27, originalPosition, "test-id")
        
        val updatedTile = tile.withPosition(newPosition)
        
        assertEquals(27, updatedTile.value)
        assertEquals(newPosition, updatedTile.position)
        assertEquals("test-id", updatedTile.id)
        assertNotSame(tile, updatedTile)
    }

    @Test
    fun `canMergeWith returns true for same value tiles`() {
        val tile1 = Tile(3, Position(1, 1))
        val tile2 = Tile(3, Position(1, 2))
        
        assertTrue(tile1.canMergeWith(tile2))
        assertTrue(tile2.canMergeWith(tile1))
    }

    @Test
    fun `canMergeWith returns false for different value tiles`() {
        val tile1 = Tile(3, Position(1, 1))
        val tile2 = Tile(9, Position(1, 2))
        
        assertFalse(tile1.canMergeWith(tile2))
        assertFalse(tile2.canMergeWith(tile1))
    }

    @Test
    fun `merge creates new tile with tripled value`() {
        val tile = Tile(3, Position(1, 1), "test-id")
        val mergedTile = tile.merge()
        
        assertEquals(9, mergedTile.value)
        assertEquals(Position(1, 1), mergedTile.position)
        assertEquals("test-id", mergedTile.id)
        assertNotSame(tile, mergedTile)
    }

    @Test
    fun `merge works with different values`() {
        val tile1 = Tile(1, Position(0, 0))
        val tile27 = Tile(27, Position(2, 2))
        val tile243 = Tile(243, Position(3, 3))
        
        assertEquals(3, tile1.merge().value)
        assertEquals(81, tile27.merge().value)
        assertEquals(729, tile243.merge().value)
    }
}