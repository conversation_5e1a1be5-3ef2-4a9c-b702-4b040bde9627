package com.frageo.triswipe.data.models

import org.junit.Test
import org.junit.Assert.*
import org.junit.Before

class GameBoardTest {
    
    private lateinit var gameBoard: GameBoard
    
    @Before
    fun setUp() {
        gameBoard = GameBoard()
    }
    
    @Test
    fun `empty board returns null for all positions`() {
        for (row in 0 until GameConfig.BOARD_SIZE) {
            for (col in 0 until GameConfig.BOARD_SIZE) {
                val position = Position(row, col)
                assertNull(gameBoard.getTile(position))
                assertTrue(gameBoard.isEmpty(position))
            }
        }
    }
    
    @Test
    fun `setTile and getTile work correctly`() {
        val position = Position(1, 1)
        val tile = Tile(3, position)
        
        gameBoard.setTile(position, tile)
        
        assertEquals(tile, gameBoard.getTile(position))
        assertFalse(gameBoard.isEmpty(position))
    }
    
    @Test
    fun `invalid positions return null`() {
        val invalidPositions = listOf(
            Position(-1, 0),
            Position(0, -1),
            Position(4, 0),
            Position(0, 4),
            Position(4, 4)
        )
        
        for (position in invalidPositions) {
            assertNull(gameBoard.getTile(position))
        }
    }
    
    @Test
    fun `getEmptyPositions returns all positions when board is empty`() {
        val emptyPositions = gameBoard.getEmptyPositions()
        assertEquals(16, emptyPositions.size)
    }
    
    @Test
    fun `getEmptyPositions returns correct count after placing tiles`() {
        gameBoard.setTile(Position(0, 0), Tile(1, Position(0, 0)))
        gameBoard.setTile(Position(1, 1), Tile(3, Position(1, 1)))
        
        val emptyPositions = gameBoard.getEmptyPositions()
        assertEquals(14, emptyPositions.size)
    }
    
    @Test
    fun `hasEmptySpaces returns true for empty board`() {
        assertTrue(gameBoard.hasEmptySpaces())
    }
    
    @Test
    fun `hasEmptySpaces returns false for full board`() {
        for (row in 0 until GameConfig.BOARD_SIZE) {
            for (col in 0 until GameConfig.BOARD_SIZE) {
                val position = Position(row, col)
                gameBoard.setTile(position, Tile(1, position))
            }
        }
        
        assertFalse(gameBoard.hasEmptySpaces())
    }
    
    @Test
    fun `getAllTiles returns empty list for empty board`() {
        val tiles = gameBoard.getAllTiles()
        assertTrue(tiles.isEmpty())
    }
    
    @Test
    fun `getAllTiles returns all tiles when board has tiles`() {
        gameBoard.setTile(Position(0, 0), Tile(1, Position(0, 0)))
        gameBoard.setTile(Position(1, 1), Tile(3, Position(1, 1)))
        gameBoard.setTile(Position(2, 2), Tile(9, Position(2, 2)))
        
        val tiles = gameBoard.getAllTiles()
        assertEquals(3, tiles.size)
    }
    
    @Test
    fun `spawnRandomTile creates tile on empty board`() {
        val spawnedTile = gameBoard.spawnRandomTile()
        
        assertNotNull(spawnedTile)
        assertTrue(GameConfig.SPAWN_TILE_VALUES.contains(spawnedTile!!.value))
        assertEquals(spawnedTile, gameBoard.getTile(spawnedTile.position))
    }
    
    @Test
    fun `spawnRandomTile returns null on full board`() {
        for (row in 0 until GameConfig.BOARD_SIZE) {
            for (col in 0 until GameConfig.BOARD_SIZE) {
                val position = Position(row, col)
                gameBoard.setTile(position, Tile(1, position))
            }
        }
        
        val spawnedTile = gameBoard.spawnRandomTile()
        assertNull(spawnedTile)
    }
    
    @Test
    fun `initializeBoard creates initial tiles`() {
        gameBoard.initializeBoard()
        
        val tiles = gameBoard.getAllTiles()
        assertEquals(GameConfig.INITIAL_TILES_COUNT, tiles.size)
        
        for (tile in tiles) {
            assertTrue(GameConfig.SPAWN_TILE_VALUES.contains(tile.value))
        }
    }
    
    @Test
    fun `clearBoard removes all tiles`() {
        gameBoard.initializeBoard()
        assertTrue(gameBoard.getAllTiles().isNotEmpty())
        
        gameBoard.clearBoard()
        assertTrue(gameBoard.getAllTiles().isEmpty())
    }
    
    @Test
    fun `swapTiles works for adjacent positions`() {
        val pos1 = Position(0, 0)
        val pos2 = Position(0, 1)
        val tile1 = Tile(1, pos1)
        val tile2 = Tile(3, pos2)
        
        gameBoard.setTile(pos1, tile1)
        gameBoard.setTile(pos2, tile2)
        
        val swapResult = gameBoard.swapTiles(pos1, pos2)
        
        assertTrue(swapResult)
        assertEquals(3, gameBoard.getTile(pos1)?.value)
        assertEquals(1, gameBoard.getTile(pos2)?.value)
    }
    
    @Test
    fun `swapTiles fails for non-adjacent positions`() {
        val pos1 = Position(0, 0)
        val pos2 = Position(2, 2)
        val tile1 = Tile(1, pos1)
        val tile2 = Tile(3, pos2)
        
        gameBoard.setTile(pos1, tile1)
        gameBoard.setTile(pos2, tile2)
        
        val swapResult = gameBoard.swapTiles(pos1, pos2)
        
        assertFalse(swapResult)
        assertEquals(1, gameBoard.getTile(pos1)?.value)
        assertEquals(3, gameBoard.getTile(pos2)?.value)
    }
    
    @Test
    fun `swapTiles works with empty positions`() {
        val pos1 = Position(0, 0)
        val pos2 = Position(0, 1)
        val tile1 = Tile(1, pos1)
        
        gameBoard.setTile(pos1, tile1)
        
        val swapResult = gameBoard.swapTiles(pos1, pos2)
        
        assertTrue(swapResult)
        assertNull(gameBoard.getTile(pos1))
        assertEquals(1, gameBoard.getTile(pos2)?.value)
    }
    
    @Test
    fun `moveTiles left moves tiles correctly`() {
        gameBoard.setTile(Position(0, 1), Tile(1, Position(0, 1)))
        gameBoard.setTile(Position(0, 3), Tile(3, Position(0, 3)))
        
        val movements = gameBoard.moveTiles(Direction.LEFT)
        
        assertEquals(2, movements.size)
        assertNotNull(gameBoard.getTile(Position(0, 0)))
        assertNotNull(gameBoard.getTile(Position(0, 1)))
        assertNull(gameBoard.getTile(Position(0, 2)))
        assertNull(gameBoard.getTile(Position(0, 3)))
    }
    
    @Test
    fun `moveTiles right moves tiles correctly`() {
        gameBoard.setTile(Position(0, 0), Tile(1, Position(0, 0)))
        gameBoard.setTile(Position(0, 2), Tile(3, Position(0, 2)))
        
        val movements = gameBoard.moveTiles(Direction.RIGHT)
        
        assertEquals(2, movements.size)
        assertNull(gameBoard.getTile(Position(0, 0)))
        assertNull(gameBoard.getTile(Position(0, 1)))
        assertNotNull(gameBoard.getTile(Position(0, 2)))
        assertNotNull(gameBoard.getTile(Position(0, 3)))
    }
}