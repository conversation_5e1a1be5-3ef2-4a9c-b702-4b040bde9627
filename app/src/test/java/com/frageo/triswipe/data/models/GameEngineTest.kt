package com.frageo.triswipe.data.models

import com.frageo.triswipe.data.models.managers.UndoManager
import com.frageo.triswipe.data.models.managers.MoveAnalyzer
import com.frageo.triswipe.data.models.managers.GameValidator
import com.frageo.triswipe.data.models.managers.MoveExecutor
import com.frageo.triswipe.data.repository.GameStatisticsManager
import org.junit.Assert.assertEquals
import org.junit.Assert.assertFalse
import org.junit.Before
import org.junit.Test
import org.mockito.Mock
import org.mockito.Mockito.verify
import org.mockito.Mockito.never
import org.mockito.MockitoAnnotations

class GameEngineTest {

    @Mock
    private lateinit var undoManager: UndoManager

    @Mock
    private lateinit var moveAnalyzer: MoveAnalyzer

    @Mock
    private lateinit var gameValidator: GameValidator

    @Mock
    private lateinit var moveExecutor: MoveExecutor

    @Mock
    private lateinit var mergeDetector: MergeDetector

    @Mock
    private lateinit var mergeExecutor: MergeExecutor

    @Mock
    private lateinit var statisticsManager: GameStatisticsManager

    private lateinit var gameEngine: GameEngine

    @Before
    fun setUp() {
        MockitoAnnotations.openMocks(this)
        val mockSwapModeManager = org.mockito.kotlin.mock<com.frageo.triswipe.data.models.managers.SwapModeManager>()
        gameEngine = GameEngine(
            undoManager,
            moveAnalyzer,
            gameValidator,
            moveExecutor,
            mergeDetector,
            mergeExecutor,
            statisticsManager,
            mockSwapModeManager
        )
    }

    @Test
    fun `initializeGame should initialize game state without recording start`() {
        val gameState = gameEngine.initializeGame()
        
        // Verify game state is properly initialized
        assertFalse(gameState.isGameOver)
        assertFalse(gameState.hasWon)
        assertEquals(0, gameState.score)
        assertEquals(0, gameState.moves)
        
        // Verify that recordGameStart is not called at the engine level
        // (it should be called at the ViewModel level instead)
        verify(statisticsManager, never()).recordGameStart()
    }
}