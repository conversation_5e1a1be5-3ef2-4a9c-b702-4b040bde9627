package com.frageo.triswipe.data.events

import com.frageo.triswipe.data.models.GameState
import com.frageo.triswipe.data.models.GameStats
import com.frageo.triswipe.data.models.Tile
import com.frageo.triswipe.data.models.Position
import com.frageo.triswipe.data.repository.GameStatisticsRepository
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.delay
import org.junit.Test
import org.junit.Assert.*
import org.junit.Before
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.any

class StatisticsEventProcessorTest {
    
    @Mock
    private lateinit var statisticsRepository: GameStatisticsRepository
    
    private lateinit var testScope: CoroutineScope
    private lateinit var eventProcessor: StatisticsEventProcessor
    
    @Before
    fun setUp() {
        MockitoAnnotations.openMocks(this)
        testScope = CoroutineScope(SupervisorJob())
        
        // Mock repository to return default stats
        runBlocking {
            doReturn(GameStats()).`when`(statisticsRepository).loadGameStats()
        }
        
        eventProcessor = StatisticsEventProcessor(statisticsRepository, testScope)
    }
    
    @Test
    fun `merge events update live statistics immediately`() = runTest {
        // Given: Initial state
        delay(100) // Allow initialization to complete
        val initialStats = eventProcessor.currentStats.first()
        assertEquals(0, initialStats.totalMerges)
        assertEquals(0, initialStats.bestTile)
        
        // When: Process merge event
        eventProcessor.processEvent(StatisticsEvent.MergePerformed(27))
        
        // Then: In-memory state updated immediately
        val updatedStats = eventProcessor.currentStats.first()
        assertEquals(1, updatedStats.totalMerges)
        assertEquals(27, updatedStats.bestTile)
    }
    
    @Test
    fun `game start events update stats immediately`() = runTest {
        // Given: Initial state
        delay(100) // Allow initialization to complete
        val initialStats = eventProcessor.currentStats.first()
        assertEquals(0, initialStats.gamesPlayed)
        
        // When: Process game start event
        eventProcessor.processEvent(StatisticsEvent.GameStarted)
        
        // Then: In-memory state updated immediately
        val updatedStats = eventProcessor.currentStats.first()
        assertEquals(1, updatedStats.gamesPlayed)
    }
    
    @Test
    fun `event validation rejects invalid merge values`() = runTest {
        // Given: Initial state
        delay(100) // Allow initialization to complete
        val initialStats = eventProcessor.currentStats.first()
        
        // When: Process invalid merge events
        eventProcessor.processEvent(StatisticsEvent.MergePerformed(-10)) // Negative value
        eventProcessor.processEvent(StatisticsEvent.MergePerformed(0))   // Zero value
        eventProcessor.processEvent(StatisticsEvent.MergePerformed(5000)) // Too high value
        
        // Then: In-memory stats should not be updated
        val stats = eventProcessor.currentStats.first()
        assertEquals(initialStats.totalMerges, stats.totalMerges)
        assertEquals(initialStats.bestTile, stats.bestTile)
    }
    
    @Test
    fun `event validation accepts valid merge values`() = runTest {
        // Given: Initial state
        delay(100) // Allow initialization to complete
        
        // When: Process valid merge events
        eventProcessor.processEvent(StatisticsEvent.MergePerformed(1))    // Min valid
        eventProcessor.processEvent(StatisticsEvent.MergePerformed(243))  // Mid valid
        eventProcessor.processEvent(StatisticsEvent.MergePerformed(2187)) // Max valid
        
        // Then: In-memory stats should be updated
        val stats = eventProcessor.currentStats.first()
        assertEquals(3, stats.totalMerges)
        assertEquals(2187, stats.bestTile)
    }
    
    @Test
    fun `multiple merge events update best tile correctly`() = runTest {
        // Given: Initial state
        delay(100) // Allow initialization to complete
        
        // When: Process merge events with different tile values
        eventProcessor.processEvent(StatisticsEvent.MergePerformed(9))
        eventProcessor.processEvent(StatisticsEvent.MergePerformed(27))
        eventProcessor.processEvent(StatisticsEvent.MergePerformed(3))  // Lower value
        eventProcessor.processEvent(StatisticsEvent.MergePerformed(81)) // Higher value
        
        // Then: Best tile should be the highest value processed
        val stats = eventProcessor.currentStats.first()
        assertEquals(4, stats.totalMerges)
        assertEquals(81, stats.bestTile)
    }
    
    @Test
    fun `game completion event is processed without errors`() = runTest {
        // Given: Game state
        val gameState = GameState(
            board = createBoardWithTiles(arrayOf(81, 27, 9)),
            score = 500,
            moves = 25,
            isGameOver = true,
            hasWon = true,
            lastMoveType = "SWIPE"
        )
        
        delay(100) // Allow initialization to complete
        
        // When: Process game completion event (should not throw exception)
        assertDoesNotThrow {
            eventProcessor.processEvent(
                StatisticsEvent.GameCompleted(gameState, 120000L, 8)
            )
        }
    }
    
    @Test
    fun `premium status change event is processed without errors`() = runTest {
        delay(100) // Allow initialization to complete
        
        // When: Process premium status change event (should not throw exception)
        assertDoesNotThrow {
            eventProcessor.processEvent(StatisticsEvent.PremiumStatusChanged(true))
        }
    }
    
    @Test
    fun `performance metrics track event processing`() = runTest {
        // Given: Initial state
        delay(100) // Allow initialization to complete
        
        // When: Process various events
        eventProcessor.processEvent(StatisticsEvent.GameStarted)
        eventProcessor.processEvent(StatisticsEvent.MergePerformed(27))
        eventProcessor.processEvent(StatisticsEvent.MergePerformed(81))
        
        delay(50) // Allow events to be processed
        
        // Then: Performance metrics should reflect processing
        val metrics = eventProcessor.getPerformanceMetrics()
        
        assertEquals(3L, metrics["totalEventsProcessed"])
        assertEquals(2L, metrics["totalMergeEventsProcessed"])
        assertTrue((metrics["currentBatchSize"] as Int) >= 0)
        assertTrue((metrics["timeSinceLastFlush"] as Long) >= 0)
    }
    
    @Test
    fun `error handling does not crash on problematic events`() = runTest {
        delay(100) // Allow initialization to complete
        
        // When: Process various problematic events (should not throw exceptions)
        assertDoesNotThrow {
            eventProcessor.processEvent(StatisticsEvent.MergePerformed(-1))
            
            val invalidGameState = GameState(
                board = createEmptyBoard(),
                score = -100, // Invalid score
                moves = -5,   // Invalid moves  
                isGameOver = true,
                hasWon = false,
                lastMoveType = "INVALID"
            )
            eventProcessor.processEvent(
                StatisticsEvent.GameCompleted(invalidGameState, -1000L, -5)
            )
        }
        
        // Then: Processor should still be functional
        val stats = eventProcessor.currentStats.first()
        assertNotNull(stats)
        
        // And: Valid events should still work
        eventProcessor.processEvent(StatisticsEvent.MergePerformed(27))
        val updatedStats = eventProcessor.currentStats.first()
        assertEquals(1, updatedStats.totalMerges)
    }
    
    // Helper methods
    private fun createBoardWithTiles(values: Array<Int>): Array<Array<Tile?>> {
        val board = Array(4) { Array<Tile?>(4) { null } }
        
        var index = 0
        for (row in 0 until 4) {
            for (col in 0 until 4) {
                if (index < values.size) {
                    board[row][col] = Tile(values[index], Position(row, col))
                    index++
                }
            }
        }
        
        return board
    }
    
    private fun createEmptyBoard(): Array<Array<Tile?>> {
        return Array(4) { Array<Tile?>(4) { null } }
    }
    
    // Helper method for assertDoesNotThrow (if not available in test framework)
    private fun assertDoesNotThrow(block: () -> Unit) {
        try {
            block()
        } catch (e: Exception) {
            fail("Expected no exception but got: ${e.message}")
        }
    }
}