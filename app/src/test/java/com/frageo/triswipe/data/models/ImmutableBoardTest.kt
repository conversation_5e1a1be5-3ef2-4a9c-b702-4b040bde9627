package com.frageo.triswipe.data.models

import org.junit.Assert.*
import org.junit.Before
import org.junit.Test

class ImmutableBoardTest {
    
    private lateinit var emptyBoard: ImmutableBoard
    private lateinit var testTile1: Tile
    private lateinit var testTile2: Tile
    private lateinit var testPosition1: Position
    private lateinit var testPosition2: Position
    
    @Before
    fun setUp() {
        emptyBoard = ImmutableBoard.empty()
        testPosition1 = Position(0, 0)
        testPosition2 = Position(1, 1)
        testTile1 = Tile(value = 2, position = testPosition1, origin = TileOrigin.EXISTING)
        testTile2 = Tile(value = 4, position = testPosition2, origin = TileOrigin.SPAWN)
    }
    
    @Test
    fun `empty board should have no tiles`() {
        // Given & When
        val board = ImmutableBoard.empty()
        
        // Then
        assertEquals("Empty board should have 0 tiles", 0, board.getTileCount())
        assertEquals("Empty board should have 16 empty spaces", 16, board.getEmptySpaceCount())
        assertTrue("Empty board should have empty spaces", board.hasEmptySpaces())
        assertTrue("Empty board should validate", board.validateBoardState().isValid)
    }
    
    @Test
    fun `setTile should create new board with tile added`() {
        // Given
        val originalBoard = ImmutableBoard.empty()
        
        // When
        val newBoard = originalBoard.setTile(testPosition1, testTile1)
        
        // Then
        assertNotSame("Should create new board instance", originalBoard, newBoard)
        assertEquals("Original board should remain unchanged", 0, originalBoard.getTileCount())
        assertEquals("New board should have 1 tile", 1, newBoard.getTileCount())
        assertEquals("New board should have tile at position", testTile1.value, newBoard.getTile(testPosition1)?.value)
    }
    
    @Test
    fun `setTile with null should remove tile`() {
        // Given
        val boardWithTile = emptyBoard.setTile(testPosition1, testTile1)
        
        // When
        val boardWithoutTile = boardWithTile.setTile(testPosition1, null)
        
        // Then
        assertEquals("Board should have 1 tile initially", 1, boardWithTile.getTileCount())
        assertEquals("Board should have 0 tiles after removal", 0, boardWithoutTile.getTileCount())
        assertNull("Position should be empty after removal", boardWithoutTile.getTile(testPosition1))
    }
    
    @Test
    fun `setTiles should update multiple positions efficiently`() {
        // Given
        val updates = mapOf(
            testPosition1 to testTile1,
            testPosition2 to testTile2,
            Position(2, 2) to null // Remove tile (if any)
        )
        
        // When
        val newBoard = emptyBoard.setTiles(updates)
        
        // Then
        assertEquals("Board should have 2 tiles", 2, newBoard.getTileCount())
        assertEquals("Tile 1 should be at correct position", testTile1.value, newBoard.getTile(testPosition1)?.value)
        assertEquals("Tile 2 should be at correct position", testTile2.value, newBoard.getTile(testPosition2)?.value)
    }
    
    @Test
    fun `setTiles with no changes should return same instance`() {
        // Given
        val boardWithTiles = emptyBoard.setTiles(mapOf(testPosition1 to testTile1))
        val noChanges = mapOf(testPosition1 to testTile1) // Same tile
        
        // When
        val resultBoard = boardWithTiles.setTiles(noChanges)
        
        // Then
        assertSame("Should return same instance when no changes", boardWithTiles, resultBoard)
    }
    
    @Test
    fun `fromArray should create board from 2D array`() {
        // Given
        val array = Array(4) { Array<Tile?>(4) { null } }
        array[0][0] = testTile1
        array[1][1] = testTile2
        
        // When
        val board = ImmutableBoard.fromArray(array)
        
        // Then
        assertEquals("Board should have 2 tiles", 2, board.getTileCount())
        assertEquals("Tile 1 should be present", testTile1.value, board.getTile(Position(0, 0))?.value)
        assertEquals("Tile 2 should be present", testTile2.value, board.getTile(Position(1, 1))?.value)
    }
    
    @Test
    fun `toArray should convert board to 2D array`() {
        // Given
        val board = emptyBoard
            .setTile(testPosition1, testTile1)
            .setTile(testPosition2, testTile2)
        
        // When
        val array = board.toArray()
        
        // Then
        assertEquals("Array should have correct dimensions", 4, array.size)
        assertEquals("Array should have correct dimensions", 4, array[0].size)
        assertEquals("Tile 1 should be in array", testTile1.value, array[0][0]?.value)
        assertEquals("Tile 2 should be in array", testTile2.value, array[1][1]?.value)
        assertNull("Empty positions should be null", array[2][2])
    }
    
    @Test
    fun `getEmptyPositions should return all empty positions`() {
        // Given
        val board = emptyBoard.setTile(testPosition1, testTile1)
        
        // When
        val emptyPositions = board.getEmptyPositions()
        
        // Then
        assertEquals("Should have 15 empty positions", 15, emptyPositions.size)
        assertFalse("Should not include occupied position", emptyPositions.contains(testPosition1))
    }
    
    @Test
    fun `getAllTiles should return all tiles on board`() {
        // Given
        val board = emptyBoard
            .setTile(testPosition1, testTile1)
            .setTile(testPosition2, testTile2)
        
        // When
        val allTiles = board.getAllTiles()
        
        // Then
        assertEquals("Should have 2 tiles", 2, allTiles.size)
        assertTrue("Should contain tile 1", allTiles.any { it.value == testTile1.value })
        assertTrue("Should contain tile 2", allTiles.any { it.value == testTile2.value })
    }
    
    @Test
    fun `spawnRandomTile should add tile to empty position`() {
        // Given
        val board = emptyBoard
        
        // When
        val newBoard = board.spawnRandomTile()
        
        // Then
        assertEquals("Should have 1 tile after spawn", 1, newBoard.getTileCount())
        assertEquals("Should have 15 empty spaces after spawn", 15, newBoard.getEmptySpaceCount())
        
        val spawnedTile = newBoard.getAllTiles().first()
        assertTrue("Spawned tile should have valid value", spawnedTile.value in GameConfig.SPAWN_TILE_VALUES)
        assertEquals("Spawned tile should have SPAWN origin", TileOrigin.SPAWN, spawnedTile.origin)
    }
    
    @Test
    fun `spawnRandomTile on full board should return same board`() {
        // Given - Create a full board
        var board = emptyBoard
        for (row in 0 until 4) {
            for (col in 0 until 4) {
                val tile = Tile(value = 2, position = Position(row, col), origin = TileOrigin.EXISTING)
                board = board.setTile(Position(row, col), tile)
            }
        }
        
        // When
        val newBoard = board.spawnRandomTile()
        
        // Then
        assertSame("Should return same board when full", board, newBoard)
    }
    
    @Test
    fun `moveTiles should move tiles in specified direction`() {
        // Given
        val board = emptyBoard
            .setTile(Position(1, 0), Tile(2, Position(1, 0), origin = TileOrigin.EXISTING))
            .setTile(Position(3, 0), Tile(4, Position(3, 0), origin = TileOrigin.EXISTING))
        
        // When
        val (newBoard, movements) = board.moveTiles(Direction.UP)
        
        // Then
        assertEquals("Should have 2 tiles after move", 2, newBoard.getTileCount())
        assertNotNull("Tile should be at top", newBoard.getTile(Position(0, 0)))
        assertNotNull("Tile should be at second position", newBoard.getTile(Position(1, 0)))
        assertNull("Original positions should be empty", newBoard.getTile(Position(3, 0)))
        
        assertEquals("Should have 2 movements", 2, movements.size)
    }
    
    @Test
    fun `swapTiles should swap adjacent tiles`() {
        // Given
        val pos1 = Position(0, 0)
        val pos2 = Position(0, 1)
        val tile1 = Tile(2, pos1, origin = TileOrigin.EXISTING)
        val tile2 = Tile(4, pos2, origin = TileOrigin.EXISTING)
        
        val board = emptyBoard
            .setTile(pos1, tile1)
            .setTile(pos2, tile2)
        
        // When
        val newBoard = board.swapTiles(pos1, pos2)
        
        // Then
        assertEquals("Tile 1 should be at position 2", tile1.value, newBoard.getTile(pos2)?.value)
        assertEquals("Tile 2 should be at position 1", tile2.value, newBoard.getTile(pos1)?.value)
    }
    
    @Test
    fun `swapTiles with non-adjacent positions should return same board`() {
        // Given
        val pos1 = Position(0, 0)
        val pos2 = Position(2, 2)
        val board = emptyBoard.setTile(pos1, testTile1)
        
        // When
        val newBoard = board.swapTiles(pos1, pos2)
        
        // Then
        assertSame("Should return same board for non-adjacent positions", board, newBoard)
    }
    
    @Test
    fun `hasSameStructure should work correctly`() {
        // Given
        val board1 = emptyBoard.setTile(testPosition1, testTile1)
        val board2 = emptyBoard.setTile(testPosition1, testTile1)
        val board3 = emptyBoard.setTile(testPosition2, testTile1)
        
        // When & Then
        assertTrue("Boards with same structure should match", board1.hasSameStructure(board2))
        assertFalse("Boards with different structure should not match", board1.hasSameStructure(board3))
        assertFalse("Empty and non-empty boards should not match", emptyBoard.hasSameStructure(board1))
    }
    
    @Test
    fun `validateBoardState should detect errors`() {
        // Given - Create board with invalid tile value (not in GameConfig.TILE_VALUES)
        val invalidTile = Tile(5, Position(0, 0), origin = TileOrigin.EXISTING)  // 5 is not a valid tile value
        val board = emptyBoard.setTile(Position(0, 0), invalidTile)
        
        // When
        val validation = board.validateBoardState()
        
        // Then
        assertFalse("Board should be invalid", validation.isValid)
        assertFalse("Should have errors", validation.errors.isEmpty())
        assertTrue("Should detect invalid tile value", 
            validation.errors.any { it.contains("Invalid tile value") })
    }
    
    @Test
    fun `canPerformMove should detect valid moves`() {
        // Given
        val board = emptyBoard
            .setTile(Position(1, 0), Tile(2, Position(1, 0)))
            .setTile(Position(3, 0), Tile(4, Position(3, 0)))
        
        // When & Then
        assertTrue("Should be able to move up", board.canPerformMove(Direction.UP))
        assertTrue("Should be able to move down", board.canPerformMove(Direction.DOWN))
        assertFalse("Should not be able to move left (already at leftmost column)", board.canPerformMove(Direction.LEFT))
        assertTrue("Should be able to move right (can move to rightmost column)", board.canPerformMove(Direction.RIGHT))
    }
    
    @Test
    fun `equals should work correctly with structural sharing`() {
        // Given
        val board1 = emptyBoard.setTile(testPosition1, testTile1)
        val board2 = emptyBoard.setTile(testPosition1, testTile1)
        val board3 = emptyBoard.setTile(testPosition1, testTile2)
        
        // When & Then
        assertEquals("Boards with same tiles should be equal", board1, board2)
        assertNotEquals("Boards with different tiles should not be equal", board1, board3)
        assertEquals("Equal boards should have same hash code", board1.hashCode(), board2.hashCode())
    }
    
    @Test
    fun `initialized board should have initial tiles`() {
        // When
        val board = ImmutableBoard.initialized()
        
        // Then
        assertEquals("Should have initial tiles", GameConfig.INITIAL_TILES_COUNT, board.getTileCount())
        assertTrue("Should have valid state", board.validateBoardState().isValid)
        
        val allTiles = board.getAllTiles()
        assertTrue("All tiles should have spawn origin", 
            allTiles.all { it.origin == TileOrigin.SPAWN })
        assertTrue("All tiles should have valid spawn values",
            allTiles.all { it.value in GameConfig.SPAWN_TILE_VALUES })
    }
}