package com.frageo.triswipe.data.models

import org.junit.Test
import org.junit.Assert.*
import org.junit.Before

class MergeDetectorTest {
    
    private lateinit var mergeDetector: MergeDetector
    private lateinit var gameBoard: GameBoard
    
    @Before
    fun setUp() {
        mergeDetector = MergeDetector()
        gameBoard = GameBoard()
    }
    
    @Test
    fun `findMergeGroups returns empty list for empty board`() {
        val mergeGroups = mergeDetector.findMergeGroups(gameBoard)
        assertTrue(mergeGroups.isEmpty())
    }
    
    @Test
    fun `findMergeGroups returns empty list for insufficient tiles`() {
        gameBoard.setTile(Position(0, 0), Tile(1, Position(0, 0)))
        gameBoard.setTile(Position(0, 1), Tile(1, Position(0, 1)))
        
        val mergeGroups = mergeDetector.findMergeGroups(gameBoard)
        assertTrue(mergeGroups.isEmpty())
    }
    
    @Test
    fun `findMergeGroups detects horizontal merge group`() {
        gameBoard.setTile(Position(0, 0), Tile(1, Position(0, 0)))
        gameBoard.setTile(Position(0, 1), Tile(1, Position(0, 1)))
        gameBoard.setTile(Position(0, 2), Tile(1, Position(0, 2)))
        
        val mergeGroups = mergeDetector.findMergeGroups(gameBoard)
        
        assertEquals(1, mergeGroups.size)
        assertEquals(3, mergeGroups[0].positions.size)
        assertEquals(1, mergeGroups[0].value)
    }
    
    @Test
    fun `findMergeGroups detects vertical merge group`() {
        gameBoard.setTile(Position(0, 0), Tile(3, Position(0, 0)))
        gameBoard.setTile(Position(1, 0), Tile(3, Position(1, 0)))
        gameBoard.setTile(Position(2, 0), Tile(3, Position(2, 0)))
        
        val mergeGroups = mergeDetector.findMergeGroups(gameBoard)
        
        assertEquals(1, mergeGroups.size)
        assertEquals(3, mergeGroups[0].positions.size)
        assertEquals(3, mergeGroups[0].value)
    }
    
    @Test
    fun `findMergeGroups detects L-shaped merge group`() {
        gameBoard.setTile(Position(0, 0), Tile(9, Position(0, 0)))
        gameBoard.setTile(Position(0, 1), Tile(9, Position(0, 1)))
        gameBoard.setTile(Position(1, 0), Tile(9, Position(1, 0)))
        
        val mergeGroups = mergeDetector.findMergeGroups(gameBoard)
        
        assertEquals(1, mergeGroups.size)
        assertEquals(3, mergeGroups[0].positions.size)
        assertEquals(9, mergeGroups[0].value)
    }
    
    @Test
    fun `findMergeGroups breaks larger group into triple groups`() {
        gameBoard.setTile(Position(0, 0), Tile(1, Position(0, 0)))
        gameBoard.setTile(Position(0, 1), Tile(1, Position(0, 1)))
        gameBoard.setTile(Position(0, 2), Tile(1, Position(0, 2)))
        gameBoard.setTile(Position(0, 3), Tile(1, Position(0, 3)))
        
        val mergeGroups = mergeDetector.findMergeGroups(gameBoard)
        
        // Should break 4-tile group into one 3-tile group (triple merge game mechanics)
        assertEquals(1, mergeGroups.size)
        assertEquals(3, mergeGroups[0].positions.size)
        assertEquals(1, mergeGroups[0].value)
    }
    
    @Test
    fun `findMergeGroups detects multiple separate groups`() {
        gameBoard.setTile(Position(0, 0), Tile(1, Position(0, 0)))
        gameBoard.setTile(Position(0, 1), Tile(1, Position(0, 1)))
        gameBoard.setTile(Position(0, 2), Tile(1, Position(0, 2)))
        
        gameBoard.setTile(Position(2, 0), Tile(3, Position(2, 0)))
        gameBoard.setTile(Position(2, 1), Tile(3, Position(2, 1)))
        gameBoard.setTile(Position(2, 2), Tile(3, Position(2, 2)))
        
        val mergeGroups = mergeDetector.findMergeGroups(gameBoard)
        
        assertEquals(2, mergeGroups.size)
        assertTrue(mergeGroups.any { it.value == 1 && it.positions.size == 3 })
        assertTrue(mergeGroups.any { it.value == 3 && it.positions.size == 3 })
    }
    
    @Test
    fun `hasValidMerges returns true when merges exist`() {
        gameBoard.setTile(Position(0, 0), Tile(1, Position(0, 0)))
        gameBoard.setTile(Position(0, 1), Tile(1, Position(0, 1)))
        gameBoard.setTile(Position(0, 2), Tile(1, Position(0, 2)))
        
        assertTrue(mergeDetector.hasValidMerges(gameBoard))
    }
    
    @Test
    fun `hasValidMerges returns false when no merges exist`() {
        gameBoard.setTile(Position(0, 0), Tile(1, Position(0, 0)))
        gameBoard.setTile(Position(0, 1), Tile(3, Position(0, 1)))
        gameBoard.setTile(Position(0, 2), Tile(9, Position(0, 2)))
        
        assertFalse(mergeDetector.hasValidMerges(gameBoard))
    }
    
    @Test
    fun `findBestMergeGroup returns group with highest value`() {
        gameBoard.setTile(Position(0, 0), Tile(1, Position(0, 0)))
        gameBoard.setTile(Position(0, 1), Tile(1, Position(0, 1)))
        gameBoard.setTile(Position(0, 2), Tile(1, Position(0, 2)))
        
        gameBoard.setTile(Position(2, 0), Tile(27, Position(2, 0)))
        gameBoard.setTile(Position(2, 1), Tile(27, Position(2, 1)))
        gameBoard.setTile(Position(2, 2), Tile(27, Position(2, 2)))
        
        val bestGroup = mergeDetector.findBestMergeGroup(gameBoard)
        
        assertNotNull(bestGroup)
        assertEquals(27, bestGroup!!.value)
    }
    
    @Test
    fun `findBestMergeGroup returns null when no merges exist`() {
        gameBoard.setTile(Position(0, 0), Tile(1, Position(0, 0)))
        gameBoard.setTile(Position(0, 1), Tile(3, Position(0, 1)))
        
        val bestGroup = mergeDetector.findBestMergeGroup(gameBoard)
        assertNull(bestGroup)
    }
    
    @Test
    fun `getMergeScore calculates correct score`() {
        val mergeGroup = MergeGroup(
            positions = listOf(Position(0, 0), Position(0, 1), Position(0, 2)),
            value = 3
        )
        
        val score = mergeDetector.getMergeScore(mergeGroup)
        
        val expectedNewValue = GameConfig.getNextTileValue(3)
        val expectedScore = GameConfig.calculateScore(expectedNewValue, 3)
        assertEquals(expectedScore, score)
    }
    
    @Test
    fun `mergeGroup getCenterPosition returns correct position`() {
        val positions = listOf(Position(0, 0), Position(0, 1), Position(0, 2))
        val mergeGroup = MergeGroup(positions, 1)
        
        val centerPosition = mergeGroup.getCenterPosition()
        
        assertEquals(Position(0, 1), centerPosition)
    }
    
    @Test
    fun `mergeGroup getCenterPosition handles L-shape correctly`() {
        val positions = listOf(Position(0, 0), Position(0, 1), Position(1, 0))
        val mergeGroup = MergeGroup(positions, 1)
        
        val centerPosition = mergeGroup.getCenterPosition()
        
        assertEquals(Position(0, 0), centerPosition)
    }
    
    // ===== Enhanced Triple Grouping Algorithm Tests =====
    
    @Test
    fun `4-tile L-shape selects optimal center position over sequential grouping`() {
        // Create an L-shape where optimal grouping should prefer center positions
        // Pattern:
        //   A B
        //   C D
        val positions = listOf(
            Position(1, 1), // A - center position (high value)
            Position(1, 2), // B - center position (high value)  
            Position(2, 1), // C - center position (high value)
            Position(2, 2)  // D - center position (high value)
        )
        
        gameBoard.setTile(positions[0], Tile(27, positions[0]))
        gameBoard.setTile(positions[1], Tile(27, positions[1]))
        gameBoard.setTile(positions[2], Tile(27, positions[2]))
        gameBoard.setTile(positions[3], Tile(27, positions[3]))
        
        val mergeGroups = mergeDetector.findMergeGroups(gameBoard)
        
        assertEquals(1, mergeGroups.size)
        assertEquals(3, mergeGroups[0].positions.size)
        assertEquals(27, mergeGroups[0].value)
        
        // The algorithm should have selected a strategic combination
        // All positions are center positions, so any combination should be good
        assertTrue("Should select 3 positions from the 4-tile group", 
                  mergeGroups[0].positions.all { it in positions })
    }
    
    @Test
    fun `6-tile group creates two optimal triples maximizing strategic value`() {
        // Create a 2x3 rectangle of tiles
        val positions = listOf(
            Position(1, 1), Position(1, 2), Position(1, 3),
            Position(2, 1), Position(2, 2), Position(2, 3)
        )
        
        positions.forEach { pos ->
            gameBoard.setTile(pos, Tile(81, pos))
        }
        
        val mergeGroups = mergeDetector.findMergeGroups(gameBoard)
        
        assertEquals(2, mergeGroups.size)
        assertEquals(3, mergeGroups[0].positions.size)
        assertEquals(3, mergeGroups[1].positions.size)
        assertEquals(81, mergeGroups[0].value)
        assertEquals(81, mergeGroups[1].value)
        
        // Verify all positions are accounted for and no duplicates
        val allSelectedPositions = mergeGroups.flatMap { it.positions }
        assertEquals(6, allSelectedPositions.size)
        assertEquals(6, allSelectedPositions.toSet().size) // No duplicates
        assertTrue("All positions should be from original set", 
                  allSelectedPositions.all { it in positions })
    }
    
    @Test
    fun `5-tile group selects best triple prioritizing center positions`() {
        // Create a cross pattern with center position
        //     B
        //   A C D
        //     E
        val positions = listOf(
            Position(2, 1), // A - edge
            Position(1, 2), // B - center
            Position(2, 2), // C - center (optimal)
            Position(2, 3), // D - edge  
            Position(3, 2)  // E - center
        )
        
        positions.forEach { pos ->
            gameBoard.setTile(pos, Tile(243, pos))
        }
        
        val mergeGroups = mergeDetector.findMergeGroups(gameBoard)
        
        assertEquals(1, mergeGroups.size)
        assertEquals(3, mergeGroups[0].positions.size)
        assertEquals(243, mergeGroups[0].value)
        
        // Should prefer center positions (B, C, E) over edge positions (A, D)
        val selectedPositions = mergeGroups[0].positions
        val centerPositions = selectedPositions.filter { pos ->
            pos.row in 1..2 && pos.col in 1..2
        }
        
        // Should select mostly center positions for better strategic value
        assertTrue("Should prefer center positions", centerPositions.size >= 2)
    }
    
    @Test
    fun `large group (7+ tiles) uses geometric clustering efficiently`() {
        // Create a large connected group (snake pattern)
        val positions = listOf(
            Position(0, 0), Position(0, 1), Position(0, 2),
            Position(1, 2), Position(2, 2), Position(2, 1),
            Position(2, 0), Position(3, 0)
        )
        
        positions.forEach { pos ->
            gameBoard.setTile(pos, Tile(9, pos))
        }
        
        val mergeGroups = mergeDetector.findMergeGroups(gameBoard)
        
        // Should create at least 2 groups from 8 tiles (2 triples + remainders)
        assertTrue("Should create multiple groups from large set", mergeGroups.size >= 2)
        
        val totalSelectedTiles = mergeGroups.sumOf { it.positions.size }
        assertTrue("Should select at least 6 tiles for merging", totalSelectedTiles >= 6)
        
        // All selected positions should be from original set
        val allSelectedPositions = mergeGroups.flatMap { it.positions }
        assertTrue("All positions should be from original set",
                  allSelectedPositions.all { it in positions })
        
        // No duplicate selections
        assertEquals("Should not have duplicate positions",
                    allSelectedPositions.size, allSelectedPositions.toSet().size)
    }
    
    @Test
    fun `algorithm maintains backward compatibility with existing test cases`() {
        // Test that simple 3-tile horizontal group still works correctly
        gameBoard.setTile(Position(0, 0), Tile(1, Position(0, 0)))
        gameBoard.setTile(Position(0, 1), Tile(1, Position(0, 1)))
        gameBoard.setTile(Position(0, 2), Tile(1, Position(0, 2)))
        
        val mergeGroups = mergeDetector.findMergeGroups(gameBoard)
        
        assertEquals(1, mergeGroups.size)
        assertEquals(3, mergeGroups[0].positions.size)
        assertEquals(1, mergeGroups[0].value)
        
        // Verify positions match expected
        val expectedPositions = setOf(Position(0, 0), Position(0, 1), Position(0, 2))
        val actualPositions = mergeGroups[0].positions.toSet()
        assertEquals("Should maintain exact same behavior for simple cases", 
                    expectedPositions, actualPositions)
    }
    
    @Test
    fun `algorithm handles edge cases gracefully`() {
        // Test with less than 3 tiles
        gameBoard.setTile(Position(0, 0), Tile(1, Position(0, 0)))
        gameBoard.setTile(Position(0, 1), Tile(1, Position(0, 1)))
        
        val mergeGroups = mergeDetector.findMergeGroups(gameBoard)
        assertTrue("Should return empty for insufficient tiles", mergeGroups.isEmpty())
        
        // Clear board and test empty
        gameBoard.clearBoard()
        val emptyGroups = mergeDetector.findMergeGroups(gameBoard)
        assertTrue("Should return empty for empty board", emptyGroups.isEmpty())
    }
    
    @Test
    fun `position quality scoring works correctly`() {
        // This is an indirect test of our scoring system
        // Create a more realistic scenario where optimization should be observable
        
        // Create a connected group where center positions are clearly better
        gameBoard.setTile(Position(0, 0), Tile(27, Position(0, 0))) // Corner - connected
        gameBoard.setTile(Position(0, 1), Tile(27, Position(0, 1))) // Edge - bridge
        gameBoard.setTile(Position(1, 1), Tile(27, Position(1, 1))) // Center - optimal
        gameBoard.setTile(Position(1, 2), Tile(27, Position(1, 2))) // Center - optimal
        
        val mergeGroups = mergeDetector.findMergeGroups(gameBoard)
        
        assertEquals(1, mergeGroups.size)
        assertEquals(3, mergeGroups[0].positions.size)
        
        // The algorithm should select some combination of the 4 positions
        // Since this is a 4-tile group, it will pick the best 3
        val selectedPositions = mergeGroups[0].positions
        assertTrue("Should select 3 positions from the 4-tile connected group", 
                  selectedPositions.size == 3)
        
        // Verify all selected positions are from our original set
        val originalPositions = setOf(
            Position(0, 0), Position(0, 1), Position(1, 1), Position(1, 2)
        )
        assertTrue("All selected positions should be from the original connected group",
                  selectedPositions.all { it in originalPositions })
    }
}