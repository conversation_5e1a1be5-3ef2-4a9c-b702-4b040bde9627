package com.frageo.triswipe.statistics

import androidx.test.ext.junit.runners.AndroidJUnit4
import com.frageo.triswipe.data.events.StatisticsEvent
import com.frageo.triswipe.data.events.StatisticsEventProcessor
import com.frageo.triswipe.data.models.GameState
import com.frageo.triswipe.data.models.GameStats
import com.frageo.triswipe.data.models.Tile
import com.frageo.triswipe.data.models.Position
import com.frageo.triswipe.data.repository.GameStatisticsManager
import com.frageo.triswipe.data.repository.GameStatisticsRepository
import com.frageo.triswipe.data.repository.MonetizationRepository
import dagger.hilt.android.testing.HiltAndroidRule
import dagger.hilt.android.testing.HiltAndroidTest
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.delay
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.Assert.*
import javax.inject.Inject

/**
 * Integration tests for the statistics event system.
 * 
 * These tests validate the end-to-end flow of the event-driven statistics system:
 * - Event emission from GameStatisticsManager
 * - Event processing by StatisticsEventProcessor
 * - Background database persistence
 * - Real-time UI state updates
 */
@HiltAndroidTest
@RunWith(AndroidJUnit4::class)
class StatisticsEventSystemTest {

    @get:Rule
    var hiltRule = HiltAndroidRule(this)

    @Inject
    lateinit var statisticsManager: GameStatisticsManager
    
    @Inject
    lateinit var eventProcessor: StatisticsEventProcessor
    
    @Inject
    lateinit var statisticsRepository: GameStatisticsRepository
    
    @Inject
    lateinit var monetizationRepository: MonetizationRepository

    @Before
    fun init() {
        hiltRule.inject()
    }

    @Test
    fun `end to end statistics flow works correctly`() = runTest {
        // Given: Clean slate - reset statistics
        statisticsRepository.saveGameStats(GameStats())
        delay(200) // Allow reset to complete
        
        // When: Record game start through manager
        statisticsManager.recordGameStart()
        delay(100) // Allow event processing
        
        // Then: Live statistics should reflect the change immediately
        val stats1 = statisticsManager.getCurrentStatistics()
        assertEquals(1, stats1.gamesPlayed)
        
        // When: Record multiple merges
        statisticsManager.recordMerge(9)
        statisticsManager.recordMerge(27)
        statisticsManager.recordMerge(81)
        delay(100) // Allow event processing
        
        // Then: Live statistics should show merge updates immediately
        val stats2 = statisticsManager.getCurrentStatistics()
        assertEquals(3, stats2.totalMerges)
        assertEquals(81, stats2.bestTile)
        
        // When: Complete a game
        val gameState = createGameState(score = 1500, won = true)
        statisticsManager.recordGameCompletion(gameState, 180000L, 5)
        delay(300) // Allow background processing to complete
        
        // Then: Statistics should be persisted to database
        val persistedStats = statisticsRepository.loadGameStats()
        assertTrue(persistedStats.gamesPlayed >= 1)
        assertTrue(persistedStats.totalMerges >= 3)
        assertEquals(81, persistedStats.bestTile)
    }

    @Test
    fun `UI updates reflect live statistics changes`() = runTest {
        // Given: Reset statistics and get initial flow
        statisticsRepository.saveGameStats(GameStats())
        delay(200)
        
        val initialStats = statisticsManager.getStatisticsFlow().first()
        assertEquals(0, initialStats.totalMerges)
        
        // When: Record merge events
        statisticsManager.recordMerge(27)
        statisticsManager.recordMerge(243)
        delay(100)
        
        // Then: Flow should emit updated statistics immediately
        val updatedStats = statisticsManager.getStatisticsFlow().first()
        assertEquals(2, updatedStats.totalMerges)
        assertEquals(243, updatedStats.bestTile)
        
        // And: The changes should be available synchronously through getCurrentStatistics
        val currentStats = statisticsManager.getCurrentStatistics()
        assertEquals(2, currentStats.totalMerges)
        assertEquals(243, currentStats.bestTile)
    }

    @Test
    fun `batch processing persists to database correctly`() = runTest {
        // Given: Clean slate
        statisticsRepository.saveGameStats(GameStats())
        delay(200)
        
        val initialPersisted = statisticsRepository.loadGameStats()
        val initialMerges = initialPersisted.totalMerges
        
        // When: Record many merge events rapidly (should trigger batching)
        repeat(25) { index ->
            statisticsManager.recordMerge(if (index % 3 == 0) 27 else 9)
        }
        
        // Allow sufficient time for background batch processing
        delay(500)
        
        // Then: All merges should be persisted to database
        val finalPersisted = statisticsRepository.loadGameStats()
        assertEquals(initialMerges + 25, finalPersisted.totalMerges)
        assertEquals(27, finalPersisted.bestTile) // Highest tile value processed
    }

    @Test
    fun `event system handles concurrent operations correctly`() = runTest {
        // Given: Clean state
        statisticsRepository.saveGameStats(GameStats())
        delay(200)
        
        // When: Simulate concurrent game operations
        // Start a game
        statisticsManager.recordGameStart()
        
        // Record merges concurrently with game completion
        repeat(5) {
            statisticsManager.recordMerge(81)
        }
        
        // Complete the game
        val gameState = createGameState(score = 2000, won = true)
        statisticsManager.recordGameCompletion(gameState, 240000L, 8)
        
        // Allow all background processing to complete
        delay(500)
        
        // Then: Final state should be consistent
        val finalStats = statisticsRepository.loadGameStats()
        assertTrue(finalStats.gamesPlayed >= 1)
        assertTrue(finalStats.totalMerges >= 5) // At least the 5 merge events
        assertEquals(81, finalStats.bestTile)
        
        // And: Live state should match persisted state (after sync)
        delay(100) // Allow any sync to complete
        val liveStats = statisticsManager.getCurrentStatistics()
        assertEquals(finalStats.gamesPlayed, liveStats.gamesPlayed)
        assertEquals(finalStats.totalMerges, liveStats.totalMerges)
        assertEquals(finalStats.bestTile, liveStats.bestTile)
    }

    @Test
    fun `event validation prevents invalid data from affecting statistics`() = runTest {
        // Given: Clean state
        statisticsRepository.saveGameStats(GameStats())
        delay(200)
        
        val initialStats = statisticsManager.getCurrentStatistics()
        
        // When: Attempt to record invalid merge values directly through event processor
        // (These should be rejected by validation)
        eventProcessor.processEvent(StatisticsEvent.MergePerformed(-10))
        eventProcessor.processEvent(StatisticsEvent.MergePerformed(0))
        eventProcessor.processEvent(StatisticsEvent.MergePerformed(5000))
        
        delay(200) // Allow processing
        
        // Then: Statistics should remain unchanged
        val finalStats = statisticsManager.getCurrentStatistics()
        assertEquals(initialStats.totalMerges, finalStats.totalMerges)
        assertEquals(initialStats.bestTile, finalStats.bestTile)
        
        // But: Valid events should still work
        statisticsManager.recordMerge(243)
        delay(100)
        
        val validatedStats = statisticsManager.getCurrentStatistics()
        assertEquals(initialStats.totalMerges + 1, validatedStats.totalMerges)
        assertEquals(243, validatedStats.bestTile)
    }

    @Test
    fun `statistics system survives error conditions gracefully`() = runTest {
        // Given: Clean state
        statisticsRepository.saveGameStats(GameStats())
        delay(200)
        
        // When: Create problematic game state and try to process it
        val problematicGameState = GameState(
            board = Array(4) { Array<Tile?>(4) { null } },
            score = -100, // Invalid score
            moves = -10,  // Invalid moves
            isGameOver = true,
            hasWon = false,
            lastMoveType = "INVALID"
        )
        
        // This should not crash the system
        assertDoesNotThrow {
            statisticsManager.recordGameCompletion(problematicGameState, -1000L, -5)
        }
        
        delay(200)
        
        // Then: System should still be functional for valid operations
        val initialStats = statisticsManager.getCurrentStatistics()
        
        statisticsManager.recordGameStart()
        statisticsManager.recordMerge(81)
        delay(100)
        
        val finalStats = statisticsManager.getCurrentStatistics()
        assertEquals(initialStats.gamesPlayed + 1, finalStats.gamesPlayed)
        assertEquals(initialStats.totalMerges + 1, finalStats.totalMerges)
        assertEquals(81, finalStats.bestTile)
    }

    @Test
    fun `freemium integration works with event system`() = runTest {
        // Given: User is not premium
        // (This test assumes default non-premium state)
        
        // When: Check if user can start game
        val canStart = statisticsManager.canStartNewGame()
        
        // Then: Should be able to start (assuming within free game limit)
        val freeGamesRemaining = statisticsManager.getFreeGamesRemaining()
        if (freeGamesRemaining > 0) {
            assertTrue(canStart)
        }
        
        // When: Consume a free game
        if (canStart) {
            statisticsManager.consumeFreeGame()
            delay(100)
            
            // Then: Free games remaining should decrease
            val remainingAfter = statisticsManager.getFreeGamesRemaining()
            assertEquals(freeGamesRemaining - 1, remainingAfter)
        }
    }

    // Helper methods
    private fun createGameState(score: Int = 1000, won: Boolean = false): GameState {
        val board = Array(4) { Array<Tile?>(4) { null } }
        // Add some tiles to the board
        board[0][0] = Tile(243, Position(0, 0))
        board[0][1] = Tile(81, Position(0, 1))
        board[1][0] = Tile(27, Position(1, 0))
        
        return GameState(
            board = board,
            score = score,
            moves = 50,
            isGameOver = true,
            hasWon = won,
            lastMoveType = "SWIPE"
        )
    }
    
    // Helper method for assertDoesNotThrow
    private fun assertDoesNotThrow(block: () -> Unit) {
        try {
            block()
        } catch (e: Exception) {
            fail("Expected no exception but got: ${e.message}")
        }
    }
}