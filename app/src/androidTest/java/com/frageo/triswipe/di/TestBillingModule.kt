package com.frageo.triswipe.di

import android.content.Context
import com.frageo.triswipe.billing.BillingModule
import com.frageo.triswipe.billing.SimpleBillingManager
import com.frageo.triswipe.data.repository.MonetizationRepository
import com.frageo.triswipe.premium.PremiumStatusManager
import com.frageo.triswipe.testing.TestBillingManagerWrapper
import dagger.Module
import dagger.Provides
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import dagger.hilt.testing.TestInstallIn
import javax.inject.Singleton

/**
 * Test module that replaces BillingModule for integration testing.
 * Provides fake billing manager for predictable testing.
 */
@Module
@TestInstallIn(
    components = [SingletonComponent::class],
    replaces = [BillingModule::class]
)
object TestBillingModule {

    @Provides
    @Singleton
    fun provideFakeBillingManager(
        @ApplicationContext context: Context,
        premiumStatusManager: PremiumStatusManager
    ): SimpleBillingManager {
        // Create a real SimpleBillingManager for testing
        // The TestBillingManagerWrapper will be accessed separately in tests
        return SimpleBillingManager(context, premiumStatusManager)
    }

    @Provides
    @Singleton
    fun provideTestBillingManagerWrapper(
        @ApplicationContext context: Context,
        monetizationRepository: MonetizationRepository,
        premiumStatusManager: PremiumStatusManager
    ): TestBillingManagerWrapper {
        return TestBillingManagerWrapper(context, monetizationRepository, premiumStatusManager)
    }
}
