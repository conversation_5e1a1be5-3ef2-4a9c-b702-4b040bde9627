package com.frageo.triswipe.di

import android.content.Context
import androidx.room.Room
import com.frageo.triswipe.data.database.GameDao
import com.frageo.triswipe.data.database.GameDatabase
import com.frageo.triswipe.data.preferences.GamePreferences
import com.frageo.triswipe.data.repository.GameStatisticsManager
import com.frageo.triswipe.data.repository.GameStateRepository
import com.frageo.triswipe.data.repository.GameStateRepositoryImpl
import com.frageo.triswipe.data.repository.GameStatisticsRepository
import com.frageo.triswipe.data.repository.GameStatisticsRepositoryImpl
import com.frageo.triswipe.data.repository.UserPreferencesRepository
import com.frageo.triswipe.data.repository.UserPreferencesRepositoryImpl
import com.frageo.triswipe.data.repository.MonetizationRepository
import com.frageo.triswipe.data.repository.MonetizationRepositoryImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.components.SingletonComponent
import dagger.hilt.testing.TestInstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Singleton

/**
 * Test module that replaces DatabaseModule for integration testing.
 * Provides in-memory database and test-specific configurations.
 */
@Module
@TestInstallIn(
    components = [SingletonComponent::class],
    replaces = [DatabaseModule::class]
)
object TestDatabaseModule {
    
    @Provides
    @Singleton
    fun provideTestGameDatabase(@ApplicationContext context: Context): GameDatabase {
        return Room.inMemoryDatabaseBuilder(
            context,
            GameDatabase::class.java
        )
        .allowMainThreadQueries() // Allow for testing
        .build()
    }
    
    @Provides
    fun provideGameDao(database: GameDatabase): GameDao {
        return database.gameDao()
    }
    
    @Provides
    @Singleton
    fun provideTestGamePreferences(@ApplicationContext context: Context): GamePreferences {
        // Use test-specific SharedPreferences
        return GamePreferences(context, "test_preferences")
    }
    
    
    @Provides
    @Singleton
    fun provideGameStatisticsManager(
        gameStatisticsRepository: GameStatisticsRepository,
        monetizationRepository: MonetizationRepository
    ): GameStatisticsManager {
        return GameStatisticsManager(gameStatisticsRepository, monetizationRepository)
    }
    
    // New specialized repositories for testing
    @Provides
    @Singleton
    fun provideGameStateRepository(
        gameDao: GameDao
    ): GameStateRepository {
        return GameStateRepositoryImpl(gameDao)
    }
    
    @Provides
    @Singleton
    fun provideGameStatisticsRepository(
        gameDao: GameDao,
        gamePreferences: GamePreferences
    ): GameStatisticsRepository {
        return GameStatisticsRepositoryImpl(gameDao, gamePreferences)
    }
    
    @Provides
    @Singleton
    fun provideUserPreferencesRepository(
        gamePreferences: GamePreferences
    ): UserPreferencesRepository {
        return UserPreferencesRepositoryImpl(gamePreferences)
    }
    
    @Provides
    @Singleton
    fun provideMonetizationRepository(
        gamePreferences: GamePreferences
    ): MonetizationRepository {
        return MonetizationRepositoryImpl(gamePreferences)
    }
}
