package com.frageo.triswipe.database

import androidx.room.testing.MigrationTestHelper
import androidx.sqlite.db.framework.FrameworkSQLiteOpenHelperFactory
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.platform.app.InstrumentationRegistry
import com.frageo.triswipe.data.database.DatabaseMigrations
import com.frageo.triswipe.data.database.GameDatabase
import org.junit.Assert.*
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

/**
 * Comprehensive testing for database migrations.
 * 
 * These tests ensure that:
 * 1. Migrations run without errors
 * 2. Data is preserved during migrations
 * 3. Schema changes are applied correctly
 * 4. Migration paths work for all version combinations
 * 
 * CRITICAL: These tests prevent data loss in production!
 */
@RunWith(AndroidJUnit4::class)
class MigrationTest {
    
    private val TEST_DB = "migration-test"
    
    @get:Rule
    val helper: MigrationTestHelper = MigrationTestHelper(
        InstrumentationRegistry.getInstrumentation(),
        GameDatabase::class.java,
        listOf(), // No auto-migrations for now
        FrameworkSQLiteOpenHelperFactory()
    )
    
    /**
     * Test Migration 2→3: Adding premiumActivationDate column
     */
    @Test
    fun migrate2To3_AddsPremiumActivationDate() {
        // Start with version 2 database
        var db = helper.createDatabase(TEST_DB, 2).apply {
            // Insert test data
            execSQL("""
                INSERT INTO game_stats (
                    id, highScore, gamesPlayed, gamesWon, totalMoves,
                    highestTile, bestTile, averageScore, bestTime, totalPlayTime,
                    totalMerges, longestWinStreak, currentWinStreak, lastUpdated
                ) VALUES (
                    'test_stats', 15000, 50, 25, 1000,
                    243, 243, 8500.5, 120000, 600000,
                    150, 5, 2, 1640995200000
                )
            """)
            close()
        }
        
        // Run migration to version 3
        db = helper.runMigrationsAndValidate(
            TEST_DB, 
            3, 
            true, 
            DatabaseMigrations.MIGRATION_2_3
        )
        
        // Verify data is preserved
        val cursor = db.query("SELECT * FROM game_stats WHERE id = 'test_stats'")
        assertTrue("Stats record should exist", cursor.moveToFirst())
        
        // Verify original data is intact
        assertEquals(15000, cursor.getInt(cursor.getColumnIndexOrThrow("highScore")))
        assertEquals(50, cursor.getInt(cursor.getColumnIndexOrThrow("gamesPlayed")))
        assertEquals(243, cursor.getInt(cursor.getColumnIndexOrThrow("bestTile")))
        assertEquals(8500.5, cursor.getDouble(cursor.getColumnIndexOrThrow("averageScore")), 0.01)
        
        // Verify new column exists and has expected default value
        val premiumDateIndex = cursor.getColumnIndexOrThrow("premiumActivationDate")
        assertTrue("New column should exist", premiumDateIndex >= 0)
        assertTrue("Default value should be NULL", cursor.isNull(premiumDateIndex))
        
        cursor.close()
        db.close()
    }
    
    /**
     * Test Migration 3→4: Adding achievements table
     */
    @Test
    fun migrate3To4_AddsAchievementsTable() {
        // Create version 3 database (with premiumActivationDate column)
        var db = helper.createDatabase(TEST_DB, 3).apply {
            // Insert test data to ensure it's preserved
            execSQL("""
                INSERT INTO game_stats (
                    id, highScore, gamesPlayed, gamesWon, totalMoves,
                    highestTile, bestTile, averageScore, bestTime, totalPlayTime,
                    totalMerges, longestWinStreak, currentWinStreak, lastUpdated,
                    premiumActivationDate
                ) VALUES (
                    'test_stats', 20000, 75, 40, 1500,
                    729, 729, 12000.0, 90000, 900000,
                    200, 8, 3, 1640995200000,
                    1640995200000
                )
            """)
            close()
        }
        
        // Run migration to version 4
        db = helper.runMigrationsAndValidate(
            TEST_DB, 
            4, 
            true, 
            DatabaseMigrations.MIGRATION_3_4
        )
        
        // Verify achievements table was created
        val cursor = db.query("SELECT name FROM sqlite_master WHERE type='table' AND name='achievements'")
        assertTrue("Achievements table should exist", cursor.moveToFirst())
        cursor.close()
        
        // Verify achievements table structure
        val schemaCursor = db.query("PRAGMA table_info(achievements)")
        val columnNames = mutableSetOf<String>()
        while (schemaCursor.moveToNext()) {
            columnNames.add(schemaCursor.getString(1)) // Column name is at index 1
        }
        schemaCursor.close()
        
        // Verify required columns exist
        assertTrue("Should have id column", columnNames.contains("id"))
        assertTrue("Should have type column", columnNames.contains("type"))
        assertTrue("Should have unlockedAt column", columnNames.contains("unlockedAt"))
        assertTrue("Should have progress column", columnNames.contains("progress"))
        assertTrue("Should have target column", columnNames.contains("target"))
        assertTrue("Should have description column", columnNames.contains("description"))
        
        // Verify original game_stats data is preserved
        val statsCursor = db.query("SELECT * FROM game_stats WHERE id = 'test_stats'")
        assertTrue("Original stats should be preserved", statsCursor.moveToFirst())
        assertEquals(20000, statsCursor.getInt(statsCursor.getColumnIndexOrThrow("highScore")))
        assertEquals(75, statsCursor.getInt(statsCursor.getColumnIndexOrThrow("gamesPlayed")))
        statsCursor.close()
        
        db.close()
    }
    
    /**
     * Test Migration 4→5: Complex data transformation
     */
    @Test
    fun migrate4To5_TransformsDataCorrectly() {
        // Create version 4 database
        var db = helper.createDatabase(TEST_DB, 4).apply {
            // Insert test data with some NULL values to test COALESCE
            execSQL("""
                INSERT INTO game_stats (
                    id, highScore, gamesPlayed, gamesWon, totalMoves,
                    highestTile, bestTile, averageScore, bestTime, totalPlayTime,
                    totalMerges, longestWinStreak, currentWinStreak, lastUpdated,
                    premiumActivationDate
                ) VALUES (
                    'test_stats', 25000, 100, 60, 2000,
                    2187, 2187, 15000.75, 60000, 1200000,
                    300, 12, 5, 1640995200000,
                    NULL
                )
            """)
            close()
        }
        
        // Run migration to version 5
        db = helper.runMigrationsAndValidate(
            TEST_DB, 
            5, 
            true, 
            DatabaseMigrations.MIGRATION_4_5
        )
        
        // Verify data transformation worked
        val cursor = db.query("SELECT * FROM game_stats WHERE id = 'test_stats'")
        assertTrue("Transformed data should exist", cursor.moveToFirst())
        
        // Verify original data is preserved
        assertEquals(25000, cursor.getInt(cursor.getColumnIndexOrThrow("highScore")))
        assertEquals(100, cursor.getInt(cursor.getColumnIndexOrThrow("gamesPlayed")))
        assertEquals(2187, cursor.getInt(cursor.getColumnIndexOrThrow("bestTile")))
        assertEquals(15000.75, cursor.getDouble(cursor.getColumnIndexOrThrow("averageScore")), 0.01)
        
        // Verify new fields were added
        assertEquals(2, cursor.getInt(cursor.getColumnIndexOrThrow("statsVersion")))
        assertTrue("dataIntegrityHash should exist", cursor.getColumnIndexOrThrow("dataIntegrityHash") >= 0)
        
        cursor.close()
        db.close()
    }
    
    /**
     * Test complete migration path 2→5
     */
    @Test
    fun migrateAll_From2To5() {
        // Create version 2 database with comprehensive test data
        var db = helper.createDatabase(TEST_DB, 2).apply {
            execSQL("""
                INSERT INTO game_stats (
                    id, highScore, gamesPlayed, gamesWon, totalMoves,
                    highestTile, bestTile, averageScore, bestTime, totalPlayTime,
                    totalMerges, longestWinStreak, currentWinStreak, lastUpdated
                ) VALUES (
                    'comprehensive_test', 50000, 200, 120, 5000,
                    6561, 6561, 25000.0, 45000, 2400000,
                    800, 20, 8, 1640995200000
                )
            """)
            close()
        }
        
        // Run all migrations from 2 to 5
        db = helper.runMigrationsAndValidate(
            TEST_DB, 
            5, 
            true,
            *DatabaseMigrations.getAllMigrations()
        )
        
        // Verify final state
        val cursor = db.query("SELECT * FROM game_stats WHERE id = 'comprehensive_test'")
        assertTrue("Data should survive all migrations", cursor.moveToFirst())
        
        // Verify critical data preservation
        assertEquals(50000, cursor.getInt(cursor.getColumnIndexOrThrow("highScore")))
        assertEquals(200, cursor.getInt(cursor.getColumnIndexOrThrow("gamesPlayed")))
        assertEquals(6561, cursor.getInt(cursor.getColumnIndexOrThrow("bestTile")))
        assertEquals(25000.0, cursor.getDouble(cursor.getColumnIndexOrThrow("averageScore")), 0.01)
        
        // Verify all migration changes are present
        assertTrue("premiumActivationDate should exist", cursor.getColumnIndexOrThrow("premiumActivationDate") >= 0)
        assertEquals(2, cursor.getInt(cursor.getColumnIndexOrThrow("statsVersion")))
        
        cursor.close()
        
        // Verify achievements table exists
        val achievementsCursor = db.query("SELECT name FROM sqlite_master WHERE type='table' AND name='achievements'")
        assertTrue("Achievements table should exist after full migration", achievementsCursor.moveToFirst())
        achievementsCursor.close()
        
        db.close()
    }
    
    /**
     * Test migration path validation
     */
    @Test
    fun testMigrationPathValidation() {
        // Test that migration path exists from 2 to 5
        assertTrue(
            "Should have complete migration path from 2 to 5",
            DatabaseMigrations.hasCompleteMigrationPath(2, 5)
        )
        
        // Test migration range filtering
        val range3to4 = DatabaseMigrations.getMigrationsForRange(3, 4)
        assertEquals("Should have one migration for 3→4", 1, range3to4.size)
        assertEquals("Should be migration 3→4", 3, range3to4[0].startVersion)
        assertEquals("Should be migration 3→4", 4, range3to4[0].endVersion)
    }
}