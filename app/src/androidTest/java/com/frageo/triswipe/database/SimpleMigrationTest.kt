package com.frageo.triswipe.database

import androidx.room.testing.MigrationTestHelper
import androidx.sqlite.db.framework.FrameworkSQLiteOpenHelperFactory
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.platform.app.InstrumentationRegistry
import com.frageo.triswipe.data.database.DatabaseMigrations
import com.frageo.triswipe.data.database.GameDatabase
import org.junit.Assert.*
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

/**
 * Simple migration test to verify the infrastructure works.
 * This tests the most basic migration functionality.
 */
@RunWith(AndroidJUnit4::class)
class SimpleMigrationTest {
    
    private val TEST_DB = "simple-migration-test"
    
    @get:Rule
    val helper: MigrationTestHelper = MigrationTestHelper(
        InstrumentationRegistry.getInstrumentation(),
        GameDatabase::class.java,
        listOf(),
        FrameworkSQLiteOpenHelperFactory()
    )
    
    /**
     * Test that Migration 2→3 can be applied without errors
     */
    @Test
    fun migrate2To3_Succeeds() {
        // Create version 2 database
        val db = helper.createDatabase(TEST_DB, 2)
        db.close()
        
        // Run migration to version 3 - should not throw exception
        helper.runMigrationsAndValidate(
            TEST_DB, 
            3, 
            true, 
            DatabaseMigrations.MIGRATION_2_3
        )
    }
    
    /**
     * Test that migration utility methods work
     */
    @Test
    fun testMigrationUtilities() {
        // Test migration path validation
        assertTrue(
            "Should have migration path from 2 to 3",
            DatabaseMigrations.hasCompleteMigrationPath(2, 3)
        )
        
        // Test getting all migrations
        val allMigrations = DatabaseMigrations.getAllMigrations()
        assertTrue("Should have at least one migration", allMigrations.isNotEmpty())
        
        // Test migration range
        val range2to3 = DatabaseMigrations.getMigrationsForRange(2, 3)
        assertEquals("Should have exactly one migration for 2→3", 1, range2to3.size)
        assertEquals("Migration should start at version 2", 2, range2to3[0].startVersion)
        assertEquals("Migration should end at version 3", 3, range2to3[0].endVersion)
    }
}