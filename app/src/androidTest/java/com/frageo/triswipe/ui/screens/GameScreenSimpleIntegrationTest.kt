package com.frageo.triswipe.ui.screens

import androidx.compose.foundation.layout.Column
import androidx.compose.ui.test.*
import androidx.compose.ui.test.junit4.createComposeRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.frageo.triswipe.ui.components.GameBoardComponent
import com.frageo.triswipe.ui.components.GameButtons
import com.frageo.triswipe.ui.components.MovesCounter
import com.frageo.triswipe.ui.components.ScoreDisplay
import com.frageo.triswipe.ui.theme.TriSwipeTheme
import com.frageo.triswipe.data.models.*
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

@RunWith(AndroidJUnit4::class)
class GameScreenSimpleIntegrationTest {

    @get:Rule
    val composeTestRule = createComposeRule()

    private fun createTestBoard(): Array<Array<Tile?>> {
        return Array(4) { row ->
            Array(4) { col ->
                when {
                    row == 0 && col == 0 -> Tile(1, Position(0, 0), origin = TileOrigin.EXISTING)
                    row == 0 && col == 1 -> Tile(3, Position(0, 1), origin = TileOrigin.EXISTING)
                    row == 1 && col == 0 -> Tile(9, Position(1, 0), origin = TileOrigin.EXISTING)
                    else -> null
                }
            }
        }
    }

    @Test
    fun gameBoardComponent_integratesWithScoreDisplay() {
        val testBoard = createTestBoard()
        var swipeDirection: Direction? = null
        var clickedPosition: Position? = null

        composeTestRule.setContent {
            TriSwipeTheme {
                Column {
                    ScoreDisplay(
                        score = 1500,
                        tileTheme = TileTheme.CLASSIC
                    )
                    MovesCounter(
                        moves = 42,
                        tileTheme = TileTheme.CLASSIC
                    )
                    GameBoardComponent(
                        board = testBoard,
                        onSwipeGesture = { direction -> swipeDirection = direction },
                        onTileClick = { position -> clickedPosition = position }
                    )
                }
            }
        }

        // Verify score display integration
        composeTestRule
            .onNodeWithContentDescription("Current score: 1500")
            .assertIsDisplayed()

        composeTestRule
            .onNodeWithContentDescription("Moves made: 42")
            .assertIsDisplayed()

        // Verify game board integration
        composeTestRule
            .onNodeWithContentDescription("Game board with 4x4 grid. Swipe to move tiles, tap adjacent tiles to swap them.")
            .assertIsDisplayed()

        // Test interaction
        composeTestRule
            .onNodeWithContentDescription("Tile with value 1 at position 1, 1")
            .performClick()

        assert(clickedPosition == Position(0, 0)) {
            "Expected Position(0,0) but got $clickedPosition"
        }
    }

    @Test
    fun gameButtons_integrateWithGameBoard() {
        val testBoard = createTestBoard()
        var restartCalled = false
        var menuCalled = false
        var undoCalled = false

        composeTestRule.setContent {
            TriSwipeTheme {
                Column {
                    GameButtons(
                        onRestartClick = { restartCalled = true },
                        onMenuClick = { menuCalled = true },
                        onUndoClick = { undoCalled = true },
                        canUndo = true,
                        isPremiumUser = true
                    )
                    GameBoardComponent(
                        board = testBoard,
                        onSwipeGesture = { },
                        onTileClick = { }
                    )
                }
            }
        }

        // Test restart button
        composeTestRule
            .onNodeWithContentDescription("Restart game")
            .performClick()

        assert(restartCalled) { "Restart should have been called" }

        // Test menu button
        composeTestRule
            .onNodeWithContentDescription("Go to main menu")
            .performClick()

        assert(menuCalled) { "Menu navigation should have been called" }

        // Test undo button (premium user)
        composeTestRule
            .onNodeWithContentDescription("Undo last move")
            .performClick()

        assert(undoCalled) { "Undo should have been called" }
    }

    @Test
    fun gameBoard_integratesWithGestureHandling() {
        val testBoard = createTestBoard()
        var swipeDirection: Direction? = null
        var clickedPosition: Position? = null

        composeTestRule.setContent {
            TriSwipeTheme {
                GameBoardComponent(
                    board = testBoard,
                    selectedPosition = Position(0, 0),
                    onSwipeGesture = { direction -> swipeDirection = direction },
                    onTileClick = { position -> clickedPosition = position }
                )
            }
        }

        // Test swipe gesture
        composeTestRule
            .onNodeWithContentDescription("Game board with 4x4 grid. Swipe to move tiles, tap adjacent tiles to swap them.")
            .performTouchInput {
                swipeUp()
            }

        assert(swipeDirection == Direction.UP) {
            "Expected Direction.UP but got $swipeDirection"
        }

        // Test tile click
        composeTestRule
            .onNodeWithContentDescription("Tile with value 3 at position 1, 2")
            .performClick()

        assert(clickedPosition == Position(0, 1)) {
            "Expected Position(0,1) but got $clickedPosition"
        }
    }

    @Test
    fun scoreDisplay_integratesWithGameButtons() {
        var restartCalled = false

        composeTestRule.setContent {
            TriSwipeTheme {
                Column {
                    ScoreDisplay(
                        score = 2500,
                        tileTheme = TileTheme.CLASSIC
                    )
                    MovesCounter(
                        moves = 67,
                        tileTheme = TileTheme.CLASSIC
                    )
                    GameButtons(
                        onRestartClick = { restartCalled = true },
                        onMenuClick = { },
                        onUndoClick = { },
                        canUndo = false,
                        isPremiumUser = false
                    )
                }
            }
        }

        // Verify score display
        composeTestRule
            .onNodeWithContentDescription("Current score: 2500")
            .assertIsDisplayed()

        composeTestRule
            .onNodeWithContentDescription("Moves made: 67")
            .assertIsDisplayed()

        // Verify buttons integration
        composeTestRule
            .onNodeWithContentDescription("Restart game")
            .performClick()

        assert(restartCalled) { "Restart should have been called" }

        // Verify undo button shows premium feature for non-premium user
        composeTestRule
            .onNodeWithContentDescription("Undo - Premium feature")
            .assertIsDisplayed()
    }

    @Test
    fun completeGameScreen_integrationTest() {
        val testBoard = createTestBoard()
        var swipeDirection: Direction? = null
        var clickedPosition: Position? = null
        var restartCalled = false
        var menuCalled = false

        composeTestRule.setContent {
            TriSwipeTheme {
                Column {
                    ScoreDisplay(
                        score = 3000,
                        tileTheme = TileTheme.CLASSIC
                    )
                    MovesCounter(
                        moves = 85,
                        tileTheme = TileTheme.CLASSIC
                    )
                    GameBoardComponent(
                        board = testBoard,
                        selectedPosition = Position(1, 0),
                        onSwipeGesture = { direction -> swipeDirection = direction },
                        onTileClick = { position -> clickedPosition = position }
                    )
                    GameButtons(
                        onRestartClick = { restartCalled = true },
                        onMenuClick = { menuCalled = true },
                        onUndoClick = { },
                        canUndo = true,
                        isPremiumUser = true
                    )
                }
            }
        }

        // Test complete integration flow

        // 1. Verify all components are displayed
        composeTestRule
            .onNodeWithContentDescription("Current score: 3000")
            .assertIsDisplayed()

        composeTestRule
            .onNodeWithContentDescription("Game board with 4x4 grid. Swipe to move tiles, tap adjacent tiles to swap them.")
            .assertIsDisplayed()

        composeTestRule
            .onNodeWithContentDescription("Restart game")
            .assertIsDisplayed()

        // 2. Test tile interaction
        composeTestRule
            .onNodeWithContentDescription("Tile with value 9 at position 2, 1")
            .performClick()

        assert(clickedPosition == Position(1, 0)) {
            "Expected Position(1,0) but got $clickedPosition"
        }

        // 3. Test swipe gesture
        composeTestRule
            .onNodeWithContentDescription("Game board with 4x4 grid. Swipe to move tiles, tap adjacent tiles to swap them.")
            .performTouchInput {
                swipeLeft()
            }

        assert(swipeDirection == Direction.LEFT) {
            "Expected Direction.LEFT but got $swipeDirection"
        }

        // 4. Test button interactions
        composeTestRule
            .onNodeWithContentDescription("Restart game")
            .performClick()

        assert(restartCalled) { "Restart should have been called" }

        composeTestRule
            .onNodeWithContentDescription("Go to main menu")
            .performClick()

        assert(menuCalled) { "Menu navigation should have been called" }
    }
}
