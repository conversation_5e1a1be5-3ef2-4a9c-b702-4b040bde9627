package com.frageo.triswipe.ui.components

import androidx.compose.ui.test.*
import androidx.compose.ui.test.junit4.createComposeRule
import androidx.compose.ui.unit.dp
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.frageo.triswipe.data.models.Position
import com.frageo.triswipe.data.models.Tile
import com.frageo.triswipe.data.models.TileOrigin
import com.frageo.triswipe.ui.theme.TriSwipeTheme
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

/**
 * Integration tests for GameBoardComponent layout and positioning.
 * Tests the grid centering fix and tile positioning accuracy.
 */
@RunWith(AndroidJUnit4::class)
class GameBoardLayoutTest {

    @get:Rule
    val composeTestRule = createComposeRule()

    /**
     * Creates a test board with tiles in specific positions to test layout.
     */
    private fun createLayoutTestBoard(): Array<Array<Tile?>> {
        val board = Array(4) { Array<Tile?>(4) { null } }
        
        // Place tiles in corners and center to test positioning
        board[0][0] = Tile(1, Position(0, 0), origin = TileOrigin.EXISTING)   // Top-left
        board[0][3] = Tile(3, Position(0, 3), origin = TileOrigin.EXISTING)   // Top-right
        board[1][1] = Tile(9, Position(1, 1), origin = TileOrigin.EXISTING)   // Center-left
        board[1][2] = Tile(27, Position(1, 2), origin = TileOrigin.EXISTING)  // Center-right
        board[3][0] = Tile(81, Position(3, 0), origin = TileOrigin.EXISTING)  // Bottom-left
        board[3][3] = Tile(243, Position(3, 3), origin = TileOrigin.EXISTING) // Bottom-right
        
        return board
    }

    @Test
    fun gameBoardComponent_hasCorrectGridCentering() {
        val testBoard = createLayoutTestBoard()

        composeTestRule.setContent {
            TriSwipeTheme {
                GameBoardComponent(
                    board = testBoard,
                    onSwipeGesture = { },
                    onTileClick = { }
                )
            }
        }

        // Verify the game board container is displayed with correct size
        composeTestRule
            .onNodeWithContentDescription("Game board with 4x4 grid. Swipe to move tiles, tap adjacent tiles to swap them.")
            .assertIsDisplayed()
            .assertWidthIsEqualTo(360.dp)
            .assertHeightIsEqualTo(360.dp)

        // Verify all corner and center tiles are properly positioned and displayed
        composeTestRule
            .onNodeWithContentDescription("Tile with value 1 at position 1, 1")
            .assertIsDisplayed()

        composeTestRule
            .onNodeWithContentDescription("Tile with value 3 at position 1, 4")
            .assertIsDisplayed()

        composeTestRule
            .onNodeWithContentDescription("Tile with value 9 at position 2, 2")
            .assertIsDisplayed()

        composeTestRule
            .onNodeWithContentDescription("Tile with value 27 at position 2, 3")
            .assertIsDisplayed()

        composeTestRule
            .onNodeWithContentDescription("Tile with value 81 at position 4, 1")
            .assertIsDisplayed()

        composeTestRule
            .onNodeWithContentDescription("Tile with value 243 at position 4, 4")
            .assertIsDisplayed()
    }

    @Test
    fun gameBoardComponent_allPositionsAreClickable() {
        val testBoard = createLayoutTestBoard()
        val clickedPositions = mutableListOf<Position>()

        composeTestRule.setContent {
            TriSwipeTheme {
                GameBoardComponent(
                    board = testBoard,
                    onSwipeGesture = { },
                    onTileClick = { position -> clickedPositions.add(position) }
                )
            }
        }

        // Test clicking on each positioned tile
        composeTestRule
            .onNodeWithContentDescription("Tile with value 1 at position 1, 1")
            .performClick()

        composeTestRule
            .onNodeWithContentDescription("Tile with value 3 at position 1, 4")
            .performClick()

        composeTestRule
            .onNodeWithContentDescription("Tile with value 9 at position 2, 2")
            .performClick()

        composeTestRule
            .onNodeWithContentDescription("Tile with value 27 at position 2, 3")
            .performClick()

        composeTestRule
            .onNodeWithContentDescription("Tile with value 81 at position 4, 1")
            .performClick()

        composeTestRule
            .onNodeWithContentDescription("Tile with value 243 at position 4, 4")
            .performClick()

        // Verify all clicks were registered with correct positions
        assert(clickedPositions.size == 6) { 
            "Expected 6 clicks but got ${clickedPositions.size}" 
        }
        
        assert(clickedPositions.contains(Position(0, 0))) { 
            "Top-left tile click not registered" 
        }
        assert(clickedPositions.contains(Position(0, 3))) { 
            "Top-right tile click not registered" 
        }
        assert(clickedPositions.contains(Position(1, 1))) { 
            "Center-left tile click not registered" 
        }
        assert(clickedPositions.contains(Position(1, 2))) { 
            "Center-right tile click not registered" 
        }
        assert(clickedPositions.contains(Position(3, 0))) { 
            "Bottom-left tile click not registered" 
        }
        assert(clickedPositions.contains(Position(3, 3))) { 
            "Bottom-right tile click not registered" 
        }
    }

    @Test
    fun gameBoardComponent_emptyTilesAreClickable() {
        val testBoard = createLayoutTestBoard()
        val clickedPositions = mutableListOf<Position>()

        composeTestRule.setContent {
            TriSwipeTheme {
                GameBoardComponent(
                    board = testBoard,
                    onSwipeGesture = { },
                    onTileClick = { position -> clickedPositions.add(position) }
                )
            }
        }

        // Test clicking on empty positions (should be clickable for game mechanics)
        // Click on position (0,1) which should be empty
        composeTestRule
            .onNodeWithContentDescription("Empty tile at position 1, 2")
            .performClick()

        // Click on position (2,2) which should be empty  
        composeTestRule
            .onNodeWithContentDescription("Empty tile at position 3, 3")
            .performClick()

        // Verify empty tile clicks were registered
        assert(clickedPositions.size >= 2) { 
            "Expected at least 2 empty tile clicks but got ${clickedPositions.size}" 
        }
    }

    @Test
    fun gameBoardComponent_gridLayoutConsistency() {
        val testBoard = createLayoutTestBoard()

        composeTestRule.setContent {
            TriSwipeTheme {
                GameBoardComponent(
                    board = testBoard,
                    onSwipeGesture = { },
                    onTileClick = { }
                )
            }
        }

        // Verify that all 16 grid positions exist (tiles + empty spaces)
        // We should have exactly 6 tiles with values and 10 empty tiles
        
        // Count tiles with values
        composeTestRule.onNodeWithText("1").assertIsDisplayed()
        composeTestRule.onNodeWithText("3").assertIsDisplayed()
        composeTestRule.onNodeWithText("9").assertIsDisplayed()
        composeTestRule.onNodeWithText("27").assertIsDisplayed()
        composeTestRule.onNodeWithText("81").assertIsDisplayed()
        composeTestRule.onNodeWithText("243").assertIsDisplayed()

        // Verify the grid structure is maintained
        composeTestRule
            .onNodeWithContentDescription("Game board with 4x4 grid. Swipe to move tiles, tap adjacent tiles to swap them.")
            .assertIsDisplayed()
    }

    @Test
    fun gameBoardComponent_swipeGesturesWorkWithCenteredGrid() {
        val testBoard = createLayoutTestBoard()
        var swipeDirection: com.frageo.triswipe.data.models.Direction? = null

        composeTestRule.setContent {
            TriSwipeTheme {
                GameBoardComponent(
                    board = testBoard,
                    onSwipeGesture = { direction -> swipeDirection = direction },
                    onTileClick = { }
                )
            }
        }

        // Test that swipe gestures work correctly with the centered grid
        composeTestRule
            .onNodeWithContentDescription("Game board with 4x4 grid. Swipe to move tiles, tap adjacent tiles to swap them.")
            .performTouchInput {
                swipeUp()
            }

        assert(swipeDirection == com.frageo.triswipe.data.models.Direction.UP) {
            "Expected Direction.UP but got $swipeDirection"
        }

        // Test swipe down
        composeTestRule
            .onNodeWithContentDescription("Game board with 4x4 grid. Swipe to move tiles, tap adjacent tiles to swap them.")
            .performTouchInput {
                swipeDown()
            }

        assert(swipeDirection == com.frageo.triswipe.data.models.Direction.DOWN) {
            "Expected Direction.DOWN but got $swipeDirection"
        }

        // Test swipe left
        composeTestRule
            .onNodeWithContentDescription("Game board with 4x4 grid. Swipe to move tiles, tap adjacent tiles to swap them.")
            .performTouchInput {
                swipeLeft()
            }

        assert(swipeDirection == com.frageo.triswipe.data.models.Direction.LEFT) {
            "Expected Direction.LEFT but got $swipeDirection"
        }

        // Test swipe right
        composeTestRule
            .onNodeWithContentDescription("Game board with 4x4 grid. Swipe to move tiles, tap adjacent tiles to swap them.")
            .performTouchInput {
                swipeRight()
            }

        assert(swipeDirection == com.frageo.triswipe.data.models.Direction.RIGHT) {
            "Expected Direction.RIGHT but got $swipeDirection"
        }
    }
}
