package com.frageo.triswipe.ui.components

import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.test.*
import androidx.compose.ui.test.junit4.createComposeRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.frageo.triswipe.data.models.*
import com.frageo.triswipe.ui.theme.TriSwipeTheme
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

@RunWith(AndroidJUnit4::class)
class GameBoardGestureTest {

    @get:Rule
    val composeTestRule = createComposeRule()

    private fun createTestBoard(): Array<Array<Tile?>> {
        return Array(4) { row ->
            Array(4) { col ->
                when {
                    row == 0 && col == 0 -> Tile(1, Position(0, 0), origin = TileOrigin.EXISTING)
                    row == 0 && col == 1 -> Tile(3, Position(0, 1), origin = TileOrigin.EXISTING)
                    row == 1 && col == 0 -> Tile(9, Position(1, 0), origin = TileOrigin.EXISTING)
                    row == 1 && col == 1 -> Tile(27, Position(1, 1), origin = TileOrigin.EXISTING)
                    else -> null
                }
            }
        }
    }

    @Test
    fun gameBoardComponent_handlesSwipeUp() {
        val testBoard = createTestBoard()
        var capturedDirection: Direction? = null

        composeTestRule.setContent {
            TriSwipeTheme {
                GameBoardComponent(
                    board = testBoard,
                    onSwipeGesture = { direction -> capturedDirection = direction },
                    onTileClick = { }
                )
            }
        }

        // Perform swipe up gesture on the game board
        composeTestRule
            .onNodeWithContentDescription("Game board with 4x4 grid. Swipe to move tiles, tap adjacent tiles to swap them.")
            .performTouchInput {
                swipeUp()
            }

        // Verify the swipe was detected correctly
        assert(capturedDirection == Direction.UP) {
            "Expected Direction.UP but got $capturedDirection"
        }
    }

    @Test
    fun gameBoardComponent_handlesSwipeDown() {
        val testBoard = createTestBoard()
        var capturedDirection: Direction? = null

        composeTestRule.setContent {
            TriSwipeTheme {
                GameBoardComponent(
                    board = testBoard,
                    onSwipeGesture = { direction -> capturedDirection = direction },
                    onTileClick = { }
                )
            }
        }

        // Perform swipe down gesture
        composeTestRule
            .onNodeWithContentDescription("Game board with 4x4 grid. Swipe to move tiles, tap adjacent tiles to swap them.")
            .performTouchInput {
                swipeDown()
            }

        // Verify the swipe was detected correctly
        assert(capturedDirection == Direction.DOWN) {
            "Expected Direction.DOWN but got $capturedDirection"
        }
    }

    @Test
    fun gameBoardComponent_handlesSwipeLeft() {
        val testBoard = createTestBoard()
        var capturedDirection: Direction? = null

        composeTestRule.setContent {
            TriSwipeTheme {
                GameBoardComponent(
                    board = testBoard,
                    onSwipeGesture = { direction -> capturedDirection = direction },
                    onTileClick = { }
                )
            }
        }

        // Perform swipe left gesture
        composeTestRule
            .onNodeWithContentDescription("Game board with 4x4 grid. Swipe to move tiles, tap adjacent tiles to swap them.")
            .performTouchInput {
                swipeLeft()
            }

        // Verify the swipe was detected correctly
        assert(capturedDirection == Direction.LEFT) {
            "Expected Direction.LEFT but got $capturedDirection"
        }
    }

    @Test
    fun gameBoardComponent_handlesSwipeRight() {
        val testBoard = createTestBoard()
        var capturedDirection: Direction? = null

        composeTestRule.setContent {
            TriSwipeTheme {
                GameBoardComponent(
                    board = testBoard,
                    onSwipeGesture = { direction -> capturedDirection = direction },
                    onTileClick = { }
                )
            }
        }

        // Perform swipe right gesture
        composeTestRule
            .onNodeWithContentDescription("Game board with 4x4 grid. Swipe to move tiles, tap adjacent tiles to swap them.")
            .performTouchInput {
                swipeRight()
            }

        // Verify the swipe was detected correctly
        assert(capturedDirection == Direction.RIGHT) {
            "Expected Direction.RIGHT but got $capturedDirection"
        }
    }

    @Test
    fun gameBoardComponent_handlesMultipleTileClicks() {
        val testBoard = createTestBoard()
        val clickedPositions = mutableListOf<Position>()

        composeTestRule.setContent {
            TriSwipeTheme {
                GameBoardComponent(
                    board = testBoard,
                    onSwipeGesture = { },
                    onTileClick = { position -> clickedPositions.add(position) }
                )
            }
        }

        // Click multiple tiles in sequence (simulating tap-to-swap)
        composeTestRule
            .onNodeWithContentDescription("Tile with value 1 at position 1, 1")
            .performClick()

        composeTestRule
            .onNodeWithContentDescription("Tile with value 3 at position 1, 2")
            .performClick()

        // Verify both clicks were captured
        assert(clickedPositions.size == 2) {
            "Expected 2 clicks but got ${clickedPositions.size}"
        }
        assert(clickedPositions[0] == Position(0, 0)) {
            "First click should be Position(0,0) but was ${clickedPositions[0]}"
        }
        assert(clickedPositions[1] == Position(0, 1)) {
            "Second click should be Position(0,1) but was ${clickedPositions[1]}"
        }
    }

    @Test
    fun gameBoardComponent_handlesShortSwipeAsClick() {
        val testBoard = createTestBoard()
        var swipeDetected = false
        var clickDetected = false
        var clickedPosition: Position? = null

        composeTestRule.setContent {
            TriSwipeTheme {
                GameBoardComponent(
                    board = testBoard,
                    onSwipeGesture = { swipeDetected = true },
                    onTileClick = { position -> 
                        clickDetected = true
                        clickedPosition = position
                    }
                )
            }
        }

        // Perform a click on the tile instead of a short swipe
        composeTestRule
            .onNodeWithContentDescription("Tile with value 1 at position 1, 1")
            .performClick()

        // Should be treated as a click, not a swipe
        // Note: This behavior depends on the swipe threshold implementation
    }

    @Test
    fun gameBoardComponent_ignoresVeryShortGestures() {
        val testBoard = createTestBoard()
        var gestureDetected = false

        composeTestRule.setContent {
            TriSwipeTheme {
                GameBoardComponent(
                    board = testBoard,
                    onSwipeGesture = { gestureDetected = true },
                    onTileClick = { gestureDetected = true }
                )
            }
        }

        // Perform a click on the game board
        composeTestRule
            .onNodeWithContentDescription("Game board with 4x4 grid. Swipe to move tiles, tap adjacent tiles to swap them.")
            .performClick()

        // This should trigger a click on the underlying tile
        // The exact behavior depends on the implementation
    }

    @Test
    fun gameBoardComponent_handlesRapidGestures() {
        val testBoard = createTestBoard()
        val capturedDirections = mutableListOf<Direction>()

        composeTestRule.setContent {
            TriSwipeTheme {
                GameBoardComponent(
                    board = testBoard,
                    onSwipeGesture = { direction -> capturedDirections.add(direction) },
                    onTileClick = { }
                )
            }
        }

        val gameBoard = composeTestRule
            .onNodeWithContentDescription("Game board with 4x4 grid. Swipe to move tiles, tap adjacent tiles to swap them.")

        // Perform rapid swipes in different directions
        gameBoard.performTouchInput {
            swipeUp()
        }

        // Small delay between gestures
        composeTestRule.waitForIdle()

        gameBoard.performTouchInput {
            swipeRight()
        }

        // Verify both gestures were captured
        assert(capturedDirections.size >= 1) {
            "Expected at least 1 gesture but got ${capturedDirections.size}"
        }
    }
}
