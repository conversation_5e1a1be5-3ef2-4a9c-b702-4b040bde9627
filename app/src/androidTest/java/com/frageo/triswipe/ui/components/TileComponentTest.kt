package com.frageo.triswipe.ui.components

import androidx.compose.ui.test.*
import androidx.compose.ui.test.junit4.createComposeRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.frageo.triswipe.data.models.*
import com.frageo.triswipe.ui.theme.TriSwipeTheme
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

@RunWith(AndroidJUnit4::class)
class TileComponentTest {

    @get:Rule
    val composeTestRule = createComposeRule()

    @Test
    fun tileComponent_displaysValueCorrectly() {
        val position = Position(1, 2)
        val tile = Tile(27, position, origin = TileOrigin.EXISTING)
        var clickedPosition: Position? = null

        composeTestRule.setContent {
            TriSwipeTheme {
                TileComponent(
                    tile = tile,
                    position = position,
                    onTileClick = { pos -> clickedPosition = pos }
                )
            }
        }

        // Verify the tile displays the correct value
        composeTestRule
            .onNodeWithText("27")
            .assertIsDisplayed()

        // Verify the content description is correct
        composeTestRule
            .onNodeWithContentDescription("Tile with value 27 at position 2, 3")
            .assertIsDisplayed()
    }

    @Test
    fun tileComponent_displaysEmptyTileCorrectly() {
        val position = Position(0, 0)
        var clickedPosition: Position? = null

        composeTestRule.setContent {
            TriSwipeTheme {
                TileComponent(
                    tile = null,
                    position = position,
                    onTileClick = { pos -> clickedPosition = pos }
                )
            }
        }

        // Verify the empty tile has correct content description
        composeTestRule
            .onNodeWithContentDescription("Empty tile at position 1, 1")
            .assertIsDisplayed()

        // Verify no text is displayed for empty tile
        composeTestRule
            .onNodeWithText("0")
            .assertDoesNotExist()
    }

    @Test
    fun tileComponent_handlesClick() {
        val position = Position(2, 1)
        val tile = Tile(9, position, origin = TileOrigin.EXISTING)
        var clickedPosition: Position? = null

        composeTestRule.setContent {
            TriSwipeTheme {
                TileComponent(
                    tile = tile,
                    position = position,
                    onTileClick = { pos -> clickedPosition = pos }
                )
            }
        }

        // Click the tile
        composeTestRule
            .onNodeWithContentDescription("Tile with value 9 at position 3, 2")
            .performClick()

        // Verify the click was handled correctly
        assert(clickedPosition == position)
    }

    @Test
    fun tileComponent_handlesEmptyTileClick() {
        val position = Position(3, 3)
        var clickedPosition: Position? = null

        composeTestRule.setContent {
            TriSwipeTheme {
                TileComponent(
                    tile = null,
                    position = position,
                    onTileClick = { pos -> clickedPosition = pos }
                )
            }
        }

        // Click the empty tile
        composeTestRule
            .onNodeWithContentDescription("Empty tile at position 4, 4")
            .performClick()

        // Verify the click was handled correctly
        assert(clickedPosition == position)
    }

    @Test
    fun tileComponent_showsSelectedState() {
        val position = Position(1, 1)
        val tile = Tile(3, position, origin = TileOrigin.EXISTING)

        composeTestRule.setContent {
            TriSwipeTheme {
                TileComponent(
                    tile = tile,
                    position = position,
                    isSelected = true,
                    onTileClick = { }
                )
            }
        }

        // Verify the tile is displayed (selection styling is visual)
        composeTestRule
            .onNodeWithContentDescription("Tile with value 3 at position 2, 2")
            .assertIsDisplayed()
    }

    @Test
    fun tileComponent_showsMergingState() {
        val position = Position(0, 2)
        val tile = Tile(81, position, origin = TileOrigin.MERGE)

        composeTestRule.setContent {
            TriSwipeTheme {
                TileComponent(
                    tile = tile,
                    position = position,
                    isMerging = true,
                    onTileClick = { }
                )
            }
        }

        // Verify the merging tile is displayed
        composeTestRule
            .onNodeWithContentDescription("Tile with value 81 at position 1, 3")
            .assertIsDisplayed()

        composeTestRule
            .onNodeWithText("81")
            .assertIsDisplayed()
    }

    @Test
    fun tileComponent_displaysLargeNumbers() {
        val position = Position(2, 2)
        val tile = Tile(2187, position, origin = TileOrigin.EXISTING) // Win condition tile

        composeTestRule.setContent {
            TriSwipeTheme {
                TileComponent(
                    tile = tile,
                    position = position,
                    onTileClick = { }
                )
            }
        }

        // Verify large numbers are displayed correctly
        composeTestRule
            .onNodeWithText("2187")
            .assertIsDisplayed()

        composeTestRule
            .onNodeWithContentDescription("Tile with value 2187 at position 3, 3")
            .assertIsDisplayed()
    }

    @Test
    fun tileComponent_handlesSpawnTile() {
        val position = Position(3, 0)
        val tile = Tile(1, position, origin = TileOrigin.SPAWN)

        composeTestRule.setContent {
            TriSwipeTheme {
                TileComponent(
                    tile = tile,
                    position = position,
                    onTileClick = { }
                )
            }
        }

        // Verify spawn tile is displayed correctly
        composeTestRule
            .onNodeWithText("1")
            .assertIsDisplayed()

        composeTestRule
            .onNodeWithContentDescription("Tile with value 1 at position 4, 1")
            .assertIsDisplayed()
    }

    @Test
    fun tileComponent_handlesAllTileValues() {
        // Test a few representative tile values instead of all in one test
        val position = Position(0, 0)
        val tile = Tile(243, position, origin = TileOrigin.EXISTING)

        composeTestRule.setContent {
            TriSwipeTheme {
                TileComponent(
                    tile = tile,
                    position = position,
                    onTileClick = { }
                )
            }
        }

        // Verify the tile value is displayed correctly
        composeTestRule
            .onNodeWithText("243")
            .assertIsDisplayed()

        composeTestRule
            .onNodeWithContentDescription("Tile with value 243 at position 1, 1")
            .assertIsDisplayed()
    }
}
