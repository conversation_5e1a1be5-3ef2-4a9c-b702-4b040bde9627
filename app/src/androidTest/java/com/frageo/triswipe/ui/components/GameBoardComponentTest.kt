package com.frageo.triswipe.ui.components

import androidx.compose.ui.test.*
import androidx.compose.ui.test.junit4.createComposeRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.frageo.triswipe.data.models.*
import com.frageo.triswipe.ui.theme.TriSwipeTheme
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

@RunWith(AndroidJUnit4::class)
class GameBoardComponentTest {

    @get:Rule
    val composeTestRule = createComposeRule()

    private fun createTestBoard(): Array<Array<Tile?>> {
        return Array(4) { row ->
            Array(4) { col ->
                when {
                    row == 0 && col == 0 -> Tile(1, Position(0, 0), origin = TileOrigin.EXISTING)
                    row == 0 && col == 1 -> Tile(3, Position(0, 1), origin = TileOrigin.EXISTING)
                    row == 1 && col == 0 -> Tile(9, Position(1, 0), origin = TileOrigin.EXISTING)
                    else -> null
                }
            }
        }
    }

    private fun createEmptyBoard(): Array<Array<Tile?>> {
        return Array(4) { Array(4) { null } }
    }

    @Test
    fun gameBoardComponent_displaysCorrectly() {
        val testBoard = createTestBoard()
        var swipeDirection: Direction? = null
        var clickedPosition: Position? = null

        composeTestRule.setContent {
            TriSwipeTheme {
                GameBoardComponent(
                    board = testBoard,
                    onSwipeGesture = { direction -> swipeDirection = direction },
                    onTileClick = { position -> clickedPosition = position }
                )
            }
        }

        // Verify the game board is displayed
        composeTestRule
            .onNodeWithContentDescription("Game board with 4x4 grid. Swipe to move tiles, tap adjacent tiles to swap them.")
            .assertIsDisplayed()

        // Verify tiles with values are displayed
        composeTestRule
            .onNodeWithContentDescription("Tile with value 1 at position 1, 1")
            .assertIsDisplayed()

        composeTestRule
            .onNodeWithContentDescription("Tile with value 3 at position 1, 2")
            .assertIsDisplayed()

        composeTestRule
            .onNodeWithContentDescription("Tile with value 9 at position 2, 1")
            .assertIsDisplayed()
    }

    @Test
    fun gameBoardComponent_handlesEmptyBoard() {
        val emptyBoard = createEmptyBoard()

        composeTestRule.setContent {
            TriSwipeTheme {
                GameBoardComponent(
                    board = emptyBoard,
                    onSwipeGesture = { },
                    onTileClick = { }
                )
            }
        }

        // Verify the game board is displayed even when empty
        composeTestRule
            .onNodeWithContentDescription("Game board with 4x4 grid. Swipe to move tiles, tap adjacent tiles to swap them.")
            .assertIsDisplayed()

        // Verify empty tiles are displayed (should have 16 empty tiles)
        composeTestRule
            .onAllNodesWithContentDescription("Empty tile at position 1, 1")
            .assertCountEquals(1)
    }

    @Test
    fun gameBoardComponent_handlesTileClick() {
        val testBoard = createTestBoard()
        var clickedPosition: Position? = null

        composeTestRule.setContent {
            TriSwipeTheme {
                GameBoardComponent(
                    board = testBoard,
                    onSwipeGesture = { },
                    onTileClick = { position -> clickedPosition = position }
                )
            }
        }

        // Click on the tile with value 1 at position (0,0)
        composeTestRule
            .onNodeWithContentDescription("Tile with value 1 at position 1, 1")
            .performClick()

        // Verify the click was handled correctly
        assert(clickedPosition == Position(0, 0))
    }

    @Test
    fun gameBoardComponent_showsSelectedTile() {
        val testBoard = createTestBoard()
        val selectedPosition = Position(0, 0)

        composeTestRule.setContent {
            TriSwipeTheme {
                GameBoardComponent(
                    board = testBoard,
                    selectedPosition = selectedPosition,
                    onSwipeGesture = { },
                    onTileClick = { }
                )
            }
        }

        // The selected tile should be displayed (visual selection is handled by TileComponent)
        composeTestRule
            .onNodeWithContentDescription("Tile with value 1 at position 1, 1")
            .assertIsDisplayed()
    }

    @Test
    fun gameBoardComponent_handlesSwappingState() {
        val testBoard = createTestBoard()

        composeTestRule.setContent {
            TriSwipeTheme {
                GameBoardComponent(
                    board = testBoard,
                    isSwapping = true,
                    onSwipeGesture = { },
                    onTileClick = { }
                )
            }
        }

        // Verify the board is still displayed during swapping
        composeTestRule
            .onNodeWithContentDescription("Game board with 4x4 grid. Swipe to move tiles, tap adjacent tiles to swap them.")
            .assertIsDisplayed()
    }

    @Test
    fun gameBoardComponent_handlesMergingState() {
        val testBoard = createTestBoard()

        composeTestRule.setContent {
            TriSwipeTheme {
                GameBoardComponent(
                    board = testBoard,
                    isMerging = true,
                    onSwipeGesture = { },
                    onTileClick = { }
                )
            }
        }

        // Verify the board is still displayed during merging
        composeTestRule
            .onNodeWithContentDescription("Game board with 4x4 grid. Swipe to move tiles, tap adjacent tiles to swap them.")
            .assertIsDisplayed()
    }

    @Test
    fun gameBoardComponent_displaysAllTilePositions() {
        val testBoard = createTestBoard()

        composeTestRule.setContent {
            TriSwipeTheme {
                GameBoardComponent(
                    board = testBoard,
                    onSwipeGesture = { },
                    onTileClick = { }
                )
            }
        }

        // Verify that all 16 positions are rendered (some with tiles, some empty)
        // We should have 3 tiles with values and 13 empty tiles
        
        // Check that we have the expected tiles
        composeTestRule.onNodeWithText("1").assertIsDisplayed()
        composeTestRule.onNodeWithText("3").assertIsDisplayed()
        composeTestRule.onNodeWithText("9").assertIsDisplayed()
    }
}
