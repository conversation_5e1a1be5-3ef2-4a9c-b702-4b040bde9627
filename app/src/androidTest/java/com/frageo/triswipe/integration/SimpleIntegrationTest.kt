package com.frageo.triswipe.integration

import androidx.compose.ui.test.*
import androidx.compose.ui.platform.testTag
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.frageo.triswipe.testing.BaseIntegrationTest
import dagger.hilt.android.testing.HiltAndroidTest
import kotlinx.coroutines.runBlocking
import org.junit.Test
import org.junit.runner.RunWith

/**
 * Simple integration test to verify the testing infrastructure works.
 */
@HiltAndroidTest
@RunWith(AndroidJUnit4::class)
class SimpleIntegrationTest : BaseIntegrationTest() {

    @Test
    fun testInfrastructure_works() {
        runBlocking {
            // Setup: Basic test scenario
            setupTestScenario(isPremiumUser = false, freeGamesRemaining = 5)

            // Verify: Test infrastructure is working
            waitForUiToSettle()

            // This test just verifies that:
            // 1. Hilt dependency injection works
            // 2. Test modules are loaded correctly
            // 3. BaseIntegrationTest setup works
            // 4. Basic test utilities work

            // If this test passes, the infrastructure is working
            assert(true)
        }
    }

    @Test
    fun simpleComposable_displaysCorrectly() {
        runBlocking {
            // Setup: Basic test scenario
            setupTestScenario(isPremiumUser = false, freeGamesRemaining = 5)

            composeTestRule.setContent {
                com.frageo.triswipe.ui.theme.TriSwipeTheme {
                    androidx.compose.material3.Text(
                        text = "Test Integration",
                        modifier = androidx.compose.ui.Modifier.testTag("test-text")
                    )
                }
            }

            waitForUiToSettle()

            // Verify simple composable displays correctly
            composeTestRule.onNodeWithTag("test-text")
                .assertIsDisplayed()
                .assertTextEquals("Test Integration")
        }
    }

    @Test
    fun testBillingManager_isInjected() {
        runBlocking {
            // Verify that the test billing manager is properly injected
            setupTestScenario(isPremiumUser = false, freeGamesRemaining = 5)
            
            // Test that we can access the test billing manager
            testBillingManager.resetToIdle()
            
            // Verify billing manager is working
            assert(testBillingManager.isBillingAvailable())
            assert(testBillingManager.canMakePurchases())
        }
    }

    @Test
    fun testRepository_isInjected() {
        runBlocking {
            // Verify that the repository is properly injected
            setupTestScenario(isPremiumUser = false, freeGamesRemaining = 5)
            
            // Test that we can access the game repository
            gameRepository.setPremiumUser(true)
            
            // Verify repository is working
            assert(true) // If we get here without exceptions, injection worked
        }
    }
}
