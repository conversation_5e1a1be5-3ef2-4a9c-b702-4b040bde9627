package com.frageo.triswipe.testing

import androidx.compose.ui.test.*
import com.frageo.triswipe.data.models.*

/**
 * Custom assertion helpers for TriSwipe integration testing.
 */
object TestAssertions {
    
    /**
     * Asserts that a game board displays the expected tiles.
     */
    fun SemanticsNodeInteractionsProvider.assertBoardDisplaysTiles(
        expectedBoard: Array<Array<Tile?>>
    ) {
        for (row in 0 until 4) {
            for (col in 0 until 4) {
                val tile = expectedBoard[row][col]
                if (tile != null) {
                    onNodeWithContentDescription(
                        "Tile with value ${tile.value} at position ${row + 1}, ${col + 1}"
                    ).assertIsDisplayed()
                }
            }
        }
    }
    
    /**
     * Asserts that the score display shows the expected value.
     */
    fun SemanticsNodeInteractionsProvider.assertScoreDisplays(expectedScore: Int) {
        onNodeWithContentDescription("Current score: $expectedScore")
            .assertIsDisplayed()
    }
    
    /**
     * Asserts that the moves counter shows the expected value.
     */
    fun SemanticsNodeInteractionsProvider.assertMovesDisplay(expectedMoves: Int) {
        onNodeWithContentDescription("Moves made: $expectedMoves")
            .assertIsDisplayed()
    }
    
    /**
     * Asserts that a tile is selected (highlighted).
     */
    fun SemanticsNodeInteractionsProvider.assertTileIsSelected(position: Position) {
        onNodeWithContentDescription(
            "Tile with value at position ${position.row + 1}, ${position.col + 1}"
        ).assertIsDisplayed()
    }
    
    /**
     * Asserts that an error message is displayed.
     */
    fun SemanticsNodeInteractionsProvider.assertErrorMessageDisplayed(message: String) {
        onNodeWithText(message).assertIsDisplayed()
    }
    
    /**
     * Asserts that the game over dialog is displayed.
     */
    fun SemanticsNodeInteractionsProvider.assertGameOverDialogDisplayed() {
        onNodeWithText("Game Over").assertIsDisplayed()
    }
    
    /**
     * Asserts that the game win dialog is displayed.
     */
    fun SemanticsNodeInteractionsProvider.assertGameWinDialogDisplayed() {
        onNodeWithText("Congratulations!").assertIsDisplayed()
    }
    
    /**
     * Asserts that the undo button is in the expected state.
     */
    fun SemanticsNodeInteractionsProvider.assertUndoButtonState(
        isEnabled: Boolean,
        isPremiumUser: Boolean
    ) {
        val undoButton = onNodeWithContentDescription("Undo last move")
        
        if (isEnabled && isPremiumUser) {
            undoButton.assertIsEnabled()
        } else {
            undoButton.assertIsNotEnabled()
        }
    }
    
    /**
     * Asserts that the upgrade dialog is displayed.
     */
    fun SemanticsNodeInteractionsProvider.assertUpgradeDialogDisplayed() {
        onNodeWithText("Upgrade to Premium").assertIsDisplayed()
    }
    
    /**
     * Asserts that navigation buttons are displayed and enabled.
     */
    fun SemanticsNodeInteractionsProvider.assertNavigationButtonsDisplayed() {
        onNodeWithContentDescription("Restart game").assertIsDisplayed()
        onNodeWithContentDescription("Go to main menu").assertIsDisplayed()
    }
    
    /**
     * Asserts that the game board is in the expected state.
     */
    fun SemanticsNodeInteractionsProvider.assertGameBoardState(
        isInteractive: Boolean = true,
        hasSelectedTile: Boolean = false
    ) {
        val gameBoard = onNodeWithContentDescription(
            "Game board with 4x4 grid. Swipe to move tiles, tap adjacent tiles to swap them."
        )
        
        gameBoard.assertIsDisplayed()
        
        if (isInteractive) {
            gameBoard.assertHasClickAction()
        }
    }
    
    /**
     * Asserts that loading state is displayed.
     */
    fun SemanticsNodeInteractionsProvider.assertLoadingDisplayed() {
        // Look for loading indicators - this might need adjustment based on actual loading UI
        onNodeWithText("Loading...")
            .assertIsDisplayed()
    }
    
    /**
     * Asserts that freemium status is displayed correctly.
     */
    fun SemanticsNodeInteractionsProvider.assertFreemiumStatusDisplayed(
        freeGamesRemaining: Int,
        isPremiumUser: Boolean
    ) {
        if (!isPremiumUser && freeGamesRemaining >= 0) {
            onNodeWithText("Free games remaining: $freeGamesRemaining")
                .assertIsDisplayed()
        }
    }
}
