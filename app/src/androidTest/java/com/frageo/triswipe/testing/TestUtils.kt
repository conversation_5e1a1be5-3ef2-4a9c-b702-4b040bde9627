package com.frageo.triswipe.testing

import androidx.compose.ui.test.*
import androidx.compose.ui.test.junit4.ComposeTestRule
import kotlinx.coroutines.delay

/**
 * Utility functions for integration testing.
 */
object TestUtils {
    
    /**
     * Performs a swipe gesture in the specified direction on the game board.
     */
    fun ComposeTestRule.performSwipeOnGameBoard(direction: String) {
        onNodeWithContentDescription(
            "Game board with 4x4 grid. Swipe to move tiles, tap adjacent tiles to swap them."
        ).performTouchInput {
            when (direction.lowercase()) {
                "up" -> swipeUp()
                "down" -> swipeDown()
                "left" -> swipeLeft()
                "right" -> swipeRight()
                else -> throw IllegalArgumentException("Invalid direction: $direction")
            }
        }
    }
    
    /**
     * Clicks on a tile at the specified position.
     */
    fun ComposeTestRule.clickTileAt(row: Int, col: Int) {
        // Note: UI positions are 1-based, so we add 1
        // Try to find any tile at this position
        onAllNodesWithContentDescription("Tile with value")
            .onFirst()
            .performClick()
    }
    
    /**
     * Clicks on a specific tile with a known value.
     */
    fun ComposeTestRule.clickTileWithValue(value: Int, row: Int, col: Int) {
        onNodeWithContentDescription(
            "Tile with value $value at position ${row + 1}, ${col + 1}"
        ).performClick()
    }
    
    /**
     * Waits for animations to complete.
     */
    fun ComposeTestRule.waitForAnimations(timeoutMs: Long = 1000L) {
        waitForIdle()
        // Additional wait for any custom animations
        mainClock.advanceTimeBy(timeoutMs)
    }
    
    /**
     * Performs rapid gestures for testing gesture conflicts.
     */
    fun ComposeTestRule.performRapidGestures(vararg directions: String) {
        directions.forEach { direction ->
            performSwipeOnGameBoard(direction)
            waitForIdle()
        }
    }
    
    /**
     * Simulates a complete tile swap operation.
     */
    fun ComposeTestRule.performTileSwap(
        fromRow: Int, fromCol: Int,
        toRow: Int, toCol: Int
    ) {
        // Click first tile to select it
        clickTileAt(fromRow, fromCol)
        waitForIdle()
        
        // Click second tile to swap
        clickTileAt(toRow, toCol)
        waitForIdle()
    }
    
    /**
     * Navigates to a specific screen using buttons.
     */
    fun ComposeTestRule.navigateToScreen(screenName: String) {
        when (screenName.lowercase()) {
            "game" -> {
                onNodeWithText("Start Game").performClick()
            }
            "settings" -> {
                onNodeWithText("Settings").performClick()
            }
            "menu", "main menu" -> {
                onNodeWithContentDescription("Go to main menu").performClick()
            }
            else -> throw IllegalArgumentException("Unknown screen: $screenName")
        }
        waitForIdle()
    }
    
    /**
     * Restarts the game using the restart button.
     */
    fun ComposeTestRule.restartGame() {
        onNodeWithContentDescription("Restart game").performClick()
        waitForIdle()
    }
    
    /**
     * Performs undo action if available.
     */
    fun ComposeTestRule.performUndo() {
        onNodeWithContentDescription("Undo last move").performClick()
        waitForIdle()
    }
    
    /**
     * Dismisses any error messages that might be displayed.
     */
    fun ComposeTestRule.dismissErrorMessage() {
        // Error messages typically auto-dismiss, but we can wait for them to disappear
        waitForAnimations(3000L) // Wait up to 3 seconds for auto-dismiss
    }
    
    /**
     * Waits for a specific condition to be met.
     */
    fun ComposeTestRule.waitForCondition(
        timeoutMs: Long = 5000L,
        condition: () -> Boolean
    ) {
        val startTime = System.currentTimeMillis()
        while (!condition() && (System.currentTimeMillis() - startTime) < timeoutMs) {
            waitForIdle()
            Thread.sleep(100)
        }
    }
    
    /**
     * Simulates device rotation for testing responsive layouts.
     */
    fun ComposeTestRule.simulateRotation() {
        // This would typically involve changing the configuration
        // For now, we'll just wait for any layout changes
        waitForAnimations()
    }
    
    /**
     * Verifies that no crashes or exceptions occurred during test.
     */
    fun ComposeTestRule.assertNoExceptions() {
        // This is more of a conceptual assertion
        // In practice, any exceptions would cause the test to fail
        waitForIdle()
    }
}
