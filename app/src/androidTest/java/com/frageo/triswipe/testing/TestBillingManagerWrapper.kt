package com.frageo.triswipe.testing

import android.app.Activity
import android.content.Context
import com.android.billingclient.api.BillingClient
import com.android.billingclient.api.BillingClientStateListener
import com.android.billingclient.api.BillingResult
import com.android.billingclient.api.Purchase
import com.android.billingclient.api.PurchasesUpdatedListener
import com.frageo.triswipe.billing.BillingState
import com.frageo.triswipe.billing.PurchaseState
import com.frageo.triswipe.billing.SimpleBillingManager
import com.frageo.triswipe.data.repository.MonetizationRepository
import com.frageo.triswipe.premium.PremiumStatusManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * Test implementation that mimics SimpleBillingManager behavior for testing.
 * Since SimpleBillingManager is final, we create a separate implementation.
 */
class TestBillingManagerWrapper(
    private val context: Context,
    private val monetizationRepository: MonetizationRepository,
    private val premiumStatusManager: PremiumStatusManager
) : PurchasesUpdatedListener, BillingClientStateListener {

    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    // Test state flows
    private val _testBillingState = MutableStateFlow(BillingState.CONNECTED)
    val billingState: StateFlow<BillingState> = _testBillingState.asStateFlow()

    private val _testPurchaseState = MutableStateFlow<PurchaseState>(PurchaseState.Idle)
    val purchaseState: StateFlow<PurchaseState> = _testPurchaseState.asStateFlow()

    // Test control properties
    var shouldSimulateError = false
    var shouldSimulatePending = false
    var purchaseDelay = 100L // Short delay for tests

    fun launchPurchaseFlow(activity: Activity) {
        _testPurchaseState.value = PurchaseState.Purchasing

        scope.launch {
            kotlinx.coroutines.delay(purchaseDelay)

            when {
                shouldSimulateError -> {
                    _testPurchaseState.value = PurchaseState.Error("Test error")
                }
                shouldSimulatePending -> {
                    _testPurchaseState.value = PurchaseState.Pending
                }
                else -> {
                    _testPurchaseState.value = PurchaseState.Success
                    // Set premium user through repository
                    scope.launch {
                        monetizationRepository.setPremiumUser(true)
                    }
                }
            }
        }
    }

    fun isBillingAvailable(): Boolean = _testBillingState.value == BillingState.CONNECTED
    fun canMakePurchases(): Boolean = true

    // Test helper methods
    fun simulateConnectionError() {
        _testBillingState.value = BillingState.UNAVAILABLE
    }
    
    fun simulateConnectionSuccess() {
        _testBillingState.value = BillingState.CONNECTED
    }
    
    fun resetToIdle() {
        _testPurchaseState.value = PurchaseState.Idle
        shouldSimulateError = false
        shouldSimulatePending = false
        _testBillingState.value = BillingState.CONNECTED
    }
    
    fun simulatePurchaseSuccess() {
        scope.launch {
            _testPurchaseState.value = PurchaseState.Success
            monetizationRepository.setPremiumUser(true)
        }
    }
    
    fun simulatePurchaseError(message: String = "Test purchase error") {
        _testPurchaseState.value = PurchaseState.Error(message)
    }
    
    fun simulatePurchaseCancelled() {
        _testPurchaseState.value = PurchaseState.Cancelled
    }

    // Required interface implementations
    override fun onPurchasesUpdated(billingResult: BillingResult, purchases: MutableList<Purchase>?) {
        // Test implementation - not used in fake
    }

    override fun onBillingSetupFinished(billingResult: BillingResult) {
        // Test implementation - not used in fake
    }

    override fun onBillingServiceDisconnected() {
        // Test implementation - not used in fake
    }
}
