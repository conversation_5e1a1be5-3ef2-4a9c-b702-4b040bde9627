package com.frageo.triswipe.testing

import com.frageo.triswipe.data.models.*

/**
 * Test data builders for creating consistent test scenarios.
 */
object TestDataBuilders {
    
    /**
     * Creates a basic test board with some tiles for testing.
     */
    fun createBasicTestBoard(): Array<Array<Tile?>> {
        return Array(4) { row ->
            Array(4) { col ->
                when {
                    row == 0 && col == 0 -> Tile(1, Position(0, 0), origin = TileOrigin.EXISTING)
                    row == 0 && col == 1 -> Tile(3, Position(0, 1), origin = TileOrigin.EXISTING)
                    row == 1 && col == 0 -> Tile(9, Position(1, 0), origin = TileOrigin.EXISTING)
                    row == 1 && col == 1 -> Tile(27, Position(1, 1), origin = TileOrigin.EXISTING)
                    else -> null
                }
            }
        }
    }
    
    /**
     * Creates a board with merge opportunities for testing merge detection.
     */
    fun createMergeableBoard(): Array<Array<Tile?>> {
        return Array(4) { row ->
            Array(4) { col ->
                when {
                    row == 0 && col == 0 -> Tile(3, Position(0, 0), origin = TileOrigin.EXISTING)
                    row == 0 && col == 1 -> Tile(3, Position(0, 1), origin = TileOrigin.EXISTING)
                    row == 0 && col == 2 -> Tile(3, Position(0, 2), origin = TileOrigin.EXISTING)
                    row == 1 && col == 0 -> Tile(9, Position(1, 0), origin = TileOrigin.EXISTING)
                    else -> null
                }
            }
        }
    }
    
    /**
     * Creates a nearly full board for testing game over scenarios.
     */
    fun createNearlyFullBoard(): Array<Array<Tile?>> {
        return Array(4) { row ->
            Array(4) { col ->
                when {
                    row == 3 && col == 3 -> null // Only one empty space
                    else -> Tile(
                        value = if ((row + col) % 2 == 0) 1 else 3,
                        position = Position(row, col),
                        origin = TileOrigin.EXISTING
                    )
                }
            }
        }
    }
    
    /**
     * Creates an empty board for testing new game scenarios.
     */
    fun createEmptyBoard(): Array<Array<Tile?>> {
        return Array(4) { Array(4) { null } }
    }
    
    /**
     * Creates a test game state with specified parameters.
     */
    fun createTestGameState(
        board: Array<Array<Tile?>> = createBasicTestBoard(),
        score: Int = 1000,
        moves: Int = 25,
        isGameOver: Boolean = false,
        hasWon: Boolean = false
    ): GameState {
        return GameState(
            board = board,
            score = score,
            moves = moves,
            isGameOver = isGameOver,
            hasWon = hasWon
        )
    }
    
    /**
     * Creates a test UI state for testing UI components.
     * Note: This is a simplified version since GameUiState structure may vary
     */
    fun createTestUiState(
        gameState: GameState = createTestGameState(),
        isLoading: Boolean = false,
        errorMessage: String? = null,
        selectedTile: Position? = null,
        freeGamesRemaining: Int = 15,
        isPremiumUser: Boolean = false,
        canUndo: Boolean = false
    ): GameState {
        // Return the game state for now - UI state mapping can be handled in ViewModels
        return gameState
    }
    
    /**
     * Creates test game statistics.
     */
    fun createTestGameStats(
        highScore: Int = 5000,
        gamesPlayed: Int = 50,
        gamesWon: Int = 12,
        bestTile: Int = 243,
        totalMoves: Int = 1250,
        totalMerges: Int = 300
    ): GameStats {
        return GameStats(
            highScore = highScore,
            gamesPlayed = gamesPlayed,
            gamesWon = gamesWon,
            bestTile = bestTile,
            totalMoves = totalMoves,
            totalMerges = totalMerges
        )
    }
}
