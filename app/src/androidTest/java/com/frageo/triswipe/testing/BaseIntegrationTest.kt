package com.frageo.triswipe.testing

import androidx.compose.ui.test.junit4.createComposeRule
import androidx.test.ext.junit.runners.AndroidJUnit4

import com.frageo.triswipe.data.repository.GameStatisticsManager
import com.frageo.triswipe.data.repository.GameStatisticsRepository
import com.frageo.triswipe.data.models.GameStats
import com.frageo.triswipe.viewmodel.GameViewModel
import dagger.hilt.android.testing.HiltAndroidRule
import dagger.hilt.android.testing.HiltAndroidTest
import kotlinx.coroutines.runBlocking
import org.junit.Before
import org.junit.Rule
import org.junit.runner.RunWith
import javax.inject.Inject

/**
 * Base class for integration tests that provides common setup and utilities.
 * Uses Hilt for dependency injection and provides access to test utilities.
 */
@HiltAndroidTest
@RunWith(AndroidJUnit4::class)
abstract class BaseIntegrationTest {

    @get:Rule(order = 0)
    val hiltRule = HiltAndroidRule(this)

    @get:Rule(order = 1)
    val composeTestRule = createComposeRule()

    @Inject
    lateinit var statisticsManager: GameStatisticsManager
    
    @Inject
    lateinit var statisticsRepository: GameStatisticsRepository

    @Inject
    lateinit var testBillingManagerWrapper: TestBillingManagerWrapper

    // Access to test billing manager for controlling test scenarios
    protected val testBillingManager: TestBillingManagerWrapper
        get() = testBillingManagerWrapper

    @Before
    open fun setUp() {
        hiltRule.inject()

        // Reset test billing manager
        testBillingManager.resetToIdle()

        // Clear any existing data for clean test state
        runBlocking {
            clearTestData()
        }
    }

    /**
     * Clears all test data to ensure clean state for each test.
     */
    private suspend fun clearTestData() {
        // Clear statistics
        statisticsRepository.saveGameStats(GameStats())
    }

    /**
     * Sets up a test scenario with specific game state.
     */
    protected suspend fun setupTestScenario(
        isPremiumUser: Boolean = false,
        freeGamesRemaining: Int = 30,
        hasActiveGame: Boolean = false
    ) {
        // Test setup methods would go here if GameRepository existed
        // For now this is a placeholder
    }

    /**
     * Waits for UI to settle after actions.
     */
    protected fun waitForUiToSettle() {
        composeTestRule.waitForIdle()
        Thread.sleep(100) // Small additional wait for any async operations
    }

    /**
     * Performs common assertions that should be true for most screens.
     */
    protected fun assertCommonUiElements() {
        // This can be overridden by subclasses for screen-specific assertions
        waitForUiToSettle()
    }

    /**
     * Simulates a device configuration change (like rotation).
     */
    protected fun simulateConfigurationChange() {
        // This would typically involve recreating the activity
        // For now, we'll just ensure the UI settles
        waitForUiToSettle()
    }

    /**
     * Helper to run code blocks with error handling.
     */
    protected fun runTestBlock(block: () -> Unit) {
        try {
            block()
        } catch (e: Exception) {
            // Log the error and re-throw for test failure
            throw e
        }
    }

    /**
     * Verifies that no memory leaks or resource issues occurred.
     */
    protected fun verifyNoResourceLeaks() {
        // This is a placeholder for resource verification
        // In practice, you might check for proper cleanup
        waitForUiToSettle()
    }
}
