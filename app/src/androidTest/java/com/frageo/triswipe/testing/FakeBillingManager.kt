package com.frageo.triswipe.testing

import android.app.Activity
import android.content.Context
import com.android.billingclient.api.BillingClient
import com.android.billingclient.api.BillingClientStateListener
import com.android.billingclient.api.BillingResult
import com.android.billingclient.api.Purchase
import com.android.billingclient.api.PurchasesUpdatedListener
import com.frageo.triswipe.billing.BillingState
import com.frageo.triswipe.billing.PurchaseState
import com.frageo.triswipe.billing.SimpleBillingManager
import com.frageo.triswipe.data.repository.GameRepository
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * Fake implementation of SimpleBillingManager for testing.
 * Provides predictable behavior for integration tests.
 */
class FakeBillingManager(
    private val context: Context,
    private val gameRepository: GameRepository
) : PurchasesUpdatedListener, BillingClientStateListener {

    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    private val _billingState = MutableStateFlow(BillingState.CONNECTED)
    val billingState: StateFlow<BillingState> = _billingState.asStateFlow()

    private val _purchaseState = MutableStateFlow<PurchaseState>(PurchaseState.Idle)
    val purchaseState: StateFlow<PurchaseState> = _purchaseState.asStateFlow()

    // Test control properties
    var shouldSimulateError = false
    var shouldSimulatePending = false
    var purchaseDelay = 100L // Short delay for tests

    fun launchPurchaseFlow(activity: Activity) {
        _purchaseState.value = PurchaseState.Purchasing

        scope.launch {
            kotlinx.coroutines.delay(purchaseDelay)

            when {
                shouldSimulateError -> {
                    _purchaseState.value = PurchaseState.Error("Test error")
                }
                shouldSimulatePending -> {
                    _purchaseState.value = PurchaseState.Pending
                }
                else -> {
                    _purchaseState.value = PurchaseState.Success
                    setPremiumUser(true)
                }
            }
        }
    }

    fun isBillingAvailable(): Boolean = true
    fun canMakePurchases(): Boolean = true

    private fun setPremiumUser(isPremium: Boolean) {
        scope.launch {
            gameRepository.setPremiumUser(isPremium)
        }
    }

    // Test helper methods
    fun simulateConnectionError() {
        _billingState.value = BillingState.UNAVAILABLE
    }

    fun simulateConnectionSuccess() {
        _billingState.value = BillingState.CONNECTED
    }

    fun resetToIdle() {
        _purchaseState.value = PurchaseState.Idle
        shouldSimulateError = false
        shouldSimulatePending = false
    }

    // Required interface implementations
    override fun onPurchasesUpdated(billingResult: BillingResult, purchases: MutableList<Purchase>?) {
        // Test implementation - not used in fake
    }

    override fun onBillingSetupFinished(billingResult: BillingResult) {
        // Test implementation - not used in fake
    }

    override fun onBillingServiceDisconnected() {
        // Test implementation - not used in fake
    }
}
