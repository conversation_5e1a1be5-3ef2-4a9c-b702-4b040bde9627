name: Performance Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # Run performance tests daily at 2 AM UTC
    - cron: '0 2 * * *'
  workflow_dispatch:
    inputs:
      test_type:
        description: 'Type of performance test to run'
        required: true
        default: 'all'
        type: choice
        options:
          - all
          - automated
          - benchmarks
          - stress
          - regression
      upload_results:
        description: 'Upload performance results as artifacts'
        required: false
        default: true
        type: boolean

jobs:
  performance-tests:
    runs-on: ubuntu-latest
    timeout-minutes: 60
    
    strategy:
      matrix:
        api-level: [28, 33]
        test-suite: [automated, benchmarks, stress, regression]
        exclude:
          # Skip stress tests on older API levels to reduce CI time
          - api-level: 28
            test-suite: stress
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
    
    - name: Set up JDK 17
      uses: actions/setup-java@v4
      with:
        java-version: '17'
        distribution: 'temurin'
    
    - name: Cache Gradle dependencies
      uses: actions/cache@v3
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
        restore-keys: |
          ${{ runner.os }}-gradle-
    
    - name: Grant execute permission for gradlew
      run: chmod +x gradlew
    
    - name: Create performance test results directory
      run: mkdir -p performance-results
    
    - name: Run Automated Performance Tests
      if: matrix.test-suite == 'automated' || github.event.inputs.test_type == 'all' || github.event.inputs.test_type == 'automated'
      run: |
        ./gradlew test --tests "*AutomatedPerformanceTestSuite*" \
          -Pandroid.testInstrumentationRunnerArguments.class=com.frageo.triswipe.performance.AutomatedPerformanceTestSuite \
          -Pperformance.results.dir=performance-results \
          -Pperformance.api.level=${{ matrix.api-level }} \
          --continue || true
    
    - name: Run Benchmarking Tests
      if: matrix.test-suite == 'benchmarks' || github.event.inputs.test_type == 'all' || github.event.inputs.test_type == 'benchmarks'
      run: |
        ./gradlew test --tests "*BenchmarkingTools*" \
          -Pandroid.testInstrumentationRunnerArguments.class=com.frageo.triswipe.performance.BenchmarkingTools \
          -Pperformance.results.dir=performance-results \
          -Pperformance.api.level=${{ matrix.api-level }} \
          --continue || true
    
    - name: Run Stress Tests
      if: matrix.test-suite == 'stress' || github.event.inputs.test_type == 'all' || github.event.inputs.test_type == 'stress'
      run: |
        ./gradlew test --tests "*StressTestSuite*" \
          -Pandroid.testInstrumentationRunnerArguments.class=com.frageo.triswipe.performance.StressTestSuite \
          -Pperformance.results.dir=performance-results \
          -Pperformance.api.level=${{ matrix.api-level }} \
          -Pperformance.stress.iterations=1000 \
          --continue || true
    
    - name: Run Performance Regression Tests
      if: matrix.test-suite == 'regression' || github.event.inputs.test_type == 'all' || github.event.inputs.test_type == 'regression'
      run: |
        ./gradlew test --tests "*PerformanceRegressionTest*" \
          -Pandroid.testInstrumentationRunnerArguments.class=com.frageo.triswipe.performance.PerformanceRegressionTest \
          -Pperformance.results.dir=performance-results \
          -Pperformance.api.level=${{ matrix.api-level }} \
          --continue || true
    
    - name: Generate Performance Report
      if: always()
      run: |
        ./gradlew generatePerformanceReport \
          -Pperformance.results.dir=performance-results \
          -Pperformance.api.level=${{ matrix.api-level }} \
          -Pperformance.test.suite=${{ matrix.test-suite }} \
          || echo "Performance report generation failed"
    
    - name: Upload Performance Test Results
      if: always() && (github.event.inputs.upload_results == 'true' || github.event.inputs.upload_results == '')
      uses: actions/upload-artifact@v3
      with:
        name: performance-results-api${{ matrix.api-level }}-${{ matrix.test-suite }}
        path: |
          performance-results/
          app/build/reports/tests/
          app/build/test-results/
        retention-days: 30
    
    - name: Comment Performance Results on PR
      if: github.event_name == 'pull_request' && always()
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');
          const path = require('path');
          
          // Read performance results
          const resultsDir = 'performance-results';
          let performanceComment = '## Performance Test Results\n\n';
          performanceComment += `**API Level**: ${{ matrix.api-level }}\n`;
          performanceComment += `**Test Suite**: ${{ matrix.test-suite }}\n\n`;
          
          try {
            if (fs.existsSync(resultsDir)) {
              const files = fs.readdirSync(resultsDir);
              for (const file of files) {
                if (file.endsWith('.json')) {
                  const filePath = path.join(resultsDir, file);
                  const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
                  performanceComment += `### ${file.replace('.json', '')}\n`;
                  performanceComment += `- **Average Response Time**: ${data.averageResponseTime || 'N/A'} ms\n`;
                  performanceComment += `- **Success Rate**: ${data.successRate || 'N/A'}%\n`;
                  performanceComment += `- **Memory Usage**: ${data.memoryUsage || 'N/A'} MB\n\n`;
                }
              }
            } else {
              performanceComment += '*No performance results found*\n';
            }
          } catch (error) {
            performanceComment += `*Error reading performance results: ${error.message}*\n`;
          }
          
          // Find existing performance comment
          const { data: comments } = await github.rest.issues.listComments({
            owner: context.repo.owner,
            repo: context.repo.repo,
            issue_number: context.issue.number,
          });
          
          const existingComment = comments.find(comment => 
            comment.body.includes('## Performance Test Results') && 
            comment.body.includes(`**API Level**: ${{ matrix.api-level }}`) &&
            comment.body.includes(`**Test Suite**: ${{ matrix.test-suite }}`)
          );
          
          if (existingComment) {
            await github.rest.issues.updateComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              comment_id: existingComment.id,
              body: performanceComment
            });
          } else {
            await github.rest.issues.createComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number,
              body: performanceComment
            });
          }

  performance-analysis:
    needs: performance-tests
    runs-on: ubuntu-latest
    if: always()
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up JDK 17
      uses: actions/setup-java@v4
      with:
        java-version: '17'
        distribution: 'temurin'
    
    - name: Download all performance artifacts
      uses: actions/download-artifact@v3
      with:
        path: all-performance-results
    
    - name: Analyze Performance Trends
      run: |
        ./gradlew analyzePerformanceTrends \
          -Pperformance.results.dir=all-performance-results \
          -Pperformance.baseline.branch=main \
          || echo "Performance trend analysis failed"
    
    - name: Check Performance Regression
      id: regression-check
      run: |
        ./gradlew checkPerformanceRegression \
          -Pperformance.results.dir=all-performance-results \
          -Pperformance.regression.threshold=20 \
          || echo "regression_detected=true" >> $GITHUB_OUTPUT
    
    - name: Upload Consolidated Performance Report
      if: always()
      uses: actions/upload-artifact@v3
      with:
        name: consolidated-performance-report
        path: |
          all-performance-results/
          performance-analysis/
        retention-days: 90
    
    - name: Fail if Performance Regression Detected
      if: steps.regression-check.outputs.regression_detected == 'true'
      run: |
        echo "Performance regression detected! Check the performance report for details."
        exit 1

  performance-dashboard:
    needs: performance-analysis
    runs-on: ubuntu-latest
    if: always() && github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
    
    - name: Download consolidated performance results
      uses: actions/download-artifact@v3
      with:
        name: consolidated-performance-report
        path: performance-dashboard/data
    
    - name: Generate Performance Dashboard
      run: |
        cd performance-dashboard
        npm install
        npm run build
        npm run generate-dashboard
    
    - name: Deploy to GitHub Pages
      uses: peaceiris/actions-gh-pages@v3
      if: github.ref == 'refs/heads/main'
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./performance-dashboard/dist
        destination_dir: performance