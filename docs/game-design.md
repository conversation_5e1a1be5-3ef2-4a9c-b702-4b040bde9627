# TriSwipe Game Design

## Core Mechanics Overview

**Core Concept**: Players combine 3 identical tiles to create higher-value tiles using a hybrid input system that combines swipe gestures (like 2048) with tap-to-swap mechanics (like match-3 games).

## Input System

### 1. Swipe Gestures (2048-style)
- **Up/Down/Left/Right swipes** move ALL tiles in that direction
- Tiles slide until they hit the board edge or another tile
- Creates opportunities for alignment and strategic positioning
- Used for board repositioning and setting up merge opportunities

### 2. Tap-to-Swap (Match-3 style)
- **Tap adjacent tiles** to swap their positions
- Only works with tiles that are directly adjacent (horizontal/vertical)
- Provides precise control for final merge alignment
- Used for tactical triple creation

### 3. Auto-Merge System
- When 3+ identical tiles align (horizontal or vertical), they **automatically merge**
- Creates a single tile with the next value in progression
- Triggers immediately after any player action
- Multiple merges can happen simultaneously

## Tile Value Progression
```
1 + 1 + 1 = 3
3 + 3 + 3 = 9
9 + 9 + 9 = 27
27 + 27 + 27 = 81
81 + 81 + 81 = 243
243 + 243 + 243 = 729
729 + 729 + 729 = 2187
etc.
```

## Strategic Depth

### Swipe Strategy
- **Board control**: Position tiles for future merges
- **Cascade setups**: Create multiple merge opportunities
- **Space management**: Prevent board from filling up
- **Emergency moves**: Quick repositioning when stuck

### Swap Strategy
- **Precision alignment**: Fine-tune triple formations
- **Combo creation**: Set up chain reactions
- **Blocking prevention**: Avoid unwanted merges
- **Last-mile optimization**: Perfect final positioning

## Game Flow

1. **Board starts** with 2-3 random tiles (values 1 or 3)
2. **Player makes move** (swipe or tap-swap)
3. **Tiles move/swap** according to input
4. **Auto-merge triggers** if 3+ tiles align
5. **New tile spawns** in random empty position
6. **Check for game over** (no valid moves)
7. **Repeat** until game ends

## Scoring System

### Base Scoring
- **Merge value**: Points equal to the sum of merged tiles
- **Triple merge**: 1+1+1=3 gives 3 points
- **Higher merges**: 27+27+27=81 gives 81 points

### Bonus Scoring
- **Combo multiplier**: Multiple merges in one turn multiply score
- **Efficiency bonus**: Fewer moves to achieve merges
- **Speed bonus**: Quick successive merges

## Win/Loss Conditions

### Game Over
- **No valid swipes**: All swipe directions are blocked
- **No valid swaps**: No adjacent tiles can be swapped
- **Board full**: No empty spaces for new tiles

### High Score Goals
- **Reach specific tile values**: 243, 729, 2187, etc.
- **Achieve score milestones**: 1000, 5000, 10000 points
- **Efficiency challenges**: High scores with fewer moves

## Unique Selling Points

### Strategic Depth
- **Dual input system** creates more strategic options than single-input games
- **Hybrid planning** requires both broad strategy (swipes) and tactical precision (swaps)
- **Multi-layered thinking** appeals to both casual and hardcore puzzle players

### Accessibility
- **Progressive learning**: Start with swipes, master swaps
- **Visual feedback**: Clear animations show both input types
- **Forgiving gameplay**: Multiple ways to achieve the same goal

### Market Differentiation
- **Unique mechanics**: No other game combines 2048-style swipes with match-3 swaps
- **Familiar yet fresh**: Uses known mechanics in new combination
- **Broad appeal**: Attracts fans of both 2048 and match-3 games

## Implementation Notes

### Input Handling Priority
1. **Swipe detection** takes precedence over taps
2. **Tap-swap validation** ensures tiles are adjacent
3. **Gesture conflicts** resolved by timing and distance
4. **Touch feedback** provides clear response to both inputs

### Animation Considerations
- **Swipe animations**: Smooth tile sliding with physics
- **Swap animations**: Quick tile exchange with arc motion
- **Merge animations**: Satisfying combination effects with warp animations
- **Spawn animations**: New tile appearance with emphasis

### Advanced Features

#### Command Pattern Implementation
- **Undo/Redo System**: 50-command history with macro support
- **GameCommand Interface**: All game actions are commands
- **SwipeCommand & TileSwapCommand**: Input-specific implementations
- **MacroCommand**: Complex multi-step operations

#### Merge Warp Animations
- **Floating-point Interpolation**: Smooth tile movement calculation
- **Two-phase Animation**: Proper board state management
- **Center Position Calculation**: Accurate merge point detection
- **Scaling and Fade Effects**: Visual enhancement for merges

#### Premium Features
- **Undo Moves**: 10-move history for premium users
- **Tile Themes**: 6 different themes (Classic, Ocean, Forest, Sunset, Neon, Monochrome)
- **Adaptive Spawning**: Smart tile spawning based on progress (planned)

### Tutorial Strategy
- **Phase 1**: Introduce swipe mechanics only
- **Phase 2**: Add tap-swap when player reaches first merge
- **Phase 3**: Show advanced combinations of both inputs
- **Phase 4**: Challenge levels requiring both mechanics

## Technical Architecture

### Core Components
- **GameEngine**: Central game logic coordinator
- **GameBoard**: 4x4 grid state management
- **MergeDetector**: Advanced merge detection with flood-fill algorithm
- **MergeExecutor**: Tile merging, scoring, and animation creation
- **CommandManager**: Undo/redo system with command history

### Data Persistence
- **Room Database**: Local SQLite for game state and statistics
- **SharedPreferences**: User settings and monetization data
- **Auto-save/Load**: Seamless game continuation
- **Statistics Tracking**: Comprehensive gameplay analytics

### Monetization
- **Freemium Model**: 30 free games, $1.99 premium upgrade
- **Google Play Billing**: Production-ready integration
- **Premium Features**: Undo, themes, unlimited games
- **Fair Value**: Substantial free content with clear premium benefits

This hybrid approach creates a unique gameplay experience that stands out in the crowded puzzle game market while remaining intuitive and accessible to new players.